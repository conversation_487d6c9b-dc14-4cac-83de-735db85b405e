{"name": "votesaveamerica", "version": "1.2.81", "author": "BlueState for VSA", "license": "ISC", "description": "A theme for Vote Save America's 2024 work", "themename": "Vote Save America 2024 WordPress Theme", "themeuri": "http://www.votesaveamerica.com", "authoruri": "http://www.bluestate.co", "main": "src/js/app.js", "directories": {"test": "tests"}, "paths": {"src": {"icons": "src/icons/", "img": "src/images/", "fonts": "src/fonts/", "js": "src/js/", "scss": "src/scss/"}, "wp": {"img": "images/", "fonts": "fonts/", "js": "js/"}}, "babel": {"presets": ["@babel/preset-env"], "plugins": ["@babel/plugin-proposal-class-properties"]}, "scripts": {"build": "npm run lint && webpack --mode=production --config webpack.config.js && npm run theme && npm run optimize:images", "start": "npm run lint && webpack-dev-server --mode=development --config webpack.config.js && npm run theme && npm run optimize:images", "lint": "npm run lint:scss && npm run pretty", "lint:fix": "npm run lint:js:fix && npm run lint:scss:fix && npm run pretty", "lint:php": "vendor/bin/phpcs **/*.php ./*.php --ignore=node_modules,vendor,tests --standard=WordPress -d error_reporting='E_ALL&~E_DEPRECATED'", "lint:php:fix": "vendor/bin/phpcbf **/*.php ./*.php --ignore=node_modules,vendor,tests --standard=WordPress -d error_reporting='E_ALL&~E_DEPRECATED'", "lint:scss": "stylelint '**/*.scss' --config '.stylelintrc.js'", "lint:scss:fix": "stylelint '**/*.scss' --config '.stylelintrc.js' --fix", "lint:js": "eslint --config .eslintrc.json --ext .js src/", "lint:js:fix": "eslint --config .eslintrc.json --fix --ext .js src/", "lint:check": "eslint-config-prettier index.js", "generate:webp": "node ./scripts/produceWebP.js", "compress:icons": "node ./scripts/compressIcons.js", "compress:images": "node ./scripts/compressImages.js", "optimize:images": "npm run compress:icons && npm run compress:images && npm run generate:webp", "pretty": "prettier --write .", "patch": "npm version patch", "minor": "npm version minor", "major": "npm version major", "theme": "cp ./dist/css/style.css ./style.css && replace-in-file /../assets/g ./dist/assets style.css --isRegex --quiet && cp ./dist/css/style.css.map ./style.css.map && replace-in-file css/style.css /style.css style.css.map --quiet"}, "husky": {"hooks": {"pre-commit": "npm run build && git add ."}}, "browserslist": ["last 2 versions", "> 2%", "not ie <= 11", "not ie_mob <= 11"], "engines": {"node": "19.6.0"}, "devDependencies": {"@babel/eslint-parser": "^7.21.8", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/preset-env": "^7.21.5", "@wordpress/babel-preset-default": "^7.31.0", "@wordpress/dependency-extraction-webpack-plugin": "^4.30.0", "babel-loader": "^9.1.2", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "css-loader": "^6.7.3", "css-minimizer-webpack-plugin": "^5.0.1", "eslint": "^8.40.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.8.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-jsdoc": "^46.2.6", "eslint-plugin-prettier": "^4.2.1", "eslint-webpack-plugin": "^4.0.1", "husky": "^8.0.3", "image-minimizer-webpack-plugin": "^3.8.2", "imagemin": "^8.0.1", "imagemin-gifsicle": "^7.0.0", "imagemin-jpegtran": "^7.0.0", "imagemin-optipng": "^8.0.0", "imagemin-svgo": "^10.0.1", "imagemin-webp": "^8.0.0", "js-yaml": "^4.1.0", "lint-staged": "^13.2.2", "mini-css-extract-plugin": "^2.7.5", "node-sass": "^8.0.0", "phplint": "^2.0.5", "postcss-loader": "^7.3.0", "postcss-preset-env": "^8.3.2", "prettier": "^2.8.8", "replace-in-file": "^7.0.1", "sass": "^1.62.1", "sass-loader": "^13.3.1", "string-replace-loader": "^3.1.0", "stylelint": "^15.7.0", "stylelint-config-prettier-scss": "^1.0.0", "stylelint-config-standard-scss": "^9.0.0", "stylelint-prettier": "^3.0.0", "stylelint-selector-bem-pattern": "^2.1.1", "stylelint-webpack-plugin": "^4.1.1", "svg-sprite-loader": "^2.0.3", "svg-spritemap-webpack-plugin": "^4.5.0", "svg4everybody": "^2.1.9", "svgo-loader": "^4.0.0", "terser-webpack-plugin": "^5.3.9", "url-loader": "^4.1.1", "webpack": "5.84.1", "webpack-cli": "^5.1.1", "webpack-dev-server": "^4.15.0"}, "dependencies": {"@babel/polyfill": "^7.12.1", "@dotlottie/player-component": "^2.7.2", "@wordpress/icons": "^9.38.0", "babel-loader": "^9.1.2", "basicscroll": "^3.0.4", "d3-geo": "^3.1.1", "d3-selection": "^3.0.0", "dompurify": "^3.2.3", "focus-trap": "^7.5.4", "fs": "0.0.1-security", "fsevents": "^2.3.2", "phone": "^3.1.41", "postcode-validator": "^3.8.20", "swiper": "^11.0.5", "topojson-client": "^3.1.0"}}