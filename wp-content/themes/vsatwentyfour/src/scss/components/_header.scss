@use 'sass:map';

/**
  HEADER COMPONENT
**/

.header {
  background: $white;
  border-bottom: 3px solid $electoral-blue;
  height: 93px;
  padding: px-to-rem(15px) px-to-rem($mobile-gutter);
  position: sticky;
  transition: padding 0.3s ease-in-out;
  top: 0;
  z-index: 5;

  $self: &;

  &__content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  &__logo {
    height: px-to-rem(60px);
    position: relative;
    width: px-to-rem(72px);
    z-index: 3;

    &-link {
      display: block;
      height: 100%;
    }

    svg {
      height: px-to-rem(60px);
      transition: all 0.5s ease-in-out;
      transform: scale(1);
      width: px-to-rem(72px);
    }

    .check {
      fill: $patriotic-red;
      transition: fill 0.5s ease-in-out;
    }

    .backdrop {
      fill: $electoral-blue;
      transition: fill 0.5s ease-in-out;
    }

    .letter {
      fill: $white;
      transition: fill 0.5s ease-in-out;
    }
  }

  &__cta {
    @include button;

    padding-bottom: 14px;
    padding-top: 14px;
    position: relative;
    min-height: 60px;

    &::after {
      content: attr(data-label);
      font-style: italic;
      visibility: hidden;
      height: 0;
      overflow: hidden;
      display: block;
    }
  }

  &__nav-cta-item {
    margin: px-to-rem(28px) px-to-rem($mobile-gutter) px-to-rem(56px);

    #{$self}__cta {
      width: 100%;
    }
  }

  &__cta-item {
    margin-left: auto;
    margin-right: 21px;
    order: 1;
  }

  .hamburger {
    path {
      transition: all 0.3s ease;
      transform-origin: center;
    }
  }

  .nav {
    margin-right: 0;
    margin-left: 0;

    &--hamburger {
      margin-left: 0;
      order: 2;
    }

    &__hamburger {
      color: $crt-black;
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: $obviously-font;
      font-weight: 700;
      line-height: 1;
      margin-right: -9px;
      padding: px-to-rem(18px) px-to-rem(9px);
      transition: all 0.5s ease-in-out;
      z-index: 2;
    }

    &__topwrap {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
    }

    &__hamburger-label {
      @include is-visually-hidden;
    }

    &__item {
      border-bottom: 1px solid $grey;
      color: $crt-black;
      display: block;
      font-family: $inter-font;
      font-size: px-to-rem(18px);
      font-weight: 800;
      line-height: 140%;
      position: relative;

      &:last-of-type {
        margin-bottom: 0;
      }

      &--sub {
        border-bottom: 0;
        overflow: hidden;
        padding: 0;

        &:first-of-type {
          .nav__link--sub {
            padding-top: 0;
          }
        }

        &:last-of-type {
          .nav__link--sub {
            padding-bottom: px-to-rem(28px);
          }
        }
      }
    }

    &__expand {
      cursor: default;
      padding: 0;
      z-index: 1;

      svg {
        display: none;
        color: $crt-black;
        transition: all 0.3s ease-in-out;
      }
    }

    &__subwrap {
      display: grid;
      grid-template-rows: 1fr;
      transition: grid-template-rows 0.2s ease-in-out;

      &--open {
        grid-template-rows: 1fr;
        transition: grid-template-rows 0.2s ease-in-out;
      }
    }

    &__subset {
      width: 100%;
    }

    &__link {
      display: block;
      color: $crt-black;
      font-style: normal;
      padding: px-to-rem(28px) px-to-rem($mobile-gutter);
      position: relative;
      transition: all 0.5s cubic-bezier(0.3333, 0.6667, 0.6667, 1);
      width: 100%;

      .nav__text {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        svg {
          display: block;
          transition: transform 0.3s ease-in;
        }
      }

      &--sub {
        color: $crt-black;
        font-weight: 400;
        padding: px-to-rem(14px) px-to-rem($mobile-gutter);
      }

      &:hover {
        color: $electoral-blue;

        .nav__text {
          svg {
            transform: translateX(5px);
          }
        }
      }

      &--button {
        &:hover {
          color: $crt-black;
        }
      }
    }

    &__set-wrap {
      display: grid;
      grid-template-rows: 0fr;
      transition: visibility 0s linear 0.5s, grid-template-rows 0.5s ease-in;
      filter: drop-shadow(6px 6px 3px rgb(50 50 0 / 50%));
      left: 0;
      position: absolute;
      right: 0;
      top: 93px;
      width: 100%;
      visibility: hidden;
    }

    &__set {
      background-color: $white;
      max-height: calc(100vh - 93px);
      padding: 0;
      overflow: auto;
      transition: transform 0.5s ease-in, width 0.1s linear,
        overflow 0s linear 0.5s;
      transform-origin: 100% 0%;
      width: 100%;
    }
  }

  &--open {
    #{$self} {
      &__cta-item {
        display: none;
      }

      &__logo {
        svg {
          transform: scale(1.1) translate(0, 0);
        }
      }
    }

    .hamburger {
      path {
        &.top {
          transform: translate(-4px, 7px) rotate(45deg);
        }

        &.middle {
          opacity: 0;
        }

        &.bottom {
          transform: translate(-4px, -6px) rotate(-45deg);
        }
      }
    }

    .nav {
      &--hamburger {
        margin-right: 0;
      }

      &__set-wrap {
        transition: visibility 0s linear 0s, grid-template-rows 0.5s ease-in;
        visibility: visible;
        grid-template-rows: 1fr;
      }
    }
  }

  @include breakpoint(nav-tablet, min) {
    .nav {
      &__set-wrap {
        filter: drop-shadow(-6px 6px 3px rgb(50 50 0 / 50%));
        width: 100%;
      }

      &__set {
        padding: 0;
        max-width: 100vw;
        max-height: calc(100vh - 93px);
      }

      &__hamburger {
        font-size: 18px;
        margin-right: px-to-rem(-4px);
        margin-left: 0;
        position: relative;
        transition: all 0.3s ease-out;

        svg {
          height: 22px;
          width: 24px;
        }
      }
    }

    &__nav-cta-item {
      display: none;
    }

    &--open {
      #{$self}__cta-item {
        display: block;
      }

      #{$self}__nav-cta-item {
        display: none;
      }
    }
  }

  @include breakpoint(tablet, min) {
    padding: 15px $tablet-gutter;

    &__subset {
      margin-bottom: 0;
    }

    .nav {
      &__subset {
        margin-bottom: 0;
      }

      &__set {
        margin-bottom: 0;
      }

      &__link {
        padding-left: px-to-rem($tablet-gutter);
        padding-right: px-to-rem($tablet-gutter);
      }
    }
  }

  @include breakpoint(desktop, min) {
    &__subset {
      margin-bottom: 0;
    }

    &__content {
      max-width: $max-wide-width;
      margin-left: auto;
      margin-right: auto;
    }

    .nav {
      &__subset {
        margin-bottom: 0;
      }

      &__set {
        margin-bottom: 0;
      }
    }
  }

  @include breakpoint(l-desktop, min) {
    height: fit-content;

    &__content {
      flex-direction: row;
      justify-content: center;
      align-items: center;
    }

    &__logo {
      align-self: flex-end;
      transition: all 0.5s ease-in-out;

      &-link {
        transform: scale(1.7) translate(15px, 15px);
        transition: all 0.5s ease-in-out;
      }

      svg,
      svg.open {
        transform: scale(1) translate(0);
      }
    }

    &__cta-item {
      display: none;
    }

    &__nav-cta-item {
      display: block;
      align-self: center;
      margin: 0 0 0 px-to-rem(25px);
    }

    .nav {
      &--hamburger {
        margin-left: auto;
        margin-right: 0;
        max-width: 100%;
        padding-left: px-to-rem(60px);
      }

      &__hamburger {
        display: none;
      }

      &__item {
        align-items: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        border-bottom: none;

        &--sub {
          .nav__link--sub {
            padding-bottom: px-to-rem(14px);
            padding-top: px-to-rem(14px);
          }

          &:first-of-type {
            .nav__link--sub {
              padding-top: px-to-rem(26px);
            }
          }

          &:last-of-type {
            .nav__link--sub {
              padding-bottom: px-to-rem(26px);
            }
          }
        }
      }

      &__link {
        padding: px-to-rem(23px) px-to-rem(25px);
        transition: color 0.3s ease-in-out;

        &--button {
          padding-right: px-to-rem(6px);
        }

        &--sub {
          font-weight: 800;
        }

        .nav__text {
          svg {
            display: none;
          }
        }

        &:hover {
          color: $electoral-blue;
        }
      }

      &__expand {
        cursor: pointer;
        display: flex;
        align-items: center;
        padding-right: px-to-rem(25px);

        svg {
          transform-origin: center center;
          transform: rotate(0deg);
          display: block;
        }

        &:hover {
          svg {
            color: $electoral-blue;
          }
        }

        &--open {
          svg {
            color: $electoral-blue;
            transform: rotate(-180deg);
          }

          .nav__link {
            color: $electoral-blue;
          }
        }
      }

      &__set-wrap {
        filter: none;
        inset: unset;
        transform: none;
        transition: none;
        position: relative;
        visibility: visible;
      }

      &__set {
        display: flex;
        flex-flow: row nowrap;
        align-items: stretch;
        justify-content: center;
        max-height: 100%;
        max-width: 100%;
        overflow: visible;
        transform: none;
        transition: none;
      }

      &__subwrap {
        display: grid;
        grid-template-rows: 0fr;
        transition: grid-template-rows 0.3s ease-in;

        &--open {
          grid-template-rows: 1fr;
          transition: grid-template-rows 0.3s ease-in;
        }
      }

      &__subset {
        background-color: $white;
        border-top: 6px solid $electoral-blue;
        transform: scale(1, 0) translate(-50%, 0%);
        transform-origin: center top;
        visibility: hidden;
        margin-bottom: 0;
        opacity: 0;
        overflow: hidden;
        position: absolute;
        transition: visibility 0s linear 0.3s, opacity 0s linear 0.3s,
          transform 0.3s ease-in 0s, width 0.1s linear 0s;
        top: calc(100% + 12px);
        min-width: 180px;
        max-width: 220px;
        width: max-content;

        &--open {
          filter: drop-shadow(-6px 6px 6px rgb(50 50 0 / 25%));
          grid-template-rows: 1fr;
          transition: visibility 0s linear, opacity 0s linear,
            transform 0.3s ease-in 0.1s, width 0.1s linear 0s;
          opacity: 1;
          transform: scale(1, 1) translate(-50%, 0%);
          visibility: visible;
        }
      }
    }

    &--open {
      .header {
        &__cta-item {
          display: none;
        }

        &__nav-cta-item {
          display: block;
        }
      }

      .nav {
        &__set-wrap {
          transform: none;
          transition: none;
        }

        &__set {
          transform: none;
          transition: none;
        }
      }
    }

    &--pinned {
      padding-top: 5px;
      padding-bottom: 5px;

      #{$self}__logo {
        align-self: center;

        &-link,
        svg,
        svg.open {
          transform: scale(1) translate(0, 0);
        }
      }

      .nav {
        &__hamburger {
          transform: none;
        }

        &__subset {
          transition: visibility 0s linear 0.3s, opacity 0s linear 0.3s,
            transform 0.3s ease-in 0s, width 0.1s linear 0s, top 0.3s ease-in 0s;
          top: calc(100% + 2px);

          &--open {
            transition: visibility 0s linear, opacity 0s linear,
              transform 0.3s ease-in 0.1s, width 0.1s linear 0s,
              top 0.3s ease-in 0s;
          }
        }
      }
    }
  }
}

.disable-animation {
  .header {
    .nav__set-wrap {
      transition: none;
    }
  }
}

.admin-bar {
  .header {
    .nav {
      &__set {
        top: 60px;
      }
    }

    @include breakpoint(tablet, min) {
      .nav {
        &__set {
          top: 110px;
        }
      }
    }
  }
}
