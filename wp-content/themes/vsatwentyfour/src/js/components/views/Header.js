import { <PERSON>, CLASS_NAMES, EVENTS, KEY_CODES } from '@bs/constants';
import { MessageBus } from '@bs/utils';

/**
 * A class for the Header module
 */
export default class Header extends MessageBus {
  /**
   * Constructor function of the HeaderFunc class. Must receive one arguments, a DOM
   * node representing a delivery ticket.
   * @param {HTMLElement} element - REQUIRED - the module's container
   * @param Services
   */
  constructor(element, Services) {
    super();
    /**
     * DOM node that is passed into the constructor
     * @property {object} element DOM node that is passed into the constructor
     */
    this.element = element;
    this.ResizeService = Services.ResizeService;

    // Initialize the view
    this.init();
  }

  /**
   * Initializes the view by calling the functions to
   * create DOM references, setup event handlers and
   * then create the event listeners
   * @returns {object} HeaderFunc A reference to the current instance of the class
   * @chainable
   */
  init() {
    this.cacheDomReferences().setupHandlers().enable();

    return this;
  }

  /**
   * Cache DOM References
   *
   * Find all necessary DOM elements used in the view and cache them
   * @returns {object} HeaderFunc A reference to the current instance of the class
   * @chainable
   */
  cacheDomReferences() {
    this.menu = this.element.querySelector('.nav__set-wrap');
    this.menuItems = this.element.querySelector('.nav__set');
    this.navLinks = this.element.querySelectorAll('.nav__link');
    this.hamburger = this.element.querySelector('.nav__hamburger');
    this.logo = this.element.querySelector('.header__logo svg');
    this.close = this.element.querySelector('.nav__close');
    this.expand = this.element.querySelectorAll('.nav__expand');
    this.overlay = document.querySelector('.overlay');
    this.sentinel = document.createElement('div');
    this.sentinel.style.position = 'absolute';
    this.sentinel.style.top = '0';
    this.sentinel.classList.add('header__sentinel');
    this.element.parentNode.insertBefore(this.sentinel, this.element);
    let resizeTimer;
    this.resizeTimer = resizeTimer;

    return this;
  }

  /**
   * Bind event handlers with the proper context of `this`.
   * @returns {object} A reference to the current instance of the class
   * @chainable
   */
  setupHandlers() {
    this.toggleHamburgerHandler = this.toggleHamburgerHandler.bind(this);
    this.toggleSubnav = this.toggleSubnav.bind(this);
    this.handleMediaResize = this.handleMediaResize.bind(this);
    this.escapeListener = this.escapeListener.bind(this);
    this.outsideClickListener = this.outsideClickListener.bind(this);
    this.handleResize = this.handleResize.bind(this);
    this.mediaQuery = window.matchMedia('(max-width: 1199px)');

    return this;
  }

  /**
   * Create event handlers to enable interaction with view
   * @returns {object} Search A reference to the current instance of the class
   * @chainable
   */
  enable() {
    this.mediaQuery.addEventListener('change', this.handleMediaResize);
    this.ResizeService.addCallback(this.handleResize);
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.intersectionRatio === 0) {
          this.element.classList.add(CLASS_NAMES.HEADER_PINNED);
        } else {
          this.element.classList.remove(CLASS_NAMES.HEADER_PINNED);
        }
      },
      { threshold: [0] }
    );
    observer.observe(this.sentinel);

    if (window.innerWidth >= 1200) {
      this.hamburger?.setAttribute(ARIA.EXPANDED, true);
      this.menu?.classList.add('nav__set-wrap--open');
      this.menuItems?.classList.add('nav__set--open');
      this.element?.classList.add('header--open');
      this.logo?.classList.add(CLASS_NAMES.OPEN);
    } else if (this.element?.classList.contains('header--open')) {
      this.hamburger?.setAttribute(ARIA.EXPANDED, false);
      this.menu?.classList.remove('nav__set-wrap--open');
      this.menuItems?.classList.remove('nav__set--open');
      this.element?.classList.remove('header--open');
      document.body?.classList.remove(CLASS_NAMES.NAV_OPEN);
      this.logo?.classList.remove(CLASS_NAMES.OPEN);
    }

    if (this.hamburger !== null) {
      this.hamburger.addEventListener(
        EVENTS.CLICK,
        this.toggleHamburgerHandler
      );
    }
    if (this.close !== null) {
      this.close.addEventListener(EVENTS.CLICK, this.toggleHamburgerHandler);
    }
    if (this.expand !== null) {
      [...this.expand].forEach((button) => {
        button.addEventListener(EVENTS.CLICK, () => this.toggleSubnav(button));
        button.addEventListener(EVENTS.MOUSEOVER, () => this.toggleSubnav(button));
        button.addEventListener(EVENTS.MOUSEOUT, () => this.toggleSubnav(button));
      });
    }

    document.addEventListener(EVENTS.KEY_DOWN, this.escapeListener);
    document.addEventListener(EVENTS.CLICK, this.outsideClickListener);

    return this;
  }

  escapeListener(event) {
    const expanded = this.hamburger.getAttribute(ARIA.EXPANDED) === 'true';
    if (event.keyCode === KEY_CODES.ESCAPE && expanded) {
      this.toggleHamburgerHandler();
    }
  }

  outsideClickListener(event) {
    const expanded = this.hamburger.getAttribute(ARIA.EXPANDED) === 'true';
    const headerOuterClicked = event.target === this.overlay;

    if (headerOuterClicked && expanded) {
      this.toggleHamburgerHandler();
    }
  }

  openSubnav(button) {
    [...this.expand].forEach((expandButton) => {
      if (button !== expandButton) {
        const selector = expandButton.dataset.subnav;
        const subnav = this.element.querySelector(`#${selector}`);
        const subwrap = subnav.parentElement;
        subnav.classList.remove('nav__subset--open');
        subwrap.classList.remove('nav__subwrap--open');
        expandButton.setAttribute(ARIA.EXPANDED, false);
        expandButton.classList.remove('nav__expand--open');
      } else {
        const selector = button.dataset.subnav;
        const subnav = this.element.querySelector(`#${selector}`);
        const subwrap = subnav.parentElement;
        subnav.classList.add('nav__subset--open');
        subwrap.classList.add('nav__subwrap--open');
        button.setAttribute(ARIA.EXPANDED, true);
        button.classList.add('nav__expand--open');
      }
    });

    if(timer) {
      clearTimeout(timer);
    }

    return this;
  }
  
  closeSubnav(button) {
    [...this.expand].forEach((expandButton) => {
      if (button !== expandButton) {
        const selector = expandButton.dataset.subnav;
        const subnav = this.element.querySelector(`#${selector}`);
        const subwrap = subnav.parentElement;
        subnav.classList.remove('nav__subset--open');
        subwrap.classList.remove('nav__subwrap--open');
        expandButton.setAttribute(ARIA.EXPANDED, false);
        expandButton.classList.remove('nav__expand--open');
      } else {
        const selector = button.dataset.subnav;
        const subnav = this.element.querySelector(`#${selector}`);
        const subwrap = subnav.parentElement;
        subnav.classList.remove('nav__subset--open');
        subwrap.classList.remove('nav__subwrap--open');
        button.setAttribute(ARIA.EXPANDED, false);
        button.classList.remove('nav__expand--open');
        timer = setTimeout(function(event){
          document.querySelector(".has-submenu.open").className = "has-submenu";
        }, 1000);
      }
    });

    return this;
  }

  toggleSubnav(button) {
    [...this.expand].forEach((expandButton) => {
      if (button !== expandButton) {
        const selector = expandButton.dataset.subnav;
        const subnav = this.element.querySelector(`#${selector}`);
        const subwrap = subnav.parentElement;
        subnav.classList.remove('nav__subset--open');
        subwrap.classList.remove('nav__subwrap--open');
        expandButton.setAttribute(ARIA.EXPANDED, false);
        expandButton.classList.remove('nav__expand--open');
      } else {
        const selector = button.dataset.subnav;
        const subnav = this.element.querySelector(`#${selector}`);
        const subwrap = subnav.parentElement;
        subnav.classList.toggle('nav__subset--open');
        subwrap.classList.toggle('nav__subwrap--open');
        const expanded =
          button.getAttribute(ARIA.EXPANDED) === 'true' ? 'false' : 'true';
        button.setAttribute(ARIA.EXPANDED, expanded);
        button.classList.toggle('nav__expand--open');
      }
    });

    return this;
  }

  /**
   * This events trigger for toggling hamburger menu.
   * @returns {void}
   */
  toggleHamburgerHandler() {
    const expanded =
      this.hamburger.getAttribute(ARIA.EXPANDED) === 'true' ? 'false' : 'true';
    this.hamburger.setAttribute(ARIA.EXPANDED, expanded);
    this.menu.classList.toggle('nav__set-wrap--open');
    this.menuItems.classList.toggle('nav__set--open');
    this.element.classList.toggle('header--open');
    document.body.classList.toggle(CLASS_NAMES.NAV_OPEN);
    this.logo.classList.toggle(CLASS_NAMES.OPEN);

    return this;
  }

  /**
   * Resize event handler to track if window size.
   * @param e
   * @return {object} A reference to the header class.
   */
  handleMediaResize(e) {
    document.body.classList.remove(CLASS_NAMES.NAV_OPEN);

    if (!e.matches && !this.element.classList.contains('header--open')) {
      this.hamburger.setAttribute(ARIA.EXPANDED, true);
      this.menu.classList.add('nav__set-wrap--open');
      this.menuItems.classList.add('nav__set--open');
      this.element.classList.add('header--open');
      this.logo.classList.add(CLASS_NAMES.OPEN);
    } else if (this.element.classList.contains('header--open')) {
      this.hamburger.setAttribute(ARIA.EXPANDED, false);
      this.menu.classList.remove('nav__set-wrap--open');
      this.menuItems.classList.remove('nav__set--open');
      this.element.classList.remove('header--open');
      document.body.classList.remove(CLASS_NAMES.NAV_OPEN);
      this.logo.classList.remove(CLASS_NAMES.OPEN);
    }
  }

  handleResize() {
    document.body.classList.add('disable-animation');

    clearTimeout(this.resizeTimer);
    this.resizeTimer = setTimeout(() => {
      document.body.classList.remove('disable-animation');
    }, 200);
  }
}
