!function(){var t,e,i={893:function(t){t.exports=function t(e,i,s){function n(o,a){if(!i[o]){if(!e[o]){if(r)return r(o,!0);var l=new Error("Cannot find module '"+o+"'");throw l.code="MODULE_NOT_FOUND",l}var d=i[o]={exports:{}};e[o][0].call(d.exports,(function(t){return n(e[o][1][t]||t)}),d,d.exports,t,e,i,s)}return i[o].exports}for(var r=void 0,o=0;o<s.length;o++)n(s[o]);return n}({1:[function(t,e,i){e.exports=function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)}},{}],2:[function(t,e,i){e.exports=function(t){var e=1.70158;return t*t*((e+1)*t-e)}},{}],3:[function(t,e,i){e.exports=function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1}},{}],4:[function(t,e,i){var s=t("./bounce-out");e.exports=function(t){return t<.5?.5*(1-s(1-2*t)):.5*s(2*t-1)+.5}},{"./bounce-out":6}],5:[function(t,e,i){var s=t("./bounce-out");e.exports=function(t){return 1-s(1-t)}},{"./bounce-out":6}],6:[function(t,e,i){e.exports=function(t){var e=t*t;return t<4/11?7.5625*e:t<8/11?9.075*e-9.9*t+3.4:t<.9?4356/361*e-35442/1805*t+16061/1805:10.8*t*t-20.52*t+10.72}},{}],7:[function(t,e,i){e.exports=function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)}},{}],8:[function(t,e,i){e.exports=function(t){return 1-Math.sqrt(1-t*t)}},{}],9:[function(t,e,i){e.exports=function(t){return Math.sqrt(1- --t*t)}},{}],10:[function(t,e,i){e.exports=function(t){return t<.5?4*t*t*t:.5*Math.pow(2*t-2,3)+1}},{}],11:[function(t,e,i){e.exports=function(t){return t*t*t}},{}],12:[function(t,e,i){e.exports=function(t){var e=t-1;return e*e*e+1}},{}],13:[function(t,e,i){e.exports=function(t){return t<.5?.5*Math.sin(13*Math.PI/2*2*t)*Math.pow(2,10*(2*t-1)):.5*Math.sin(-13*Math.PI/2*(2*t-1+1))*Math.pow(2,-10*(2*t-1))+1}},{}],14:[function(t,e,i){e.exports=function(t){return Math.sin(13*t*Math.PI/2)*Math.pow(2,10*(t-1))}},{}],15:[function(t,e,i){e.exports=function(t){return Math.sin(-13*(t+1)*Math.PI/2)*Math.pow(2,-10*t)+1}},{}],16:[function(t,e,i){e.exports=function(t){return 0===t||1===t?t:t<.5?.5*Math.pow(2,20*t-10):-.5*Math.pow(2,10-20*t)+1}},{}],17:[function(t,e,i){e.exports=function(t){return 0===t?t:Math.pow(2,10*(t-1))}},{}],18:[function(t,e,i){e.exports=function(t){return 1===t?t:1-Math.pow(2,-10*t)}},{}],19:[function(t,e,i){e.exports={backInOut:t("./back-in-out"),backIn:t("./back-in"),backOut:t("./back-out"),bounceInOut:t("./bounce-in-out"),bounceIn:t("./bounce-in"),bounceOut:t("./bounce-out"),circInOut:t("./circ-in-out"),circIn:t("./circ-in"),circOut:t("./circ-out"),cubicInOut:t("./cubic-in-out"),cubicIn:t("./cubic-in"),cubicOut:t("./cubic-out"),elasticInOut:t("./elastic-in-out"),elasticIn:t("./elastic-in"),elasticOut:t("./elastic-out"),expoInOut:t("./expo-in-out"),expoIn:t("./expo-in"),expoOut:t("./expo-out"),linear:t("./linear"),quadInOut:t("./quad-in-out"),quadIn:t("./quad-in"),quadOut:t("./quad-out"),quartInOut:t("./quart-in-out"),quartIn:t("./quart-in"),quartOut:t("./quart-out"),quintInOut:t("./quint-in-out"),quintIn:t("./quint-in"),quintOut:t("./quint-out"),sineInOut:t("./sine-in-out"),sineIn:t("./sine-in"),sineOut:t("./sine-out")}},{"./back-in":2,"./back-in-out":1,"./back-out":3,"./bounce-in":5,"./bounce-in-out":4,"./bounce-out":6,"./circ-in":8,"./circ-in-out":7,"./circ-out":9,"./cubic-in":11,"./cubic-in-out":10,"./cubic-out":12,"./elastic-in":14,"./elastic-in-out":13,"./elastic-out":15,"./expo-in":17,"./expo-in-out":16,"./expo-out":18,"./linear":20,"./quad-in":22,"./quad-in-out":21,"./quad-out":23,"./quart-in":25,"./quart-in-out":24,"./quart-out":26,"./quint-in":28,"./quint-in-out":27,"./quint-out":29,"./sine-in":31,"./sine-in-out":30,"./sine-out":32}],20:[function(t,e,i){e.exports=function(t){return t}},{}],21:[function(t,e,i){e.exports=function(t){return(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1)}},{}],22:[function(t,e,i){e.exports=function(t){return t*t}},{}],23:[function(t,e,i){e.exports=function(t){return-t*(t-2)}},{}],24:[function(t,e,i){e.exports=function(t){return t<.5?8*Math.pow(t,4):-8*Math.pow(t-1,4)+1}},{}],25:[function(t,e,i){e.exports=function(t){return Math.pow(t,4)}},{}],26:[function(t,e,i){e.exports=function(t){return Math.pow(t-1,3)*(1-t)+1}},{}],27:[function(t,e,i){e.exports=function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)}},{}],28:[function(t,e,i){e.exports=function(t){return t*t*t*t*t}},{}],29:[function(t,e,i){e.exports=function(t){return--t*t*t*t*t+1}},{}],30:[function(t,e,i){e.exports=function(t){return-.5*(Math.cos(Math.PI*t)-1)}},{}],31:[function(t,e,i){e.exports=function(t){var e=Math.cos(t*Math.PI*.5);return Math.abs(e)<1e-14?1:1-e}},{}],32:[function(t,e,i){e.exports=function(t){return Math.sin(t*Math.PI/2)}},{}],33:[function(t,e,i){e.exports=function(t,e){e||(e=[0,""]),t=String(t);var i=parseFloat(t,10);return e[0]=i,e[1]=t.match(/[\d.\-\+]*\s*(.*)/)[1]||"",e}},{}],34:[function(t,e,i){"use strict";Object.defineProperty(i,"__esModule",{value:!0}),i.create=void 0;var s=r(t("parse-unit")),n=r(t("eases"));function r(t){return t&&t.__esModule?t:{default:t}}function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var a,l,d,h=[],c="undefined"!=typeof window,u=function(){return(document.scrollingElement||document.documentElement).scrollTop},p=function(){return window.innerHeight||window.outerHeight},m=function(t){return!1===isNaN((0,s.default)(t)[0])},f=function(t){var e=(0,s.default)(t);return{value:e[0],unit:e[1]}},v=function(t){return null!==String(t).match(/^[a-z]+-[a-z]+$/)},g=function(t,e){return!0===t?e.elem:t instanceof HTMLElement==1?e.direct:e.global},_=function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:u(),s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:p(),n=e.getBoundingClientRect(),r=t.match(/^[a-z]+/)[0],o=t.match(/[a-z]+$/)[0],a=0;return"top"===o&&(a-=0),"middle"===o&&(a-=s/2),"bottom"===o&&(a-=s),"top"===r&&(a+=n.top+i),"middle"===r&&(a+=n.top+i+n.height/2),"bottom"===r&&(a+=n.top+i+n.height),"".concat(a,"px")},y=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u(),i=t.getData(),s=i.to.value-i.from.value,n=(e-i.from.value)/(s/100),r=Math.min(Math.max(n,0),100),o=g(i.direct,{global:document.documentElement,elem:i.elem,direct:i.direct}),a=Object.keys(i.props).reduce((function(t,e){var s=i.props[e],n=s.from.unit||s.to.unit,o=s.from.value-s.to.value,a=s.timing(r/100),l=s.from.value-o*a,d=Math.round(1e4*l)/1e4;return t[e]=d+n,t}),{}),l=n<0||n>100;return 1==(n>=0&&n<=100)&&i.inside(t,n,a),!0===l&&i.outside(t,n,a),{elem:o,props:a}},b=function(t,e){Object.keys(e).forEach((function(i){return function(t,e){t.style.setProperty(e.key,e.value)}(t,{key:i,value:e[i]})}))};i.create=function(t){var e=null,i=!1,s={isActive:function(){return i},getData:function(){return e},calculate:function(){e=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(null==(t=Object.assign({},t)).inside&&(t.inside=function(){}),null==t.outside&&(t.outside=function(){}),null==t.direct&&(t.direct=!1),null==t.track&&(t.track=!0),null==t.props&&(t.props={}),null==t.from)throw new Error("Missing property `from`");if(null==t.to)throw new Error("Missing property `to`");if("function"!=typeof t.inside)throw new Error("Property `inside` must be undefined or a function");if("function"!=typeof t.outside)throw new Error("Property `outside` must be undefined or a function");if("boolean"!=typeof t.direct&&t.direct instanceof HTMLElement==0)throw new Error("Property `direct` must be undefined, a boolean or a DOM element/node");if(!0===t.direct&&null==t.elem)throw new Error("Property `elem` is required when `direct` is true");if("boolean"!=typeof t.track)throw new Error("Property `track` must be undefined or a boolean");if("object"!==o(t.props))throw new Error("Property `props` must be undefined or an object");if(null==t.elem){if(!1===m(t.from))throw new Error("Property `from` must be a absolute value when no `elem` has been provided");if(!1===m(t.to))throw new Error("Property `to` must be a absolute value when no `elem` has been provided")}else!0===v(t.from)&&(t.from=_(t.from,t.elem)),!0===v(t.to)&&(t.to=_(t.to,t.elem));return t.from=f(t.from),t.to=f(t.to),t.props=Object.keys(t.props).reduce((function(e,i){var s=Object.assign({},t.props[i]);if(!1===m(s.from))throw new Error("Property `from` of prop must be a absolute value");if(!1===m(s.to))throw new Error("Property `from` of prop must be a absolute value");if(s.from=f(s.from),s.to=f(s.to),null==s.timing&&(s.timing=n.default.linear),"string"!=typeof s.timing&&"function"!=typeof s.timing)throw new Error("Property `timing` of prop must be undefined, a string or a function");if("string"==typeof s.timing&&null==n.default[s.timing])throw new Error("Unknown timing for property `timing` of prop");return"string"==typeof s.timing&&(s.timing=n.default[s.timing]),e[i]=s,e}),{}),t}(t)},update:function(){var t=y(s),e=t.elem,i=t.props;return b(e,i),i},start:function(){i=!0},stop:function(){i=!1},destroy:function(){h[r]=void 0}},r=h.push(s)-1;return s.calculate(),s},!0===c&&(function t(e,i){var s=function(){requestAnimationFrame((function(){return t(e,i)}))},n=function(t){return t.filter((function(t){return null!=t&&t.isActive()}))}(h);if(0===n.length)return s();var r=u();if(i===r)return s();i=r,n.map((function(t){return y(t,r)})).forEach((function(t){var e=t.elem,i=t.props;return b(e,i)})),s()}(),window.addEventListener("resize",(a=function(){(function(t){return t.filter((function(t){return null!=t&&t.getData().track}))})(h).forEach((function(t){t.calculate(),t.update()}))},l=50,d=null,function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];clearTimeout(d),d=setTimeout((function(){return a.apply(void 0,e)}),l)})))},{eases:19,"parse-unit":33}]},{},[34])(34)},4797:function(t,e,i){"use strict";i.r(e)},1440:function(t,e){var i,s;s=this,void 0===(i=function(){return s.svg4everybody=function(){
/*! svg4everybody v2.1.9 | github.com/jonathantneal/svg4everybody */
function t(t,e,i){if(i){var s=document.createDocumentFragment(),n=!e.hasAttribute("viewBox")&&i.getAttribute("viewBox");n&&e.setAttribute("viewBox",n);for(var r=i.cloneNode(!0);r.childNodes.length;)s.appendChild(r.firstChild);t.appendChild(s)}}function e(e){e.onreadystatechange=function(){if(4===e.readyState){var i=e._cachedDocument;i||((i=e._cachedDocument=document.implementation.createHTMLDocument("")).body.innerHTML=e.responseText,e._cachedTarget={}),e._embeds.splice(0).map((function(s){var n=e._cachedTarget[s.id];n||(n=e._cachedTarget[s.id]=i.getElementById(s.id)),t(s.parent,s.svg,n)}))}},e.onreadystatechange()}function i(i){function n(){for(var i=0;i<m.length;){var a=m[i],l=a.parentNode,d=s(l),h=a.getAttribute("xlink:href")||a.getAttribute("href");if(!h&&o.attributeName&&(h=a.getAttribute(o.attributeName)),d&&h){if(r)if(!o.validate||o.validate(h,d,a)){l.removeChild(a);var c=h.split("#"),v=c.shift(),g=c.join("#");if(v.length){var _=u[v];_||((_=u[v]=new XMLHttpRequest).open("GET",v),_.send(),_._embeds=[]),_._embeds.push({parent:l,svg:d,id:g}),e(_)}else t(l,d,document.getElementById(g))}else++i,++f}else++i}(!m.length||m.length-f>0)&&p(n,67)}var r,o=Object(i),a=/\bTrident\/[567]\b|\bMSIE (?:9|10)\.0\b/,l=/\bAppleWebKit\/(\d+)\b/,d=/\bEdge\/12\.(\d+)\b/,h=/\bEdge\/.(\d+)\b/,c=window.top!==window.self;r="polyfill"in o?o.polyfill:a.test(navigator.userAgent)||(navigator.userAgent.match(d)||[])[1]<10547||(navigator.userAgent.match(l)||[])[1]<537||h.test(navigator.userAgent)&&c;var u={},p=window.requestAnimationFrame||setTimeout,m=document.getElementsByTagName("use"),f=0;r&&n()}function s(t){for(var e=t;"svg"!==e.nodeName.toLowerCase()&&(e=e.parentNode););return e}return i}()}.apply(e,[]))||(t.exports=i)},4241:function(t,e,i){"use strict";i.d(e,{a:function(){return ae},b:function(){return le},c:function(){return de},d:function(){return ce},e:function(){return Pe},f:function(){return Ne},g:function(){return xe},h:function(){return Me},i:function(){return ke},j:function(){return De}});var s={},n=Uint8Array,r=Uint16Array,o=Int32Array,a=new n([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),l=new n([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),d=new n([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),h=function(t,e){for(var i=new r(31),s=0;s<31;++s)i[s]=e+=1<<t[s-1];var n=new o(i[30]);for(s=1;s<30;++s)for(var a=i[s];a<i[s+1];++a)n[a]=a-i[s]<<5|s;return{b:i,r:n}},c=h(a,2),u=c.b,p=c.r;u[28]=258,p[258]=28;var m=h(l,0).b,f=new r(32768);for(b=0;b<32768;++b)v=(61680&(v=(52428&(v=(43690&b)>>1|(21845&b)<<1))>>2|(13107&v)<<2))>>4|(3855&v)<<4,f[b]=((65280&v)>>8|(255&v)<<8)>>1;var v,g=function(t,e,i){for(var s=t.length,n=0,o=new r(e);n<s;++n)t[n]&&++o[t[n]-1];var a,l=new r(e);for(n=1;n<e;++n)l[n]=l[n-1]+o[n-1]<<1;if(i){a=new r(1<<e);var d=15-e;for(n=0;n<s;++n)if(t[n])for(var h=n<<4|t[n],c=e-t[n],u=l[t[n]-1]++<<c,p=u|(1<<c)-1;u<=p;++u)a[f[u]>>d]=h}else for(a=new r(s),n=0;n<s;++n)t[n]&&(a[n]=f[l[t[n]-1]++]>>15-t[n]);return a},_=new n(288);for(b=0;b<144;++b)_[b]=8;for(b=144;b<256;++b)_[b]=9;for(b=256;b<280;++b)_[b]=7;for(b=280;b<288;++b)_[b]=8;var y=new n(32);for(b=0;b<32;++b)y[b]=5;var b,E=g(_,9,1),w=g(y,5,1),T=function(t){for(var e=t[0],i=1;i<t.length;++i)t[i]>e&&(e=t[i]);return e},S=function(t,e,i){var s=e/8|0;return(t[s]|t[s+1]<<8)>>(7&e)&i},A=function(t,e){var i=e/8|0;return(t[i]|t[i+1]<<8|t[i+2]<<16)>>(7&e)},L=function(t){return(t+7)/8|0},O=function(t,e,i){(null==e||e<0)&&(e=0),(null==i||i>t.length)&&(i=t.length);var s=new n(i-e);return s.set(t.subarray(e,i)),s},C=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],I=function(t,e,i){var s=new Error(e||C[t]);if(s.code=t,Error.captureStackTrace&&Error.captureStackTrace(s,I),!i)throw s;return s},P=function(t,e,i,s){var r=t.length,o=s?s.length:0;if(!r||e.f&&!e.l)return i||new n(0);var h=!i||2!=e.i,c=e.i;i||(i=new n(3*r));var p=function(t){var e=i.length;if(t>e){var s=new n(Math.max(2*e,t));s.set(i),i=s}},f=e.f||0,v=e.p||0,_=e.b||0,y=e.l,b=e.d,C=e.m,P=e.n,N=8*r;do{if(!y){f=S(t,v,1);var x=S(t,v+1,3);if(v+=3,!x){var M=t[(q=L(v)+4)-4]|t[q-3]<<8,k=q+M;if(k>r){c&&I(0);break}h&&p(_+M),i.set(t.subarray(q,k),_),e.b=_+=M,e.p=v=8*k,e.f=f;continue}if(1==x)y=E,b=w,C=9,P=5;else if(2==x){var D=S(t,v,31)+257,R=S(t,v+10,15)+4,$=D+S(t,v+5,31)+1;v+=14;for(var F=new n($),H=new n(19),U=0;U<R;++U)H[d[U]]=S(t,v+3*U,7);v+=3*R;var z=T(H),B=(1<<z)-1,V=g(H,z,1);for(U=0;U<$;){var q,G=V[S(t,v,B)];if(v+=15&G,(q=G>>4)<16)F[U++]=q;else{var W=0,K=0;for(16==q?(K=3+S(t,v,3),v+=2,W=F[U-1]):17==q?(K=3+S(t,v,7),v+=3):18==q&&(K=11+S(t,v,127),v+=7);K--;)F[U++]=W}}var j=F.subarray(0,D),Y=F.subarray(D);C=T(j),P=T(Y),y=g(j,C,1),b=g(Y,P,1)}else I(1);if(v>N){c&&I(0);break}}h&&p(_+131072);for(var Z=(1<<C)-1,X=(1<<P)-1,J=v;;J=v){var Q=(W=y[A(t,v)&Z])>>4;if((v+=15&W)>N){c&&I(0);break}if(W||I(2),Q<256)i[_++]=Q;else{if(256==Q){J=v,y=null;break}var tt=Q-254;if(Q>264){var et=a[U=Q-257];tt=S(t,v,(1<<et)-1)+u[U],v+=et}var it=b[A(t,v)&X],st=it>>4;it||I(3),v+=15&it;Y=m[st];if(st>3){et=l[st];Y+=A(t,v)&(1<<et)-1,v+=et}if(v>N){c&&I(0);break}h&&p(_+131072);var nt=_+tt;if(_<Y){var rt=o-Y,ot=Math.min(Y,nt);for(rt+_<0&&I(3);_<ot;++_)i[_]=s[rt+_]}for(;_<nt;_+=4)i[_]=i[_-Y],i[_+1]=i[_+1-Y],i[_+2]=i[_+2-Y],i[_+3]=i[_+3-Y];_=nt}}e.l=y,e.p=J,e.b=_,e.f=f,y&&(f=1,e.m=C,e.d=b,e.n=P)}while(!f);return _==i.length?i:O(i,0,_)},N=new n(0),x=function(t,e,i){for(var s=t(),n=t.toString(),r=n.slice(n.indexOf("[")+1,n.lastIndexOf("]")).replace(/\s+/g,"").split(","),o=0;o<s.length;++o){var a=s[o],l=r[o];if("function"==typeof a){e+=";"+l+"=";var d=a.toString();if(a.prototype)if(-1!=d.indexOf("[native code]")){var h=d.indexOf(" ",8)+1;e+=d.slice(h,d.indexOf("(",h))}else for(var c in e+=d,a.prototype)e+=";"+l+".prototype."+c+"="+a.prototype[c].toString();else e+=d}else i[l]=a}return e},M=[],k=function(t,e,i,n){if(!M[i]){for(var r="",o={},a=t.length-1,l=0;l<a;++l)r=x(t[l],r,o);M[i]={c:x(t[a],r,o),e:o}}var d=function(t,e){var i={};for(var s in t)i[s]=t[s];for(var s in e)i[s]=e[s];return i}({},M[i].e);return function(t,e,i,n,r){var o=new Worker(s[e]||(s[e]=URL.createObjectURL(new Blob([t+';addEventListener("error",function(e){e=e.error;postMessage({$e$:[e.message,e.code,e.stack]})})'],{type:"text/javascript"}))));return o.onmessage=function(t){var e=t.data,i=e.$e$;if(i){var s=new Error(i[0]);s.code=i[1],s.stack=i[2],r(s,null)}else r(null,e)},o.postMessage(i,n),o}(M[i].c+";onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage="+e.toString()+"}",i,d,function(t){var e=[];for(var i in t)t[i].buffer&&e.push((t[i]=new t[i].constructor(t[i])).buffer);return e}(d),n)},D=function(){return[n,r,o,a,l,d,u,m,E,w,f,C,g,T,S,A,L,O,I,P,B,R,$]},R=function(t){return postMessage(t,[t.buffer])},$=function(t){return t&&{out:t.size&&new n(t.size),dictionary:t.dictionary}},F=function(t,e,i,s,n,r){var o=k(i,s,n,(function(t,e){o.terminate(),r(t,e)}));return o.postMessage([t,e],e.consume?[t.buffer]:[]),function(){o.terminate()}},H=function(t,e){return t[e]|t[e+1]<<8},U=function(t,e){return(t[e]|t[e+1]<<8|t[e+2]<<16|t[e+3]<<24)>>>0},z=function(t,e){return U(t,e)+4294967296*U(t,e+4)};function B(t,e){return P(t,{i:2},e&&e.out,e&&e.dictionary)}var V=typeof TextDecoder<"u"&&new TextDecoder;try{V.decode(N,{stream:!0}),1}catch{}var q=function(t){for(var e="",i=0;;){var s=t[i++],n=(s>127)+(s>223)+(s>239);if(i+n>t.length)return{s:e,r:O(t,i-1)};n?3==n?(s=((15&s)<<18|(63&t[i++])<<12|(63&t[i++])<<6|63&t[i++])-65536,e+=String.fromCharCode(55296|s>>10,56320|1023&s)):e+=1&n?String.fromCharCode((31&s)<<6|63&t[i++]):String.fromCharCode((15&s)<<12|(63&t[i++])<<6|63&t[i++]):e+=String.fromCharCode(s)}};function G(t,e){if(e){for(var i="",s=0;s<t.length;s+=16384)i+=String.fromCharCode.apply(null,t.subarray(s,s+16384));return i}if(V)return V.decode(t);var n=q(t),r=n.s;return(i=n.r).length&&I(8),r}var W=function(t,e){return e+30+H(t,e+26)+H(t,e+28)},K=function(t,e,i){var s=H(t,e+28),n=G(t.subarray(e+46,e+46+s),!(2048&H(t,e+8))),r=e+46+s,o=U(t,e+20),a=i&&4294967295==o?j(t,r):[o,U(t,e+24),U(t,e+42)],l=a[0],d=a[1],h=a[2];return[H(t,e+10),l,d,n,r+H(t,e+30)+H(t,e+32),h]},j=function(t,e){for(;1!=H(t,e);e+=4+H(t,e+2));return[z(t,e+12),z(t,e+4),z(t,e+20)]},Y="function"==typeof queueMicrotask?queueMicrotask:"function"==typeof setTimeout?setTimeout:function(t){t()};function Z(t,e,i){i||(i=e,e={}),"function"!=typeof i&&I(7);var s=[],r=function(){for(var t=0;t<s.length;++t)s[t]()},o={},a=function(t,e){Y((function(){i(t,e)}))};Y((function(){a=i}));for(var l=t.length-22;101010256!=U(t,l);--l)if(!l||t.length-l>65558)return a(I(13,0,1),null),r;var d=H(t,l+8);if(d){var h=d,c=U(t,l+16),u=4294967295==c||65535==h;if(u){var p=U(t,l-12);(u=101075792==U(t,p))&&(h=d=U(t,p+32),c=U(t,p+48))}for(var m=e&&e.filter,f=function(e){var i=K(t,c,u),l=i[0],h=i[1],p=i[2],f=i[3],v=i[4],g=i[5],_=W(t,g);c=v;var y=function(t,e){t?(r(),a(t,null)):(e&&(o[f]=e),--d||a(null,o))};if(!m||m({name:f,size:h,originalSize:p,compression:l}))if(l)if(8==l){var b=t.subarray(_,_+h);if(h<32e4)try{y(null,B(b,{out:new n(p)}))}catch(t){y(t,null)}else s.push(function(t,e,i){return i||(i=e,e={}),"function"!=typeof i&&I(7),F(t,e,[D],(function(t){return R(B(t.data[0],$(t.data[1])))}),1,i)}(b,{size:p},y))}else y(I(14,"unknown compression type "+l,1),null);else y(null,O(t,_,_+h));else y(null,null)},v=0;v<h;++v)f()}else a(null,{});return r}function X(t){return(Array.isArray(t)?t:t.issues).reduce(((t,e)=>{if(e.path){let i=e.path.map((({key:t})=>t)).join(".");t.nested[i]=[...t.nested[i]||[],e.message]}else t.root=[...t.root||[],e.message];return t}),{nested:{}})}var J=class extends Error{issues;constructor(t){super(t[0].message),this.name="ValiError",this.issues=t}};function Q(t,e){return{reason:null==t?void 0:t.reason,validation:e.validation,origin:(null==t?void 0:t.origin)||"value",message:e.message,input:e.input,abortEarly:null==t?void 0:t.abortEarly,abortPipeEarly:null==t?void 0:t.abortPipeEarly}}function tt(t,e){return{reason:e,origin:null==t?void 0:t.origin,abortEarly:null==t?void 0:t.abortEarly,abortPipeEarly:null==t?void 0:t.abortPipeEarly}}function et(t,e,i,s){if(!e||!e.length)return{output:t};let n,r,o=t;for(let t of e){let e=t(o);if(e.issue){n=n||tt(i,s);let t=Q(n,e.issue);if(r?r.push(t):r=[t],n.abortEarly||n.abortPipeEarly)break}else o=e.output}return r?{issues:r}:{output:o}}function it(t,e){return t&&"string"!=typeof t?[void 0,t]:[t,e]}function st(t,e,i,s,n,r){return{issues:[{reason:e,validation:i,origin:(null==t?void 0:t.origin)||"value",message:s,input:n,issues:r,abortEarly:null==t?void 0:t.abortEarly,abortPipeEarly:null==t?void 0:t.abortPipeEarly}]}}function nt(t,e,i){let[s,n]=it(e,i);return{schema:"array",array:{item:t},async:!1,_parse(e,i){if(!Array.isArray(e))return st(i,"type","array",s||"Invalid type",e);let r,o=[];for(let s=0;s<e.length;s++){let n=e[s],a=t._parse(n,i);if(a.issues){let t={schema:"array",input:e,key:s,value:n};for(let e of a.issues)e.path?e.path.unshift(t):e.path=[t],null==r||r.push(e);if(r||(r=a.issues),null!=i&&i.abortEarly)break}else o.push(a.output)}return r?{issues:r}:et(o,n,i,"array")}}}function rt(t,e){let[i,s]=it(t,e);return{schema:"boolean",async:!1,_parse(t,e){return"boolean"!=typeof t?st(e,"type","boolean",i||"Invalid type",t):et(t,s,e,"boolean")}}}function ot(t,e){return{schema:"literal",literal:t,async:!1,_parse(i,s){return i!==t?st(s,"type","literal",e||"Invalid type",i):{output:i}}}}function at(t,e){let[i,s]=it(t,e);return{schema:"number",async:!1,_parse(t,e){return"number"!=typeof t?st(e,"type","number",i||"Invalid type",t):et(t,s,e,"number")}}}function lt(t,e,i){let s,[n,r]=it(e,i);return{schema:"object",object:t,async:!1,_parse(e,i){if(!e||"object"!=typeof e)return st(i,"type","object",n||"Invalid type",e);s=s||Object.entries(t);let o,a={};for(let[t,n]of s){let s=e[t],r=n._parse(s,i);if(r.issues){let n={schema:"object",input:e,key:t,value:s};for(let t of r.issues)t.path?t.path.unshift(n):t.path=[n],null==o||o.push(t);if(o||(o=r.issues),null!=i&&i.abortEarly)break}else a[t]=r.output}return o?{issues:o}:et(a,r,i,"object")}}}function dt(t){return{schema:"optional",wrapped:t,async:!1,_parse(e,i){return void 0===e?{output:e}:t._parse(e,i)}}}function ht(t,e){let[i,s]=it(t,e);return{schema:"string",async:!1,_parse(t,e){return"string"!=typeof t?st(e,"type","string",i||"Invalid type",t):et(t,s,e,"string")}}}var ct=["__proto__","prototype","constructor"];function ut(t,e,i,s){let[n,r,o]=function(t,e,i){if("object"==typeof t&&!Array.isArray(t)){let[s,n]=it(e,i);return[t,s,n]}let[s,n]=it(t,e);return[void 0,s,n]}(e,i,s);return{schema:"tuple",tuple:{items:t,rest:n},async:!1,_parse(e,i){if(!Array.isArray(e)||!n&&t.length!==e.length||n&&t.length>e.length)return st(i,"type","tuple",r||"Invalid type",e);let s,a=[];for(let n=0;n<t.length;n++){let r=e[n],o=t[n]._parse(r,i);if(o.issues){let t={schema:"tuple",input:e,key:n,value:r};for(let e of o.issues)e.path?e.path.unshift(t):e.path=[t],null==s||s.push(e);if(s||(s=o.issues),null!=i&&i.abortEarly)break}else a[n]=o.output}if(n)for(let r=t.length;r<e.length;r++){let t=e[r],o=n._parse(t,i);if(o.issues){let n={schema:"tuple",input:e,key:r,value:t};for(let t of o.issues)t.path?t.path.unshift(n):t.path=[n],null==s||s.push(t);if(s||(s=o.issues),null!=i&&i.abortEarly)break}else a[r]=o.output}return s?{issues:s}:et(a,o,i,"tuple")}}}function pt(t,e){return{schema:"union",union:t,async:!1,_parse(i,s){let n,r;for(let e of t){let t=e._parse(i,s);if(!t.issues){r=[t.output];break}if(n)for(let e of t.issues)n.push(e);else n=t.issues}return r?{output:r[0]}:st(s,"type","union",e||"Invalid type",i,n)}}}function mt(t,e,i){let[s,n]=it(e,i);return lt(t.reduce(((t,e)=>({...t,...e.object})),{}),s,n)}function ft(t,e){return i=>i>t?{issue:{validation:"max_value",message:e||"Invalid value",input:i}}:{output:i}}function vt(t,e){return i=>i<t?{issue:{validation:"min_value",message:e||"Invalid value",input:i}}:{output:i}}var gt,_t=Object.create,yt=Object.defineProperty,bt=Object.getOwnPropertyDescriptor,Et=Object.getOwnPropertyNames,wt=Object.getPrototypeOf,Tt=Object.prototype.hasOwnProperty,St=(t,e)=>function(){return e||(0,t[Et(t)[0]])((e={exports:{}}).exports,e),e.exports},At=(t,e,i)=>(((t,e,i)=>{e in t?yt(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i})(t,"symbol"!=typeof e?e+"":e,i),i),Lt=St({"../../node_modules/.pnpm/@rgba-image+copy@0.1.3/node_modules/@rgba-image/copy/dist/index.js"(t){Object.defineProperty(t,"__esModule",{value:!0}),t.copy=void 0;t.copy=(t,e,i=0,s=0,n=t.width-i,r=t.height-s,o=0,a=0)=>{if(i|=0,s|=0,r|=0,o|=0,a|=0,(n|=0)<=0||r<=0)return;let l=new Uint32Array(t.data.buffer),d=new Uint32Array(e.data.buffer);for(let h=0;h<r;h++){let r=s+h;if(r<0||r>=t.height)continue;let c=a+h;if(!(c<0||c>=e.height))for(let s=0;s<n;s++){let n=i+s;if(n<0||n>=t.width)continue;let a=o+s;if(a<0||a>=e.width)continue;let h=r*t.width+n;d[c*e.width+a]=l[h]}}}}}),Ot=St({"../../node_modules/.pnpm/@rgba-image+create-image@0.1.1/node_modules/@rgba-image/create-image/dist/index.js"(t){Object.defineProperty(t,"__esModule",{value:!0}),t.CreateImageFactory=(t=[0,0,0,0],e=4)=>{if(e=Math.floor(e),isNaN(e)||e<1)throw TypeError("channels should be a positive non-zero number");if(!("length"in t)||t.length<e)throw TypeError(`fill should be iterable with at least ${e} members`);let i=(t=new Uint8ClampedArray(t).slice(0,e)).every((t=>0===t));return(s,n,r)=>{if(void 0===s||void 0===n)throw TypeError("Not enough arguments");if(s=Math.floor(s),n=Math.floor(n),isNaN(s)||s<1||isNaN(n)||n<1)throw TypeError("Index or size is negative or greater than the allowed amount");let o=s*n*e;if(void 0===r&&(r=new Uint8ClampedArray(o)),r instanceof Uint8ClampedArray){if(r.length!==o)throw TypeError("Index or size is negative or greater than the allowed amount");if(!i)for(let i=0;i<n;i++)for(let n=0;n<s;n++){let o=(i*s+n)*e;for(let i=0;i<e;i++)r[o+i]=t[i]}return{get width(){return s},get height(){return n},get data(){return r}}}throw TypeError("Expected data to be Uint8ClampedArray or undefined")}},t.createImage=t.CreateImageFactory()}}),Ct=St({"../../node_modules/.pnpm/@rgba-image+lanczos@0.1.1/node_modules/@rgba-image/lanczos/dist/filters.js"(t){Object.defineProperty(t,"__esModule",{value:!0}),t.filters=void 0;var e=(t,e)=>{if(t<=-e||t>=e||0==t)return 0;let i=t*Math.PI;return Math.sin(i)/i*Math.sin(i/e)/(i/e)},i=t=>Math.round(16383*t);t.filters=(t,s,n,r,o)=>{let a=o?2:3,l=1/n,d=Math.min(1,n),h=a/d,c=Math.floor(2*(h+1)),u=new Int16Array((c+2)*s),p=0;for(let n=0;n<s;n++){let o=(n+.5)*l+r,c=Math.max(0,Math.floor(o-h)),m=Math.min(t-1,Math.ceil(o+h)),f=m-c+1,v=new Float32Array(f),g=new Int16Array(f),_=0,y=0;for(let t=c;t<=m;t++){let i=e((t+.5-o)*d,a);_+=i,v[y]=i,y++}let b=0;for(let t=0;t<v.length;t++){let e=v[t]/_;b+=e,g[t]=i(e)}g[s>>1]+=i(1-b);let E=0;for(;E<g.length&&0===g[E];)E++;let w=g.length-1;for(;w>0&&0===g[w];)w--;let T=c+E,S=w-E+1;u[p++]=T,u[p++]=S,u.set(g.subarray(E,w+1),p),p+=S}return u}}}),It=St({"../../node_modules/.pnpm/@rgba-image+lanczos@0.1.1/node_modules/@rgba-image/lanczos/dist/convolve.js"(t){Object.defineProperty(t,"__esModule",{value:!0}),t.convolve=void 0;t.convolve=(t,e,i,s,n,r)=>{let o=0,a=0;for(let l=0;l<s;l++){let d=0;for(let i=0;i<n;i++){let i=o+4*r[d++]|0,n=0,l=0,h=0,c=0;for(let e=r[d++];e>0;e--){let e=r[d++];n=n+e*t[i]|0,l=l+e*t[i+1]|0,h=h+e*t[i+2]|0,c=c+e*t[i+3]|0,i=i+4|0}e[a]=n+8192>>14,e[a+1]=l+8192>>14,e[a+2]=h+8192>>14,e[a+3]=c+8192>>14,a=a+4*s|0}a=4*(l+1)|0,o=(l+1)*i*4|0}}}}),Pt=St({"../../node_modules/.pnpm/@rgba-image+lanczos@0.1.1/node_modules/@rgba-image/lanczos/dist/index.js"(t){Object.defineProperty(t,"__esModule",{value:!0}),t.lanczos2=t.lanczos=void 0;var e=Lt(),i=Ot(),s=Ct(),n=It(),r=(t,e,i=!1)=>{let r=e.width/t.width,o=e.height/t.height,a=s.filters(t.width,e.width,r,0,i),l=s.filters(t.height,e.height,o,0,i),d=new Uint8ClampedArray(e.width*t.height*4);n.convolve(t.data,d,t.width,t.height,e.width,a),n.convolve(d,e.data,t.height,e.width,e.height,l)};t.lanczos=(t,s,n=0,o=0,a=t.width-n,l=t.height-o,d=0,h=0,c=s.width-d,u=s.height-h)=>{if(o|=0,l|=0,d|=0,h|=0,c|=0,u|=0,(a|=0)<=0||l<=0||c<=0||u<=0)return;if(0===(n|=0)&&0===o&&a===t.width&&l===t.height&&0===d&&0===h&&c===s.width&&u===s.height)return void r(t,s);let p=i.createImage(a,l),m=i.createImage(c,u);e.copy(t,p,n,o),r(p,m),e.copy(m,s,0,0,m.width,m.height,d,h)};t.lanczos2=(t,s,n=0,o=0,a=t.width-n,l=t.height-o,d=0,h=0,c=s.width-d,u=s.height-h)=>{if(o|=0,l|=0,d|=0,h|=0,c|=0,u|=0,(a|=0)<=0||l<=0||c<=0||u<=0)return;if(0===(n|=0)&&0===o&&a===t.width&&l===t.height&&0===d&&0===h&&c===s.width&&u===s.height)return void r(t,s,!0);let p=i.createImage(a,l),m=i.createImage(c,u);e.copy(t,p,n,o),r(p,m,!0),e.copy(m,s,0,0,m.width,m.height,d,h)}}}),Nt=((gt=Nt||{}).Bounce="bounce",gt.Normal="normal",gt),xt=function(t,e){return{schema:"native_enum",nativeEnum:t,async:!1,_parse(i,s){return Object.values(t).includes(i)?{output:i}:st(s,"type","native_enum",e||"Invalid type",i)}}}(Nt),Mt=lt({autoplay:dt(rt()),defaultTheme:dt(ht()),direction:dt(pt([ot(1),ot(-1)])),hover:dt(rt()),id:ht(),intermission:dt(at()),loop:dt(pt([rt(),at()])),playMode:dt(xt),speed:dt(at()),themeColor:dt(ht())}),kt=lt({animations:nt(ht()),id:ht()}),Dt=lt({activeAnimationId:dt(ht()),animations:nt(Mt),author:dt(ht()),custom:dt(function(t,e,i,s){let[n,r,o,a]=function(t,e,i,s){if("object"==typeof e&&!Array.isArray(e)){let[n,r]=it(i,s);return[t,e,n,r]}let[n,r]=it(e,i);return[ht(),t,n,r]}(t,e,i,s);return{schema:"record",record:{key:n,value:r},async:!1,_parse(t,e){if(!t||"object"!=typeof t)return st(e,"type","record",o||"Invalid type",t);let i,s={};for(let[o,a]of Object.entries(t))if(!ct.includes(o)){let l,d=n._parse(o,{origin:"key",abortEarly:null==e?void 0:e.abortEarly,abortPipeEarly:null==e?void 0:e.abortPipeEarly});if(d.issues){l={schema:"record",input:t,key:o,value:a};for(let t of d.issues)t.path=[l],null==i||i.push(t);if(i||(i=d.issues),null!=e&&e.abortEarly)break}let h=r._parse(a,e);if(h.issues){l=l||{schema:"record",input:t,key:o,value:a};for(let t of h.issues)t.path?t.path.unshift(l):t.path=[l],null==i||i.push(t);if(i||(i=h.issues),null!=e&&e.abortEarly)break}!d.issues&&!h.issues&&(s[d.output]=h.output)}return i?{issues:i}:et(s,a,e,"record")}}}(ht(),function(t=[]){return{schema:"any",async:!1,_parse(e,i){return et(e,t,i,"any")}}}())),description:dt(ht()),generator:dt(ht()),keywords:dt(ht()),revision:dt(at()),themes:dt(nt(kt)),states:dt(nt(ht())),version:dt(ht())}),Rt=function(t,e,i,s){let[n,r]=it(i,s);return lt(Object.entries(t.object).reduce(((t,[i,s])=>e.includes(i)?t:{...t,[i]:s}),{}),n,r)}(Mt,["id"]),$t=lt({state:ht()}),Ft=$t,Ht=mt([$t,lt({ms:at()})]),Ut=mt([$t,lt({count:at()})]),zt=$t,Bt=$t,Vt=$t,qt=mt([$t,lt({threshold:dt(nt(at([vt(0),ft(1)])))})]),Gt=lt({onAfter:dt(Ht),onClick:dt(Ft),onComplete:dt(Vt),onEnter:dt(Ut),onMouseEnter:dt(zt),onMouseLeave:dt(Bt),onShow:dt(qt)}),Wt=mt([Rt,lt({playOnScroll:dt(ut([at([vt(0),ft(1)]),at([vt(0),ft(1)])])),segments:dt(pt([ut([at(),at()]),ht()]))})]);mt([Gt,lt({animationId:dt(ht()),playbackSettings:Wt})]);var Kt={jpeg:"image/jpeg",png:"image/png",gif:"image/gif",bmp:"image/bmp",svg:"image/svg+xml",webp:"image/webp",mpeg:"audio/mpeg",mp3:"audio/mp3"},jt={jpeg:[255,216,255],png:[137,80,78,71,13,10,26,10],gif:[71,73,70],bmp:[66,77],webp:[82,73,70,70,87,69,66,80],svg:[60,63,120],mp3:[73,68,51,3,0,0,0,0],mpeg:[73,68,51,3,0,0,0,0]},Yt=t=>{let e=null,i=[];if(!t)return null;let s=t.substring(t.indexOf(",")+1);e=typeof window>"u"?Buffer.from(s,"base64").toString("binary"):atob(s);let n=new Uint8Array(e.length);for(let t=0;t<e.length;t+=1)n[t]=e.charCodeAt(t);i=Array.from(n.subarray(0,8));for(let t in jt){let e=jt[t];if(e&&i.every(((t,i)=>t===e[i])))return Kt[t]}return null},Zt=class extends Error{constructor(t,e){super(t),At(this,"code"),this.name="[dotlottie-js]",this.code=e}};function Xt(t){let e;if(typeof window>"u")e=Buffer.from(t).toString("base64");else{let i=Array.prototype.map.call(t,(t=>String.fromCharCode(t))).join("");e=window.btoa(i)}return`data:${Yt(e)};base64,${e}`}function Jt(t){return"w"in t&&"h"in t&&!("xt"in t)&&"p"in t}function Qt(t){return!("h"in t)&&!("w"in t)&&"p"in t&&"e"in t&&"u"in t&&"id"in t}async function te(t,e=(()=>!0)){if(!(t instanceof Uint8Array))throw new Zt("DotLottie not found","INVALID_DOTLOTTIE");return await new Promise(((i,s)=>{Z(t,{filter:e},((t,e)=>{t&&s(t),i(e)}))}))}async function ee(t,e,i){if(!(t instanceof Uint8Array))throw new Zt("DotLottie not found","INVALID_DOTLOTTIE");return(await te(t,(t=>t.name===e&&(!i||i(t)))))[e]}async function ie(t){let e="manifest.json",i=(await te(t,(t=>t.name===e)))[e];if(!(typeof i>"u"))return JSON.parse(G(i,!1))}async function se(t){if(!(t instanceof Uint8Array))return{success:!1,error:"DotLottie not found"};let e=await ie(t);if(typeof e>"u")return{success:!1,error:"Invalid .lottie file, manifest.json is missing"};let i=function(t,e,i){let s=t._parse(e,i);return s.issues?{success:!1,error:new J(s.issues),issues:s.issues}:{success:!0,data:s.output,output:s.output}}(Dt,e);return i.success?{success:!0}:{success:!1,error:`Invalid .lottie file, manifest.json structure is invalid, ${JSON.stringify(X(i.error).nested,null,2)}`}}async function ne(t){let e=new Uint8Array(t),i=await se(e);if(i.error)throw new Zt(i.error,"INVALID_DOTLOTTIE");return e}async function re(t,e){var i;let s=new Map;for(let[t,n]of Object.entries(e))for(let e of n.assets||[])if(Qt(e)){let n=e.p;s.has(n)||s.set(n,new Set),null==(i=s.get(n))||i.add(t)}let n=await async function(t,e){let i=await te(t,(t=>{let i=t.name.replace("audio/","");return t.name.startsWith("audio/")&&(!e||e({...t,name:i}))})),s={};for(let t in i){let e=i[t];e instanceof Uint8Array&&(s[t.replace("audio/","")]=Xt(e))}return s}(t,(t=>s.has(t.name)));for(let[t,i]of s){let s=n[t];if(s)for(let n of i){let i=e[n];for(let e of(null==i?void 0:i.assets)||[])Qt(e)&&e.p===t&&(e.p=s,e.u="",e.e=1)}}}async function oe(t,e){var i;let s=new Map;for(let[t,n]of Object.entries(e))for(let e of n.assets||[])if(Jt(e)){let n=e.p;s.has(n)||s.set(n,new Set),null==(i=s.get(n))||i.add(t)}let n=await async function(t,e){let i=await te(t,(t=>{let i=t.name.replace("images/","");return t.name.startsWith("images/")&&(!e||e({...t,name:i}))})),s={};for(let t in i){let e=i[t];e instanceof Uint8Array&&(s[t.replace("images/","")]=Xt(e))}return s}(t,(t=>s.has(t.name)));for(let[t,i]of s){let s=n[t];if(s)for(let n of i){let i=e[n];for(let e of(null==i?void 0:i.assets)||[])Jt(e)&&e.p===t&&(e.p=s,e.u="",e.e=1)}}}function ae(t,e="dotLottie-common"){return new Error(`[${e}]: ${t}`)}function le(t,e="dotLottie-common",...i){}function de(t,e="dotLottie-common",...i){}function he(t){return["v","ip","op","layers","fr","w","h"].every((e=>Object.prototype.hasOwnProperty.call(t,e)))}function ce(t,e){let i=Object.keys(t).find((i=>t[i]===e));if(void 0===i)throw new Error("Value not found in the object.");return i}((t,e,i)=>{i=null!=t?_t(wt(t)):{},((t,e,i,s)=>{if(e&&"object"==typeof e||"function"==typeof e)for(let n of Et(e))!Tt.call(t,n)&&n!==i&&yt(t,n,{get:()=>e[n],enumerable:!(s=bt(e,n))||s.enumerable})})(!e&&t&&t.__esModule?i:yt(i,"default",{value:t,enumerable:!0}),t)})(Pt());function ue(){throw new Error("Cycle detected")}function pe(){if(ve>1)ve--;else{for(var t,e=!1;void 0!==fe;){var i=fe;for(fe=void 0,ge++;void 0!==i;){var s=i.o;if(i.o=void 0,i.f&=-3,!(8&i.f)&&Ee(i))try{i.c()}catch(i){e||(t=i,e=!0)}i=s}}if(ge=0,ve--,e)throw t}}var me=void 0,fe=void 0,ve=0,ge=0,_e=0;function ye(t){if(void 0!==me){var e=t.n;if(void 0===e||e.t!==me)return e={i:0,S:t,p:me.s,n:void 0,t:me,e:void 0,x:void 0,r:e},void 0!==me.s&&(me.s.n=e),me.s=e,t.n=e,32&me.f&&t.S(e),e;if(-1===e.i)return e.i=0,void 0!==e.n&&(e.n.p=e.p,void 0!==e.p&&(e.p.n=e.n),e.p=me.s,e.n=void 0,me.s.n=e,me.s=e),e}}function be(t){this.v=t,this.i=0,this.n=void 0,this.t=void 0}function Ee(t){for(var e=t.s;void 0!==e;e=e.n)if(e.S.i!==e.i||!e.S.h()||e.S.i!==e.i)return!0;return!1}function we(t){for(var e=t.s;void 0!==e;e=e.n){var i=e.S.n;if(void 0!==i&&(e.r=i),e.S.n=e,e.i=-1,void 0===e.n){t.s=e;break}}}function Te(t){for(var e=t.s,i=void 0;void 0!==e;){var s=e.p;-1===e.i?(e.S.U(e),void 0!==s&&(s.n=e.n),void 0!==e.n&&(e.n.p=s)):i=e,e.S.n=e.r,void 0!==e.r&&(e.r=void 0),e=s}t.s=i}function Se(t){be.call(this,void 0),this.x=t,this.s=void 0,this.g=_e-1,this.f=4}function Ae(t){var e=t.u;if(t.u=void 0,"function"==typeof e){ve++;var i=me;me=void 0;try{e()}catch(e){throw t.f&=-2,t.f|=8,Le(t),e}finally{me=i,pe()}}}function Le(t){for(var e=t.s;void 0!==e;e=e.n)e.S.U(e);t.x=void 0,t.s=void 0,Ae(t)}function Oe(t){if(me!==this)throw new Error("Out-of-order effect");Te(this),me=t,this.f&=-2,8&this.f&&Le(this),pe()}function Ce(t){this.x=t,this.u=void 0,this.s=void 0,this.o=void 0,this.f=32}be.prototype.h=function(){return!0},be.prototype.S=function(t){this.t!==t&&void 0===t.e&&(t.x=this.t,void 0!==this.t&&(this.t.e=t),this.t=t)},be.prototype.U=function(t){if(void 0!==this.t){var e=t.e,i=t.x;void 0!==e&&(e.x=i,t.e=void 0),void 0!==i&&(i.e=e,t.x=void 0),t===this.t&&(this.t=i)}},be.prototype.subscribe=function(t){var e=this;return function(t){var e=new Ce(t);try{e.c()}catch(t){throw e.d(),t}return e.d.bind(e)}((function(){var i=e.value,s=32&this.f;this.f&=-33;try{t(i)}finally{this.f|=s}}))},be.prototype.valueOf=function(){return this.value},be.prototype.toString=function(){return this.value+""},be.prototype.toJSON=function(){return this.value},be.prototype.peek=function(){return this.v},Object.defineProperty(be.prototype,"value",{get:function(){var t=ye(this);return void 0!==t&&(t.i=this.i),this.v},set:function(t){if(me instanceof Se&&function(){throw new Error("Computed cannot have side-effects")}(),t!==this.v){ge>100&&ue(),this.v=t,this.i++,_e++,ve++;try{for(var e=this.t;void 0!==e;e=e.x)e.t.N()}finally{pe()}}}}),(Se.prototype=new be).h=function(){if(this.f&=-3,1&this.f)return!1;if(32==(36&this.f)||(this.f&=-5,this.g===_e))return!0;if(this.g=_e,this.f|=1,this.i>0&&!Ee(this))return this.f&=-2,!0;var t=me;try{we(this),me=this;var e=this.x();(16&this.f||this.v!==e||0===this.i)&&(this.v=e,this.f&=-17,this.i++)}catch(t){this.v=t,this.f|=16,this.i++}return me=t,Te(this),this.f&=-2,!0},Se.prototype.S=function(t){if(void 0===this.t){this.f|=36;for(var e=this.s;void 0!==e;e=e.n)e.S.S(e)}be.prototype.S.call(this,t)},Se.prototype.U=function(t){if(void 0!==this.t&&(be.prototype.U.call(this,t),void 0===this.t)){this.f&=-33;for(var e=this.s;void 0!==e;e=e.n)e.S.U(e)}},Se.prototype.N=function(){if(!(2&this.f)){this.f|=6;for(var t=this.t;void 0!==t;t=t.x)t.t.N()}},Se.prototype.peek=function(){if(this.h()||ue(),16&this.f)throw this.v;return this.v},Object.defineProperty(Se.prototype,"value",{get:function(){1&this.f&&ue();var t=ye(this);if(this.h(),void 0!==t&&(t.i=this.i),16&this.f)throw this.v;return this.v}}),Ce.prototype.c=function(){var t=this.S();try{if(8&this.f||void 0===this.x)return;var e=this.x();"function"==typeof e&&(this.u=e)}finally{t()}},Ce.prototype.S=function(){1&this.f&&ue(),this.f|=1,this.f&=-9,Ae(this),we(this),ve++;var t=me;return me=this,Oe.bind(this,t)},Ce.prototype.N=function(){2&this.f||(this.f|=2,this.o=fe,fe=this)},Ce.prototype.d=function(){this.f|=8,1&this.f||Le(this)};var Ie={"@dotlottie/dotlottie-js":"0.6.0","@lottiefiles/relottie":"1.0.0","@lottiefiles/relottie-style":"0.4.1","@preact/signals-core":"^1.2.3",howler:"^2.2.3","lottie-web":"^5.12.2",xstate:"^4.38.1"},Pe=(t=>(t.Complete="complete",t.DataFail="data_fail",t.DataReady="data_ready",t.Error="error",t.Frame="frame",t.Freeze="freeze",t.LoopComplete="loopComplete",t.Pause="pause",t.Play="play",t.Ready="ready",t.Stop="stop",t.VisibilityChange="visibilityChange",t))(Pe||{}),Ne=(t=>(t.Completed="completed",t.Error="error",t.Fetching="fetching",t.Frozen="frozen",t.Initial="initial",t.Loading="loading",t.Paused="paused",t.Playing="playing",t.Ready="ready",t.Stopped="stopped",t))(Ne||{}),xe=(t=>(t.Bounce="bounce",t.Normal="normal",t))(xe||{}),Me={autoplay:!1,direction:1,hover:!1,intermission:0,loop:!1,playMode:"normal",speed:1,defaultTheme:""},ke={activeStateId:"",autoplay:!1,currentState:"initial",frame:0,seeker:0,direction:1,hover:!1,loop:!1,playMode:"normal",speed:1,background:"transparent",intermission:0,currentAnimationId:void 0,visibilityPercentage:0},De=class{_lottie;_src;_animationConfig;_prevUserPlaybackOptions={};_userPlaybackOptions;_hover=!1;_loop=!1;_counter=0;_intermission=0;_counterInterval=null;_container=null;_name;_mode="normal";_background="transparent";_animation;_defaultTheme;_activeAnimationId;_currentAnimationId;_testId;_listeners=new Map;_currentState="initial";_stateBeforeFreeze="initial";state=new class{_state;_prevState;constructor(t){this._prevState=t,this._state=function(t){return new be(t)}(t)}setState(t){this._prevState=this._state.value,this._state.value=t}subscribe(t){return this._state.subscribe((e=>t(e,this._prevState)))}}(ke);_light=!1;_worker=!1;_dotLottieLoader=new class{_dotLottie;_animationsMap=new Map;_themeMap=new Map;_stateMachinesMap=new Map;_manifest;get dotLottie(){return this._dotLottie}get animationsMap(){return this._animationsMap}get themeMap(){return this._themeMap}get stateMachinesMap(){return this._stateMachinesMap}get manifest(){return this._manifest}async loadFromUrl(t){let e=await fetch(t,{method:"GET",mode:"cors"});if(!e.ok)throw new Error(`Failed to load dotLottie from ${t} with status ${e.status}`);let i=e.headers.get("content-type");if(null!=i&&i.includes("application/json")){let i=await e.json();if(!he(i))throw new Error(`Invalid lottie JSON at ${t}`);let s=function(t=""){let e=t.trim(),i=e.lastIndexOf("/"),s=e.substring(i+1),n=s.indexOf(".");return-1!==n?s.substring(0,n):s}(t);this._animationsMap.set(s,i);let n={activeAnimationId:s,animations:[{id:s}]};this._manifest=n}else{this._dotLottie=await ne(await e.arrayBuffer());let t=await ie(this._dotLottie);if(!t)throw new Error("Manifest not found");this._manifest=t}}loadFromLottieJSON(t){if(!he(t))throw new Error("Invalid lottie JSON");let e="my-animation";this._animationsMap.set(e,t);let i={activeAnimationId:e,animations:[{id:e}]};this._manifest=i}async loadFromArrayBuffer(t){this._dotLottie=await ne(t);let e=await ie(this._dotLottie);if(!e)throw new Error("Manifest not found");this._manifest=e}async getAnimation(t){if(this._animationsMap.get(t))return this._animationsMap.get(t);if(!this._dotLottie)return;let e=await async function(t,e,{inlineAssets:i}={},s){let n=`animations/${e}.json`,r=await ee(t,n,s);if(typeof r>"u")return;let o=JSON.parse(G(r,!1));if(!i)return o;let a={[e]:o};return await oe(t,a),await re(t,a),o}(this._dotLottie,t,{inlineAssets:!0});return e&&this._animationsMap.set(t,e),e}async getTheme(t){if(this._themeMap.get(t))return this._themeMap.get(t);if(!this._dotLottie)return;let e=await async function(t,e,i){let s=`themes/${e}.lss`,n=await ee(t,s,i);if(!(typeof n>"u"))return G(n,!1)}(this._dotLottie,t);return e&&this._themeMap.set(t,e),e}async getStateMachines(){if(!this._dotLottie)return;let t=await async function(t,e){let i={},s=await te(t,(t=>{let i=t.name.replace("states/","").replace(".json","");return t.name.startsWith("states/")&&(!e||e({...t,name:i}))}));for(let t in s){let e=s[t];e instanceof Uint8Array&&(i[t.replace("states/","").replace(".json","")]=G(e,!1))}return i}(this._dotLottie);for(let e in t)if(e){let i=t[e];if(i){let t=JSON.parse(i);if(t){let e=t.descriptor.id;this._stateMachinesMap.get(e)||this._stateMachinesMap.set(e,t)}}}return Array.from(this._stateMachinesMap.values())}async getStateMachine(t){if(this._stateMachinesMap.get(t))return this._stateMachinesMap.get(t);if(!this._dotLottie)return;let e=await async function(t,e,i){let s=`states/${e}.json`,n=await ee(t,s,i);return typeof n>"u"?void 0:JSON.parse(G(n,!1))}(this._dotLottie,t);return e&&this._stateMachinesMap.set(e.descriptor.id,e),e}};_activeStateId;_inInteractiveMode=!1;_scrollTicking=!1;_scrollCallback=void 0;_onShowIntersectionObserver=void 0;_visibilityPercentage=0;_audios=[];_stateMachineManager;constructor(t,e,i){this._src="string"==typeof t?t:Object.assign({},t),null!=i&&i.testId&&(this._testId=i.testId),this._defaultTheme=(null==i?void 0:i.defaultTheme)||"",this._userPlaybackOptions=this._validatePlaybackOptions(i||{}),"string"==typeof(null==i?void 0:i.activeAnimationId)&&(this._activeAnimationId=i.activeAnimationId),this._container=e||null,"string"==typeof(null==i?void 0:i.background)&&this.setBackground(i.background),typeof(null==i?void 0:i.activeStateId)<"u"&&(this._activeStateId=i.activeStateId);let{rendererSettings:s,...n}=i||{};this._animationConfig={loop:!1,autoplay:!1,renderer:"svg",rendererSettings:{clearCanvas:!0,progressiveLoad:!0,hideOnTransparent:!0,filterSize:{width:"200%",height:"200%",x:"-50%",y:"-50%"},...s},...n},null!=i&&i.light&&(this._light=i.light),null!=i&&i.worker&&(this._worker=i.worker),this._listenToHover(),this._listenToVisibilityChange()}_listenToHover(){var t,e,i,s;let n=()=>{this._hover&&"playing"!==this.currentState&&this.play()},r=()=>{this._hover&&"playing"===this.currentState&&this.stop()};null==(t=this._container)||t.removeEventListener("mouseenter",n),null==(e=this._container)||e.removeEventListener("mouseleave",r),null==(i=this._container)||i.addEventListener("mouseleave",r),null==(s=this._container)||s.addEventListener("mouseenter",n)}_onVisibilityChange(){!this._lottie||typeof document>"u"||(document.hidden&&"playing"===this.currentState?this.freeze():"frozen"===this.currentState&&this.unfreeze())}_listenToVisibilityChange(){typeof document<"u"&&typeof document.hidden<"u"&&document.addEventListener("visibilitychange",(()=>this._onVisibilityChange()))}_getOption(t){var e;if(typeof this._userPlaybackOptions[t]<"u")return this._userPlaybackOptions[t];let i=null==(e=this._dotLottieLoader.manifest)?void 0:e.animations.find((t=>t.id===this._currentAnimationId));return i&&typeof i[t]<"u"?i[t]:Me[t]}_getPlaybackOptions(){let t={};for(let e in Me)typeof Me[e]<"u"&&(t[e]=this._getOption(e));return t}_setPlayerState(t){var e,i,s;let n=t(this._getPlaybackOptions());try{Rt._parse(n)}catch{return void JSON.stringify(n,null,2)}typeof n.defaultTheme<"u"&&(this._defaultTheme=n.defaultTheme),typeof n.playMode<"u"&&(this._mode=n.playMode),typeof n.intermission<"u"&&(this._intermission=n.intermission),typeof n.hover<"u"&&(this._hover=n.hover),typeof n.loop<"u"&&(this.clearCountTimer(),this._loop=n.loop,this._counter=0,null==(e=this._lottie)||e.setLoop("number"==typeof n.loop||n.loop)),typeof n.speed<"u"&&(null==(i=this._lottie)||i.setSpeed(n.speed)),typeof n.autoplay<"u"&&this._lottie&&(this._lottie.autoplay=n.autoplay),typeof n.direction<"u"&&(null==(s=this._lottie)||s.setDirection(n.direction))}_getOptionsFromAnimation(t){let{id:e,...i}=t;return{...Me,...i}}_updateTestData(){!this._testId||!this._lottie||(window.dotLottiePlayer||(window.dotLottiePlayer={[this._testId]:{}}),window.dotLottiePlayer[this._testId]={direction:this._lottie.playDirection,currentState:this._currentState,loop:this.loop,mode:this._mode,speed:this._lottie.playSpeed})}setContainer(t){t!==this._container&&(this._container=t,this.setBackground(this._background),this._listenToHover())}get currentState(){return this._currentState}clearCountTimer(){this._counterInterval&&clearInterval(this._counterInterval)}setCurrentState(t){this._currentState=t,this._notify(),this._updateTestData()}static isPathJSON(t){var e;return"json"===(null==(e=t.split(".").pop())?void 0:e.toLowerCase())}get src(){return this._src}updateSrc(t){this._src!==t&&(this._src="string"==typeof t?t:Object.assign({},t),this._activeAnimationId=void 0,this._currentAnimationId=void 0,this.load())}get intermission(){return this._intermission}get hover(){return this._hover}setHover(t){"boolean"==typeof t&&(this._hover=t,this._userPlaybackOptions.hover=t,this._notify())}setIntermission(t){this._intermission=t,this._userPlaybackOptions.intermission=t,this._notify()}get mode(){return this._mode}get animations(){return this._dotLottieLoader.animationsMap}get themes(){return this._dotLottieLoader.themeMap}setMode(t){"string"==typeof t&&(this._mode=t,this._userPlaybackOptions.playMode=t,this._setPlayerState((()=>({playMode:t}))),this._notify(),this._updateTestData())}get container(){if(this._container)return this._container}goToAndPlay(t,e,i){this._lottie&&!["loading"].includes(this._currentState)&&(this._lottie.goToAndPlay(t,e,i),this.setCurrentState("playing"))}goToAndStop(t,e,i){this._lottie&&!["loading"].includes(this._currentState)&&(this._lottie.goToAndStop(t,e,i),this.setCurrentState("stopped"))}seek(t){if(!this._lottie||["loading"].includes(this._currentState))return;let e=t;"number"==typeof e&&(e=Math.round(e));let i=/^(\d+)(%?)$/u.exec(e.toString());if(!i)return;let s="%"===i[2]?this.totalFrames*Number(i[1])/100:i[1];void 0!==s&&(this._lottie.goToAndPlay(s,!0),"playing"===this.currentState?this.play():"frozen"===this.currentState?this.freeze():this.pause())}_areNumbersInRange(t,e){return t>=0&&t<=1&&e>=0&&e<=1}_updatePosition(t,e,i){let[s,n]=null!=t?t:[0,this.totalFrames-1],[r,o]=null!=e?e:[0,1];if(this._areNumbersInRange(r,o)){if(this.container){let{height:t,top:e}=this.container.getBoundingClientRect(),a=(window.innerHeight-e)/(window.innerHeight+t),l=s+Math.round((a-r)/(o-r)*(n-s));i&&i(a),this.goToAndStop(l,!0),(l>=n||a>=o)&&this._handleAnimationComplete()}this._scrollTicking=!1}}_requestTick(t,e,i){this._scrollTicking||(requestAnimationFrame((()=>this._updatePosition(t,e,i))),this._scrollTicking=!0)}playOnScroll(t){this.stop(),this._scrollCallback&&this.stopPlayOnScroll(),this._scrollCallback=()=>this._requestTick(null==t?void 0:t.segments,null==t?void 0:t.threshold,null==t?void 0:t.positionCallback),window.addEventListener("scroll",this._scrollCallback)}stopPlayOnScroll(){this._scrollCallback&&(window.removeEventListener("scroll",this._scrollCallback),this._scrollCallback=void 0)}stopPlayOnShow(){this._onShowIntersectionObserver&&(this._onShowIntersectionObserver.disconnect(),this._onShowIntersectionObserver=void 0)}addIntersectionObserver(t){if(!this.container)throw ae("Can't play on show, player container element not available.");let e={root:null,rootMargin:"0px",threshold:null!=t&&t.threshold?t.threshold:[0,1]};this._onShowIntersectionObserver=new IntersectionObserver((e=>{e.forEach((e=>{var i,s;this._visibilityPercentage=100*e.intersectionRatio,e.isIntersecting?(null!=t&&t.callbackOnIntersect&&t.callbackOnIntersect(this._visibilityPercentage),null==(i=this._container)||i.dispatchEvent(new Event("visibilityChange"))):null!=t&&t.callbackOnIntersect&&(t.callbackOnIntersect(0),null==(s=this._container)||s.dispatchEvent(new Event("visibilityChange")))}))}),e),this._onShowIntersectionObserver.observe(this.container)}playOnShow(t){var e;if(this.stop(),!this.container)throw ae("Can't play on show, player container element not available.");this._onShowIntersectionObserver&&this.stopPlayOnShow(),this.addIntersectionObserver({threshold:null!=(e=null==t?void 0:t.threshold)?e:[],callbackOnIntersect:t=>{0===t?this.pause():this.play()}})}_validatePlaybackOptions(t){if(!t)return{};let e={};for(let[i,s]of Object.entries(t))switch(i){case"autoplay":"boolean"==typeof s&&(e.autoplay=s);break;case"direction":"number"==typeof s&&[1,-1].includes(s)&&(e.direction=s);break;case"loop":("boolean"==typeof s||"number"==typeof s)&&(e.loop=s);break;case"playMode":"string"==typeof s&&["normal","bounce"].includes(s)&&(e.playMode=s);break;case"speed":"number"==typeof s&&(e.speed=s);break;case"themeColor":"string"==typeof s&&(e.themeColor=s);break;case"hover":"boolean"==typeof s&&(e.hover=s);break;case"intermission":"number"==typeof s&&(e.intermission=s);break;case"defaultTheme":"string"==typeof s&&(e.defaultTheme=s)}return this._requireValidPlaybackOptions(e),e}_requireAnimationsInTheManifest(){var t;if(null==(t=this._dotLottieLoader.manifest)||!t.animations.length)throw ae("No animations found in manifest.")}_requireAnimationsToBeLoaded(){if(0===this._dotLottieLoader.animationsMap.size)throw ae("No animations have been loaded.")}async play(t,e){var i,s;if(!["initial","loading"].includes(this._currentState)){if(this._requireAnimationsInTheManifest(),this._requireAnimationsToBeLoaded(),this._lottie&&!t)return-1===this._lottie.playDirection&&0===this._lottie.currentFrame?this._lottie.goToAndPlay(this._lottie.totalFrames,!0):this._lottie.play(),void this.setCurrentState("playing");if("number"==typeof t){let s=null==(i=this._dotLottieLoader.manifest)?void 0:i.animations[t];if(!s)throw ae("animation not found.");"function"==typeof e?await this.render({id:s.id,...e(this._getPlaybackOptions(),this._getOptionsFromAnimation(s))}):await this.render({id:s.id})}if("string"==typeof t){let i=null==(s=this._dotLottieLoader.manifest)?void 0:s.animations.find((e=>e.id===t));if(!i)throw ae("animation not found.");"function"==typeof e?await this.render({id:i.id,...e(this._getPlaybackOptions(),this._getOptionsFromAnimation(i))}):await this.render({id:i.id})}}}playSegments(t,e){this._lottie&&!["loading"].includes(this._currentState)&&(this._lottie.playSegments(t,e),this.setCurrentState("playing"))}resetSegments(t){this._lottie&&!["loading"].includes(this._currentState)&&this._lottie.resetSegments(t)}togglePlay(){"playing"===this.currentState?this.pause():this.play()}_getAnimationByIdOrIndex(t){var e,i;if(this._requireAnimationsInTheManifest(),this._requireAnimationsToBeLoaded(),"number"==typeof t){let i=null==(e=this._dotLottieLoader.manifest)?void 0:e.animations[t];if(!i)throw ae("animation not found.");return i}if("string"==typeof t){let e=null==(i=this._dotLottieLoader.manifest)?void 0:i.animations.find((e=>e.id===t));if(!e)throw ae("animation not found.");return e}throw ae("first param must be a number or string")}get activeAnimationId(){return this._getActiveAnimationId()}get currentAnimationId(){return this._currentAnimationId}get activeStateId(){return this._activeStateId}async _startInteractivity(t){if(this._inInteractiveMode){if(0===this._dotLottieLoader.stateMachinesMap.size&&await this._dotLottieLoader.getStateMachines(),0===this._dotLottieLoader.stateMachinesMap.size)throw ae("No interactivity states are available.");if("undefined"===t)throw ae("stateId is not specified.");this._stateMachineManager||(this._stateMachineManager=await async function(t,e){let[{DotLottieStateMachineManager:s}]=await Promise.all([i.e(8084).then(i.bind(i,8084))]);if(!t.length)throw ae("No state machines available inside this .lottie!");return new s(t,e)}(Array.from(this._dotLottieLoader.stateMachinesMap.values()),this)),this._stateMachineManager.start(t)}}enterInteractiveMode(t){var e;if(!t)throw ae("stateId must be a non-empty string.");this._inInteractiveMode||(this._prevUserPlaybackOptions={...this._userPlaybackOptions}),this._inInteractiveMode&&(null==(e=this._stateMachineManager)||e.stop()),this._activeStateId=t,this._inInteractiveMode=!0,this._startInteractivity(t)}exitInteractiveMode(){var t;this._inInteractiveMode&&(this._inInteractiveMode=!1,this._activeStateId="",null==(t=this._stateMachineManager)||t.stop(),this._userPlaybackOptions={},this._userPlaybackOptions={...this._prevUserPlaybackOptions},this._prevUserPlaybackOptions={},this.reset())}reset(){var t;let e=this._getActiveAnimationId(),i=null==(t=this._dotLottieLoader.manifest)?void 0:t.animations.find((t=>t.id===e));if(this._inInteractiveMode&&this.exitInteractiveMode(),!i)throw ae("animation not found.");this.play(e)}previous(t){if(!this._dotLottieLoader.manifest||!this._dotLottieLoader.manifest.animations.length)throw ae("manifest not found.");if(this._inInteractiveMode)return;let e=this._dotLottieLoader.manifest.animations.findIndex((t=>t.id===this._currentAnimationId));if(-1===e)throw ae("animation not found.");let i=this._dotLottieLoader.manifest.animations[(e-1+this._dotLottieLoader.manifest.animations.length)%this._dotLottieLoader.manifest.animations.length];if(!i||!i.id)throw ae("animation not found.");"function"==typeof t?this.render({id:i.id,...t(this._getPlaybackOptions(),this._getOptionsFromAnimation(i))}):this.render({id:i.id})}next(t){if(!this._dotLottieLoader.manifest||!this._dotLottieLoader.manifest.animations.length)throw ae("manifest not found.");if(this._inInteractiveMode)return;let e=this._dotLottieLoader.manifest.animations.findIndex((t=>t.id===this._currentAnimationId));if(-1===e)throw ae("animation not found.");let i=this._dotLottieLoader.manifest.animations[(e+1)%this._dotLottieLoader.manifest.animations.length];if(!i||!i.id)throw ae("animation not found.");"function"==typeof t?this.render({id:i.id,...t(this._getPlaybackOptions(),this._getOptionsFromAnimation(i))}):this.render({id:i.id})}getManifest(){return this._dotLottieLoader.manifest}resize(){this._lottie&&!["loading"].includes(this._currentState)&&this._lottie.resize()}stop(){this._lottie&&!["loading"].includes(this._currentState)&&(this.clearCountTimer(),this._counter=0,this._setPlayerState((()=>({direction:this._getOption("direction")}))),this._lottie.stop(),this.setCurrentState("stopped"))}pause(){this._lottie&&!["loading"].includes(this._currentState)&&(this.clearCountTimer(),this._lottie.pause(),this.setCurrentState("paused"))}freeze(){this._lottie&&!["loading"].includes(this._currentState)&&("frozen"!==this.currentState&&(this._stateBeforeFreeze=this.currentState),this._lottie.pause(),this.setCurrentState("frozen"))}unfreeze(){this._lottie&&!["loading"].includes(this._currentState)&&("playing"===this._stateBeforeFreeze?this.play():this.pause())}destroy(){var t,e;null!=(t=this._container)&&t.__lottie&&(this._container.__lottie.destroy(),this._container.__lottie=null),this._audios.length&&(this._audios.forEach((t=>{t.unload()})),this._audios=[]),this.clearCountTimer(),typeof document<"u"&&document.removeEventListener("visibilitychange",(()=>this._onVisibilityChange())),this._counter=0,null==(e=this._lottie)||e.destroy(),this._lottie=void 0}getAnimationInstance(){return this._lottie}static getLottieWebVersion(){return`${Ie["lottie-web"]}`}addEventListener(t,e){var i,s,n;this._listeners.has(t)||this._listeners.set(t,new Set),null==(i=this._listeners.get(t))||i.add(e);try{"complete"===t?null==(s=this._container)||s.addEventListener(t,e):null==(n=this._lottie)||n.addEventListener(t,e)}catch(t){}}getState(){var t,e,i,s,n,r,o;return{autoplay:null!=(e=null==(t=this._lottie)?void 0:t.autoplay)&&e,currentState:this._currentState,frame:this._frame,visibilityPercentage:this._visibilityPercentage,seeker:this._seeker,direction:null!=(s=null==(i=this._lottie)?void 0:i.playDirection)?s:1,hover:this._hover,loop:this._loop||!1,playMode:this._mode,speed:null!=(r=null==(n=this._lottie)?void 0:n.playSpeed)?r:1,background:this._background,intermission:this._intermission,defaultTheme:this._defaultTheme,currentAnimationId:this._currentAnimationId,activeStateId:null!=(o=this._activeStateId)?o:""}}_notify(){this.state.setState(this.getState())}get totalFrames(){var t;return(null==(t=this._lottie)?void 0:t.totalFrames)||0}get direction(){return this._lottie?this._lottie.playDirection:1}setDirection(t){this._requireValidDirection(t),this._setPlayerState((()=>({direction:t}))),this._userPlaybackOptions.direction=t}get speed(){var t;return(null==(t=this._lottie)?void 0:t.playSpeed)||1}setSpeed(t){this._requireValidSpeed(t),this._setPlayerState((()=>({speed:t}))),this._userPlaybackOptions.speed=t}get autoplay(){var t,e;return null!=(e=null==(t=this._lottie)?void 0:t.autoplay)&&e}setAutoplay(t){this._requireValidAutoplay(t),this._lottie&&!["loading"].includes(this._currentState)&&(this._setPlayerState((()=>({autoplay:t}))),this._userPlaybackOptions.autoplay=t)}toggleAutoplay(){this._lottie&&!["loading"].includes(this._currentState)&&this.setAutoplay(!this._lottie.autoplay)}get defaultTheme(){return this._defaultTheme}setDefaultTheme(t){this._setPlayerState((()=>({defaultTheme:t}))),this._userPlaybackOptions.defaultTheme=t,this._animation&&this.render()}get loop(){return this._loop}setLoop(t){this._requireValidLoop(t),this._setPlayerState((()=>({loop:t}))),this._userPlaybackOptions.loop=t}toggleLoop(){this._lottie&&!["loading"].includes(this._currentState)&&this.setLoop(!this._loop)}get background(){return this._background}setBackground(t){this._requireValidBackground(t),this._background=t,this._container&&(this._container.style.backgroundColor=t)}get _frame(){return this._lottie?"completed"===this.currentState?-1===this.direction?0:this._lottie.totalFrames:this._lottie.currentFrame:0}get _seeker(){return this._lottie?this._frame/this._lottie.totalFrames*100:0}async revertToManifestValues(t){var e;let i;i=Array.isArray(t)&&0!==t.length?t:["autoplay","defaultTheme","direction","hover","intermission","loop","playMode","speed","activeAnimationId"];let s=!1;if(i.includes("activeAnimationId")){let t=null==(e=this._dotLottieLoader.manifest)?void 0:e.activeAnimationId,i=this._getAnimationByIdOrIndex(t||0);this._activeAnimationId=t,await this._setCurrentAnimation(i.id),s=!0}i.forEach((t=>{switch(t){case"autoplay":delete this._userPlaybackOptions.autoplay,this.setAutoplay(this._getOption("autoplay"));break;case"defaultTheme":delete this._userPlaybackOptions.defaultTheme,this.setDefaultTheme(this._getOption("defaultTheme"));break;case"direction":delete this._userPlaybackOptions.direction,this.setDirection(this._getOption("direction"));break;case"hover":delete this._userPlaybackOptions.hover,this.setHover(this._getOption("hover"));break;case"intermission":delete this._userPlaybackOptions.intermission,this.setIntermission(this._getOption("intermission"));break;case"loop":delete this._userPlaybackOptions.loop,this.setLoop(this._getOption("loop"));break;case"playMode":delete this._userPlaybackOptions.playMode,this.setMode(this._getOption("playMode")),this.setDirection(this._getOption("direction"));break;case"speed":delete this._userPlaybackOptions.speed,this.setSpeed(this._getOption("speed"))}})),s&&this.render()}removeEventListener(t,e){var i,s,n;try{"complete"===t?null==(i=this._container)||i.removeEventListener(t,e):null==(s=this._lottie)||s.removeEventListener(t,e),null==(n=this._listeners.get(t))||n.delete(e)}catch(t){}}_handleAnimationComplete(){var t;"number"==typeof this._loop&&this.stop();let e=-1===this.direction?0:this.totalFrames;this.goToAndStop(e,!0),this._counter=0,this.clearCountTimer(),this.setCurrentState("completed"),null==(t=this._container)||t.dispatchEvent(new Event("complete"))}addEventListeners(){var t;if(this._lottie&&!["loading"].includes(this._currentState)){this._lottie.addEventListener("enterFrame",(()=>{var t;this._lottie&&(0===Math.floor(this._lottie.currentFrame)&&-1===this.direction&&(null==(t=this._container)||t.dispatchEvent(new Event("complete")),this.loop||this.setCurrentState("completed")),this._notify())})),this._lottie.addEventListener("loopComplete",(()=>{var t;if(!this._lottie)return;null==(t=this._container)||t.dispatchEvent(new Event("loopComplete")),this.intermission>0&&this.pause();let e=this._lottie.playDirection;if("number"==typeof this._loop&&this._loop>0&&(this._counter+="bounce"===this._mode?.5:1,this._counter>=this._loop))return void this._handleAnimationComplete();"bounce"===this._mode&&"number"==typeof e&&(e=-1*Number(e));let i=-1===e?this._lottie.totalFrames-1:0;this.intermission?(this.goToAndPlay(i,!0),this.pause(),this._counterInterval=window.setTimeout((()=>{this._lottie&&(this._setPlayerState((()=>({direction:e}))),this.goToAndPlay(i,!0))}),this._intermission)):(this._setPlayerState((()=>({direction:e}))),this.goToAndPlay(-1===e?this.totalFrames-1:0,!0))})),this._lottie.addEventListener("complete",(()=>{if(this._lottie&&!1===this._loop&&"bounce"===this._mode){if(this._counter+=.5,this._counter>=1)return void this._handleAnimationComplete();this._counterInterval=window.setTimeout((()=>{if(!this._lottie)return;let t=this._lottie.playDirection;"bounce"===this._mode&&"number"==typeof t&&(t=-1*Number(t));let e=-1===t?this.totalFrames-1:0;this._setPlayerState((()=>({direction:t}))),this.goToAndPlay(e,!0)}),this._intermission)}else this._handleAnimationComplete()}));for(let[e,i]of this._listeners)if("complete"===e)for(let s of i)null==(t=this._container)||t.addEventListener(e,s);else for(let t of i)this._lottie.addEventListener(e,t)}}async _setCurrentAnimation(t){this._currentState="loading";let e=await this._dotLottieLoader.getAnimation(t);this._currentAnimationId=t,this._animation=e,this._currentState="ready"}async _getAudioFactory(){if(this._animation&&function(t){let e=t.assets;return!!e&&e.some((t=>Qt(t)))}(this._animation)){let{DotLottieAudio:t}=await i.e(487).then(i.bind(i,487));return e=>{let i=new t({src:[e]});return this._audios.push(i),i}}return null}async render(t){var e,s,n,r,o,a,l,d,h,c,u,p,m,f,v,g,_,y;if(null!=t&&t.id)await this._setCurrentAnimation(t.id);else if(!this._animation)throw ae("no animation selected");let b=null!=(e=Me.loop)&&e,E=null!=(s=Me.autoplay)&&s,w=null!=(n=Me.playMode)?n:"normal",T=null!=(r=Me.intermission)?r:0,S=null!=(o=Me.hover)&&o,A=null!=(a=Me.direction)?a:1,L=null!=(l=Me.speed)?l:1,O=null!=(d=Me.defaultTheme)?d:"";b=null!=(h=null==t?void 0:t.loop)?h:this._getOption("loop"),E=null!=(c=null==t?void 0:t.autoplay)?c:this._getOption("autoplay"),w=null!=(u=null==t?void 0:t.playMode)?u:this._getOption("playMode"),T=null!=(p=null==t?void 0:t.intermission)?p:this._getOption("intermission"),S=null!=(m=null==t?void 0:t.hover)?m:this._getOption("hover"),A=null!=(f=null==t?void 0:t.direction)?f:this._getOption("direction"),L=null!=(v=null==t?void 0:t.speed)?v:this._getOption("speed"),O=null!=(g=null==t?void 0:t.defaultTheme)?g:this._getOption("defaultTheme");let C={...this._animationConfig,autoplay:!S&&E,loop:"number"==typeof b||b,renderer:this._worker?"svg":null!=(_=this._animationConfig.renderer)?_:"svg"},[I,P,N]=await Promise.all([this._dotLottieLoader.getTheme(O),this._getLottiePlayerInstance(),this._getAudioFactory()]);I&&this._animation?this._animation=await async function(t,e){let[{relottie:s},{default:n}]=await Promise.all([i.e(2782).then(i.bind(i,2782)),i.e(2489).then(i.bind(i,2489))]),r=await s().use(n,{lss:e}).process(JSON.stringify(t));return JSON.parse(r.value)}(this._animation,I):this._animation=await this._dotLottieLoader.getAnimation(null!=(y=this._currentAnimationId)?y:""),!this._activeStateId||this._inInteractiveMode?(this.destroy(),this._setPlayerState((()=>({defaultTheme:O,playMode:w,intermission:T,hover:S,loop:b}))),this._lottie=N?P.loadAnimation({...C,container:this._container,animationData:this._animation,audioFactory:N}):P.loadAnimation({...C,container:this._container,animationData:this._animation}),typeof this._lottie.resetSegments>"u"&&(this._lottie.resetSegments=()=>{var t;null==(t=this._lottie)||t.playSegments([0,this._lottie.totalFrames],!0)}),this.addEventListeners(),this._container&&(this._container.__lottie=this._lottie),this._setPlayerState((()=>({direction:A,speed:L}))),E&&!S&&this.play(),this._updateTestData()):this.enterInteractiveMode(this._activeStateId)}async _getLottiePlayerInstance(){var t;let e,s=null!=(t=this._animationConfig.renderer)?t:"svg";if(this._worker)return e=await i.e(3595).then(i.bind(i,3595)),e.default;switch(s){case"svg":e=this._light?await i.e(3044).then(i.bind(i,3044)):await i.e(8471).then(i.bind(i,8471));break;case"canvas":e=this._light?await i.e(3976).then(i.bind(i,3976)):await i.e(3995).then(i.bind(i,3995));break;case"html":e=this._light?await i.e(7967).then(i.bind(i,7967)):await i.e(6331).then(i.bind(i,6331));break;default:throw new Error(`Invalid renderer: ${s}`)}return e.default}_getActiveAnimationId(){var t,e,i,s;let n=this._dotLottieLoader.manifest;return null!=(s=null!=(i=null!=(t=this._activeAnimationId)?t:null==n?void 0:n.activeAnimationId)?i:null==(e=null==n?void 0:n.animations[0])?void 0:e.id)?s:void 0}async load(t){if("loading"!==this._currentState)try{if(this.setCurrentState("loading"),"string"==typeof this._src)if(function(t){try{return he(JSON.parse(t))}catch{return!1}}(this._src)){let t=JSON.parse(this._src);this._dotLottieLoader.loadFromLottieJSON(t)}else{let t=new URL(this._src,window.location.href);await this._dotLottieLoader.loadFromUrl(t.toString())}else{if("object"!=typeof this._src||!he(this._src))throw ae("Invalid src provided");this._dotLottieLoader.loadFromLottieJSON(this._src)}if(!this._dotLottieLoader.manifest)throw ae("No manifest found");let e=this._getActiveAnimationId();if(!e)throw ae("No active animation found");await this._setCurrentAnimation(e),await this.render(t)}catch(t){this.setCurrentState("error"),t instanceof Error&&t.message}}setErrorState(t){this.setCurrentState("error")}_requireValidDirection(t){if(-1!==t&&1!==t)throw ae("Direction can only be -1 (backwards) or 1 (forwards)")}_requireValidIntermission(t){if(t<0||!Number.isInteger(t))throw ae("intermission must be a positive number")}_requireValidLoop(t){if("number"==typeof t&&(!Number.isInteger(t)||t<0))throw ae("loop must be a positive number or boolean")}_requireValidSpeed(t){if("number"!=typeof t)throw ae("speed must be a number")}_requireValidBackground(t){if("string"!=typeof t)throw ae("background must be a string")}_requireValidAutoplay(t){if("boolean"!=typeof t)throw ae("autoplay must be a boolean")}_requireValidPlaybackOptions(t){t.direction&&this._requireValidDirection(t.direction),t.intermission&&this._requireValidIntermission(t.intermission),t.loop&&this._requireValidLoop(t.loop),t.speed&&this._requireValidSpeed(t.speed)}}},7493:function(t,e,i){"use strict";i.d(e,{a:function(){return d},b:function(){return h}});var s=Object.create,n=Object.defineProperty,r=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.getPrototypeOf,l=Object.prototype.hasOwnProperty,d=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),h=(t,e,i)=>(i=null!=t?s(a(t)):{},((t,e,i,s)=>{if(e&&"object"==typeof e||"function"==typeof e)for(let a of o(e))!l.call(t,a)&&a!==i&&n(t,a,{get:()=>e[a],enumerable:!(s=r(e,a))||s.enumerable});return t})(!e&&t&&t.__esModule?i:n(i,"default",{value:t,enumerable:!0}),t))},5031:function(t,e,i){"use strict";i.d(e,{a:function(){return r}});var s=Object.defineProperty,n=Object.getOwnPropertyDescriptor,r=(t,e,i,r)=>{for(var o,a=r>1?void 0:r?n(e,i):e,l=t.length-1;l>=0;l--)(o=t[l])&&(a=(r?o(e,i,a):o(a))||a);return r&&a&&s(e,i,a),a}}},s={};function n(t){var e=s[t];if(void 0!==e)return e.exports;var r=s[t]={exports:{}};return i[t].call(r.exports,r,r.exports,n),r.exports}n.m=i,n.d=function(t,e){for(var i in e)n.o(e,i)&&!n.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},n.f={},n.e=function(t){return Promise.all(Object.keys(n.f).reduce((function(e,i){return n.f[i](t,e),e}),[]))},n.u=function(t){return"js/"+t+".js"},n.miniCssF=function(t){},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},t={},e="votesaveamerica:",n.l=function(i,s,r,o){if(t[i])t[i].push(s);else{var a,l;if(void 0!==r)for(var d=document.getElementsByTagName("script"),h=0;h<d.length;h++){var c=d[h];if(c.getAttribute("src")==i||c.getAttribute("data-webpack")==e+r){a=c;break}}a||(l=!0,(a=document.createElement("script")).charset="utf-8",a.timeout=120,n.nc&&a.setAttribute("nonce",n.nc),a.setAttribute("data-webpack",e+r),a.src=i),t[i]=[s];var u=function(e,s){a.onerror=a.onload=null,clearTimeout(p);var n=t[i];if(delete t[i],a.parentNode&&a.parentNode.removeChild(a),n&&n.forEach((function(t){return t(s)})),e)return e(s)},p=setTimeout(u.bind(null,void 0,{type:"timeout",target:a}),12e4);a.onerror=u.bind(null,a.onerror),a.onload=u.bind(null,a.onload),l&&document.head.appendChild(a)}},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},function(){var t;n.g.importScripts&&(t=n.g.location+"");var e=n.g.document;if(!t&&e&&(e.currentScript&&(t=e.currentScript.src),!t)){var i=e.getElementsByTagName("script");if(i.length)for(var s=i.length-1;s>-1&&!t;)t=i[s--].src}if(!t)throw new Error("Automatic publicPath is not supported in this browser");t=t.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),n.p=t+"../"}(),function(){var t={2143:0,9296:0};n.f.j=function(e,i){var s=n.o(t,e)?t[e]:void 0;if(0!==s)if(s)i.push(s[2]);else{var r=new Promise((function(i,n){s=t[e]=[i,n]}));i.push(s[2]=r);var o=n.p+n.u(e),a=new Error;n.l(o,(function(i){if(n.o(t,e)&&(0!==(s=t[e])&&(t[e]=void 0),s)){var r=i&&("load"===i.type?"missing":i.type),o=i&&i.target&&i.target.src;a.message="Loading chunk "+e+" failed.\n("+r+": "+o+")",a.name="ChunkLoadError",a.type=r,a.request=o,s[1](a)}}),"chunk-"+e,e)}};var e=function(e,i){var s,r,o=i[0],a=i[1],l=i[2],d=0;if(o.some((function(e){return 0!==t[e]}))){for(s in a)n.o(a,s)&&(n.m[s]=a[s]);if(l)l(n)}for(e&&e(i);d<o.length;d++)r=o[d],n.o(t,r)&&t[r]&&t[r][0](),t[r]=0},i=self.webpackChunkvotesaveamerica=self.webpackChunkvotesaveamerica||[];i.forEach(e.bind(null,0)),i.push=e.bind(null,i.push.bind(i))}(),function(){"use strict";var t,e=window,i=e.ShadowRoot&&(void 0===e.ShadyCSS||e.ShadyCSS.nativeShadow)&&"adoptedStyleSheets"in Document.prototype&&"replace"in CSSStyleSheet.prototype,s=Symbol(),r=new WeakMap,o=class{constructor(t,e,i){if(this._$cssResult$=!0,i!==s)throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");this.cssText=t,this.t=e}get styleSheet(){let t=this.o,e=this.t;if(i&&void 0===t){let i=void 0!==e&&1===e.length;i&&(t=r.get(e)),void 0===t&&((this.o=t=new CSSStyleSheet).replaceSync(this.cssText),i&&r.set(e,t))}return t}toString(){return this.cssText}},a=i?t=>t:t=>t instanceof CSSStyleSheet?(t=>{let e="";for(let i of t.cssRules)e+=i.cssText;return(t=>new o("string"==typeof t?t:t+"",void 0,s))(e)})(t):t,l=window,d=l.trustedTypes,h=d?d.emptyScript:"",c=l.reactiveElementPolyfillSupport,u={toAttribute(t,e){switch(e){case Boolean:t=t?h:null;break;case Object:case Array:t=null==t?t:JSON.stringify(t)}return t},fromAttribute(t,e){let i=t;switch(e){case Boolean:i=null!==t;break;case Number:i=null===t?null:Number(t);break;case Object:case Array:try{i=JSON.parse(t)}catch{i=null}}return i}},p=(t,e)=>e!==t&&(e==e||t==t),m={attribute:!0,type:String,converter:u,reflect:!1,hasChanged:p},f="finalized",v=class extends HTMLElement{constructor(){super(),this._$Ei=new Map,this.isUpdatePending=!1,this.hasUpdated=!1,this._$El=null,this._$Eu()}static addInitializer(t){var e;this.finalize(),(null!==(e=this.h)&&void 0!==e?e:this.h=[]).push(t)}static get observedAttributes(){this.finalize();let t=[];return this.elementProperties.forEach(((e,i)=>{let s=this._$Ep(i,e);void 0!==s&&(this._$Ev.set(s,i),t.push(s))})),t}static createProperty(t,e=m){if(e.state&&(e.attribute=!1),this.finalize(),this.elementProperties.set(t,e),!e.noAccessor&&!this.prototype.hasOwnProperty(t)){let i="symbol"==typeof t?Symbol():"__"+t,s=this.getPropertyDescriptor(t,i,e);void 0!==s&&Object.defineProperty(this.prototype,t,s)}}static getPropertyDescriptor(t,e,i){return{get(){return this[e]},set(s){let n=this[t];this[e]=s,this.requestUpdate(t,n,i)},configurable:!0,enumerable:!0}}static getPropertyOptions(t){return this.elementProperties.get(t)||m}static finalize(){if(this.hasOwnProperty(f))return!1;this[f]=!0;let t=Object.getPrototypeOf(this);if(t.finalize(),void 0!==t.h&&(this.h=[...t.h]),this.elementProperties=new Map(t.elementProperties),this._$Ev=new Map,this.hasOwnProperty("properties")){let t=this.properties,e=[...Object.getOwnPropertyNames(t),...Object.getOwnPropertySymbols(t)];for(let i of e)this.createProperty(i,t[i])}return this.elementStyles=this.finalizeStyles(this.styles),!0}static finalizeStyles(t){let e=[];if(Array.isArray(t)){let i=new Set(t.flat(1/0).reverse());for(let t of i)e.unshift(a(t))}else void 0!==t&&e.push(a(t));return e}static _$Ep(t,e){let i=e.attribute;return!1===i?void 0:"string"==typeof i?i:"string"==typeof t?t.toLowerCase():void 0}_$Eu(){var t;this._$E_=new Promise((t=>this.enableUpdating=t)),this._$AL=new Map,this._$Eg(),this.requestUpdate(),null===(t=this.constructor.h)||void 0===t||t.forEach((t=>t(this)))}addController(t){var e,i;(null!==(e=this._$ES)&&void 0!==e?e:this._$ES=[]).push(t),void 0!==this.renderRoot&&this.isConnected&&(null===(i=t.hostConnected)||void 0===i||i.call(t))}removeController(t){var e;null===(e=this._$ES)||void 0===e||e.splice(this._$ES.indexOf(t)>>>0,1)}_$Eg(){this.constructor.elementProperties.forEach(((t,e)=>{this.hasOwnProperty(e)&&(this._$Ei.set(e,this[e]),delete this[e])}))}createRenderRoot(){var t;let s=null!==(t=this.shadowRoot)&&void 0!==t?t:this.attachShadow(this.constructor.shadowRootOptions);return((t,s)=>{i?t.adoptedStyleSheets=s.map((t=>t instanceof CSSStyleSheet?t:t.styleSheet)):s.forEach((i=>{let s=document.createElement("style"),n=e.litNonce;void 0!==n&&s.setAttribute("nonce",n),s.textContent=i.cssText,t.appendChild(s)}))})(s,this.constructor.elementStyles),s}connectedCallback(){var t;void 0===this.renderRoot&&(this.renderRoot=this.createRenderRoot()),this.enableUpdating(!0),null===(t=this._$ES)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostConnected)||void 0===e?void 0:e.call(t)}))}enableUpdating(t){}disconnectedCallback(){var t;null===(t=this._$ES)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostDisconnected)||void 0===e?void 0:e.call(t)}))}attributeChangedCallback(t,e,i){this._$AK(t,i)}_$EO(t,e,i=m){var s;let n=this.constructor._$Ep(t,i);if(void 0!==n&&!0===i.reflect){let r=(void 0!==(null===(s=i.converter)||void 0===s?void 0:s.toAttribute)?i.converter:u).toAttribute(e,i.type);this._$El=t,null==r?this.removeAttribute(n):this.setAttribute(n,r),this._$El=null}}_$AK(t,e){var i;let s=this.constructor,n=s._$Ev.get(t);if(void 0!==n&&this._$El!==n){let t=s.getPropertyOptions(n),r="function"==typeof t.converter?{fromAttribute:t.converter}:void 0!==(null===(i=t.converter)||void 0===i?void 0:i.fromAttribute)?t.converter:u;this._$El=n,this[n]=r.fromAttribute(e,t.type),this._$El=null}}requestUpdate(t,e,i){let s=!0;void 0!==t&&(((i=i||this.constructor.getPropertyOptions(t)).hasChanged||p)(this[t],e)?(this._$AL.has(t)||this._$AL.set(t,e),!0===i.reflect&&this._$El!==t&&(void 0===this._$EC&&(this._$EC=new Map),this._$EC.set(t,i))):s=!1),!this.isUpdatePending&&s&&(this._$E_=this._$Ej())}async _$Ej(){this.isUpdatePending=!0;try{await this._$E_}catch(t){Promise.reject(t)}let t=this.scheduleUpdate();return null!=t&&await t,!this.isUpdatePending}scheduleUpdate(){return this.performUpdate()}performUpdate(){var t;if(!this.isUpdatePending)return;this.hasUpdated,this._$Ei&&(this._$Ei.forEach(((t,e)=>this[e]=t)),this._$Ei=void 0);let e=!1,i=this._$AL;try{e=this.shouldUpdate(i),e?(this.willUpdate(i),null===(t=this._$ES)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostUpdate)||void 0===e?void 0:e.call(t)})),this.update(i)):this._$Ek()}catch(t){throw e=!1,this._$Ek(),t}e&&this._$AE(i)}willUpdate(t){}_$AE(t){var e;null===(e=this._$ES)||void 0===e||e.forEach((t=>{var e;return null===(e=t.hostUpdated)||void 0===e?void 0:e.call(t)})),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(t)),this.updated(t)}_$Ek(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._$E_}shouldUpdate(t){return!0}update(t){void 0!==this._$EC&&(this._$EC.forEach(((t,e)=>this._$EO(e,this[e],t))),this._$EC=void 0),this._$Ek()}updated(t){}firstUpdated(t){}};v[f]=!0,v.elementProperties=new Map,v.elementStyles=[],v.shadowRootOptions={mode:"open"},null==c||c({ReactiveElement:v}),(null!==(t=l.reactiveElementVersions)&&void 0!==t?t:l.reactiveElementVersions=[]).push("1.6.3");var g,_,y=window,b=y.trustedTypes,E=b?b.createPolicy("lit-html",{createHTML:t=>t}):void 0,w="$lit$",T=`lit$${(Math.random()+"").slice(9)}$`,S="?"+T,A=`<${S}>`,L=document,O=()=>L.createComment(""),C=t=>null===t||"object"!=typeof t&&"function"!=typeof t,I=Array.isArray,P="[ \t\n\f\r]",N=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,x=/-->/g,M=/>/g,k=RegExp(`>|${P}(?:([^\\s"'>=/]+)(${P}*=${P}*(?:[^ \t\n\f\r"'\`<>=]|("|')|))|$)`,"g"),D=/'/g,R=/"/g,$=/^(?:script|style|textarea|title)$/i,F=(_=1,(t,...e)=>({_$litType$:_,strings:t,values:e})),H=Symbol.for("lit-noChange"),U=Symbol.for("lit-nothing"),z=new WeakMap,B=L.createTreeWalker(L,129,null,!1);function V(t,e){if(!Array.isArray(t)||!t.hasOwnProperty("raw"))throw Error("invalid template strings array");return void 0!==E?E.createHTML(e):e}var q=class t{constructor({strings:e,_$litType$:i},s){let n;this.parts=[];let r=0,o=0,a=e.length-1,l=this.parts,[d,h]=((t,e)=>{let i,s=t.length-1,n=[],r=2===e?"<svg>":"",o=N;for(let e=0;e<s;e++){let s,a,l=t[e],d=-1,h=0;for(;h<l.length&&(o.lastIndex=h,a=o.exec(l),null!==a);)h=o.lastIndex,o===N?"!--"===a[1]?o=x:void 0!==a[1]?o=M:void 0!==a[2]?($.test(a[2])&&(i=RegExp("</"+a[2],"g")),o=k):void 0!==a[3]&&(o=k):o===k?">"===a[0]?(o=null!=i?i:N,d=-1):void 0===a[1]?d=-2:(d=o.lastIndex-a[2].length,s=a[1],o=void 0===a[3]?k:'"'===a[3]?R:D):o===R||o===D?o=k:o===x||o===M?o=N:(o=k,i=void 0);let c=o===k&&t[e+1].startsWith("/>")?" ":"";r+=o===N?l+A:d>=0?(n.push(s),l.slice(0,d)+w+l.slice(d)+T+c):l+T+(-2===d?(n.push(void 0),e):c)}return[V(t,r+(t[s]||"<?>")+(2===e?"</svg>":"")),n]})(e,i);if(this.el=t.createElement(d,s),B.currentNode=this.el.content,2===i){let t=this.el.content,e=t.firstChild;e.remove(),t.append(...e.childNodes)}for(;null!==(n=B.nextNode())&&l.length<a;){if(1===n.nodeType){if(n.hasAttributes()){let t=[];for(let e of n.getAttributeNames())if(e.endsWith(w)||e.startsWith(T)){let i=h[o++];if(t.push(e),void 0!==i){let t=n.getAttribute(i.toLowerCase()+w).split(T),e=/([.?@])?(.*)/.exec(i);l.push({type:1,index:r,name:e[2],strings:t,ctor:"."===e[1]?j:"?"===e[1]?Z:"@"===e[1]?X:K})}else l.push({type:6,index:r})}for(let e of t)n.removeAttribute(e)}if($.test(n.tagName)){let t=n.textContent.split(T),e=t.length-1;if(e>0){n.textContent=b?b.emptyScript:"";for(let i=0;i<e;i++)n.append(t[i],O()),B.nextNode(),l.push({type:2,index:++r});n.append(t[e],O())}}}else if(8===n.nodeType)if(n.data===S)l.push({type:2,index:r});else{let t=-1;for(;-1!==(t=n.data.indexOf(T,t+1));)l.push({type:7,index:r}),t+=T.length-1}r++}}static createElement(t,e){let i=L.createElement("template");return i.innerHTML=t,i}};function G(t,e,i=t,s){var n,r,o,a;if(e===H)return e;let l=void 0!==s?null===(n=i._$Co)||void 0===n?void 0:n[s]:i._$Cl,d=C(e)?void 0:e._$litDirective$;return(null==l?void 0:l.constructor)!==d&&(null===(r=null==l?void 0:l._$AO)||void 0===r||r.call(l,!1),void 0===d?l=void 0:(l=new d(t),l._$AT(t,i,s)),void 0!==s?(null!==(o=(a=i)._$Co)&&void 0!==o?o:a._$Co=[])[s]=l:i._$Cl=l),void 0!==l&&(e=G(t,l._$AS(t,e.values),l,s)),e}var W=class t{constructor(t,e,i,s){var n;this.type=2,this._$AH=U,this._$AN=void 0,this._$AA=t,this._$AB=e,this._$AM=i,this.options=s,this._$Cp=null===(n=null==s?void 0:s.isConnected)||void 0===n||n}get _$AU(){var t,e;return null!==(e=null===(t=this._$AM)||void 0===t?void 0:t._$AU)&&void 0!==e?e:this._$Cp}get parentNode(){let t=this._$AA.parentNode,e=this._$AM;return void 0!==e&&11===(null==t?void 0:t.nodeType)&&(t=e.parentNode),t}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(t,e=this){t=G(this,t,e),C(t)?t===U||null==t||""===t?(this._$AH!==U&&this._$AR(),this._$AH=U):t!==this._$AH&&t!==H&&this._(t):void 0!==t._$litType$?this.g(t):void 0!==t.nodeType?this.$(t):(t=>I(t)||"function"==typeof(null==t?void 0:t[Symbol.iterator]))(t)?this.T(t):this._(t)}k(t){return this._$AA.parentNode.insertBefore(t,this._$AB)}$(t){this._$AH!==t&&(this._$AR(),this._$AH=this.k(t))}_(t){this._$AH!==U&&C(this._$AH)?this._$AA.nextSibling.data=t:this.$(L.createTextNode(t)),this._$AH=t}g(t){var e;let{values:i,_$litType$:s}=t,n="number"==typeof s?this._$AC(t):(void 0===s.el&&(s.el=q.createElement(V(s.h,s.h[0]),this.options)),s);if((null===(e=this._$AH)||void 0===e?void 0:e._$AD)===n)this._$AH.v(i);else{let t=new class{constructor(t,e){this._$AV=[],this._$AN=void 0,this._$AD=t,this._$AM=e}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}u(t){var e;let{el:{content:i},parts:s}=this._$AD,n=(null!==(e=null==t?void 0:t.creationScope)&&void 0!==e?e:L).importNode(i,!0);B.currentNode=n;let r=B.nextNode(),o=0,a=0,l=s[0];for(;void 0!==l;){if(o===l.index){let e;2===l.type?e=new W(r,r.nextSibling,this,t):1===l.type?e=new l.ctor(r,l.name,l.strings,this,t):6===l.type&&(e=new J(r,this,t)),this._$AV.push(e),l=s[++a]}o!==(null==l?void 0:l.index)&&(r=B.nextNode(),o++)}return B.currentNode=L,n}v(t){let e=0;for(let i of this._$AV)void 0!==i&&(void 0!==i.strings?(i._$AI(t,i,e),e+=i.strings.length-2):i._$AI(t[e])),e++}}(n,this),e=t.u(this.options);t.v(i),this.$(e),this._$AH=t}}_$AC(t){let e=z.get(t.strings);return void 0===e&&z.set(t.strings,e=new q(t)),e}T(e){I(this._$AH)||(this._$AH=[],this._$AR());let i,s=this._$AH,n=0;for(let r of e)n===s.length?s.push(i=new t(this.k(O()),this.k(O()),this,this.options)):i=s[n],i._$AI(r),n++;n<s.length&&(this._$AR(i&&i._$AB.nextSibling,n),s.length=n)}_$AR(t=this._$AA.nextSibling,e){var i;for(null===(i=this._$AP)||void 0===i||i.call(this,!1,!0,e);t&&t!==this._$AB;){let e=t.nextSibling;t.remove(),t=e}}setConnected(t){var e;void 0===this._$AM&&(this._$Cp=t,null===(e=this._$AP)||void 0===e||e.call(this,t))}},K=class{constructor(t,e,i,s,n){this.type=1,this._$AH=U,this._$AN=void 0,this.element=t,this.name=e,this._$AM=s,this.options=n,i.length>2||""!==i[0]||""!==i[1]?(this._$AH=Array(i.length-1).fill(new String),this.strings=i):this._$AH=U}get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}_$AI(t,e=this,i,s){let n=this.strings,r=!1;if(void 0===n)t=G(this,t,e,0),r=!C(t)||t!==this._$AH&&t!==H,r&&(this._$AH=t);else{let s,o,a=t;for(t=n[0],s=0;s<n.length-1;s++)o=G(this,a[i+s],e,s),o===H&&(o=this._$AH[s]),r||(r=!C(o)||o!==this._$AH[s]),o===U?t=U:t!==U&&(t+=(null!=o?o:"")+n[s+1]),this._$AH[s]=o}r&&!s&&this.j(t)}j(t){t===U?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,null!=t?t:"")}},j=class extends K{constructor(){super(...arguments),this.type=3}j(t){this.element[this.name]=t===U?void 0:t}},Y=b?b.emptyScript:"",Z=class extends K{constructor(){super(...arguments),this.type=4}j(t){t&&t!==U?this.element.setAttribute(this.name,Y):this.element.removeAttribute(this.name)}},X=class extends K{constructor(t,e,i,s,n){super(t,e,i,s,n),this.type=5}_$AI(t,e=this){var i;if((t=null!==(i=G(this,t,e,0))&&void 0!==i?i:U)===H)return;let s=this._$AH,n=t===U&&s!==U||t.capture!==s.capture||t.once!==s.once||t.passive!==s.passive,r=t!==U&&(s===U||n);n&&this.element.removeEventListener(this.name,this,s),r&&this.element.addEventListener(this.name,this,t),this._$AH=t}handleEvent(t){var e,i;"function"==typeof this._$AH?this._$AH.call(null!==(i=null===(e=this.options)||void 0===e?void 0:e.host)&&void 0!==i?i:this.element,t):this._$AH.handleEvent(t)}},J=class{constructor(t,e,i){this.element=t,this.type=6,this._$AN=void 0,this._$AM=e,this.options=i}get _$AU(){return this._$AM._$AU}_$AI(t){G(this,t)}},Q=y.litHtmlPolyfillSupport;null==Q||Q(q,W),(null!==(g=y.litHtmlVersions)&&void 0!==g?g:y.litHtmlVersions=[]).push("2.8.0");var tt,et,it=class extends v{constructor(){super(...arguments),this.renderOptions={host:this},this._$Do=void 0}createRenderRoot(){var t,e;let i=super.createRenderRoot();return null!==(t=(e=this.renderOptions).renderBefore)&&void 0!==t||(e.renderBefore=i.firstChild),i}update(t){let e=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(t),this._$Do=((t,e,i)=>{var s,n;let r=null!==(s=null==i?void 0:i.renderBefore)&&void 0!==s?s:e,o=r._$litPart$;if(void 0===o){let t=null!==(n=null==i?void 0:i.renderBefore)&&void 0!==n?n:null;r._$litPart$=o=new W(e.insertBefore(O(),t),t,void 0,null!=i?i:{})}return o._$AI(t),o})(e,this.renderRoot,this.renderOptions)}connectedCallback(){var t;super.connectedCallback(),null===(t=this._$Do)||void 0===t||t.setConnected(!0)}disconnectedCallback(){var t;super.disconnectedCallback(),null===(t=this._$Do)||void 0===t||t.setConnected(!1)}render(){return H}};it.finalized=!0,it._$litElement$=!0,null===(tt=globalThis.litElementHydrateSupport)||void 0===tt||tt.call(globalThis,{LitElement:it});var st=globalThis.litElementPolyfillSupport;null==st||st({LitElement:it}),(null!==(et=globalThis.litElementVersions)&&void 0!==et?et:globalThis.litElementVersions=[]).push("3.3.3");var nt=((t,...e)=>{let i=1===t.length?t[0]:e.reduce(((e,i,s)=>e+(t=>{if(!0===t._$cssResult$)return t.cssText;if("number"==typeof t)return t;throw Error("Value passed to 'css' function must be a 'css' function result: "+t+". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.")})(i)+t[s+1]),t[0]);return new o(i,t,s)})`
  @font-face {
    font-family: 'Karla';
    font-weight: regular;
    src: url('./fonts/Karla-regular.woff') format('woff');
  }

  * {
    box-sizing: border-box;
  }

  :host {
    --lottie-player-toolbar-height: 35px;
    --lottie-player-toolbar-background-color: transparent;
    --lottie-player-toolbar-hover-background-color: #f3f6f8;
    --lottie-player-toolbar-icon-color: #20272c;
    --lottie-player-toolbar-icon-hover-color: #f3f6f8;
    --lottie-player-toolbar-icon-active-color: #00ddb3;
    --lottie-player-seeker-track-color: #00ddb3;
    --lottie-player-seeker-accent-color: #00c1a2;
    --lottie-player-seeker-thumb-color: #00c1a2;
    --lottie-player-options-separator: #d9e0e6;

    display: block;
    width: 100%;
    height: 100%;

    font-family: 'Karla', sans-serif;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .active {
    color: var(--lottie-player-toolbar-icon-active-color) !important;
  }

  .main {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
  }

  .animation {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
  }
  .animation.controls {
    height: calc(100% - var(--lottie-player-toolbar-height));
  }

  .toolbar {
    display: flex;
    align-items: center;
    justify-items: center;
    background-color: var(--lottie-player-toolbar-background-color);
    margin: 0 8px;
    height: var(--lottie-player-toolbar-height);
  }

  .btn-spacing-left {
    margin-right: 4px;
    margin-left: 8px;
  }

  .btn-spacing-center {
    margin-right: 4px;
    margin-left: 4px;
  }

  .btn-spacing-right {
    margin-right: 8px;
    margin-left: 4px;
  }

  .toolbar button {
    color: #20272c;
    cursor: pointer;
    fill: var(--lottie-player-toolbar-icon-color);
    display: flex;
    background: none;
    border: 0px;
    border-radius: 4px;
    padding: 4px;
    outline: none;
    width: 24px;
    height: 24px;
    align-items: center;
  }

  .toolbar button:hover {
    background-color: var(--lottie-player-toolbar-icon-hover-color);
    border-style: solid;
    border-radius: 2px;
  }

  .toolbar button.active {
    fill: var(--lottie-player-toolbar-icon-active-color);
  }

  .toolbar button.active:hover {
    fill: var(--lottie-player-toolbar-icon-hover-color);
    border-radius: 4px;
  }

  .toolbar button:focus-visible {
    outline: 2px solid var(--lottie-player-toolbar-icon-active-color);
    border-radius: 4px;
    box-sizing: border-box;
  }

  .toolbar button svg {
    width: 16px;
    height: 16px;
  }

  .toolbar button.disabled svg {
    display: none;
  }

  .popover {
    position: absolute;
    bottom: 40px;
    left: calc(100% - 239px);
    width: 224px;
    min-height: 84px;
    max-height: 300px;
    background-color: #ffffff;
    box-shadow: 0px 8px 48px 0px rgba(243, 246, 248, 0.15), 0px 8px 16px 0px rgba(61, 72, 83, 0.16),
      0px 0px 1px 0px rgba(61, 72, 83, 0.36);
    border-radius: 8px;
    padding: 8px;
    z-index: 100;
    overflow-y: scroll;
    scrollbar-width: none;
  }
  .popover:focus-visible {
    outline: 2px solid var(--lottie-player-toolbar-icon-active-color);
    border-radius: 4px;
    box-sizing: border-box;
  }

  .popover::-webkit-scrollbar {
    width: 0px;
  }

  .popover-button {
    background: none;
    border: none;
    font-family: inherit;
    width: 100%;
    flex-direction: row;
    cursor: pointer;
    height: 32px;
    color: #20272c;
    justify-content: space-between;
    display: flex;
    padding: 4px 8px;
    align-items: flex-start;
    gap: 8px;
    align-self: stretch;
    border-radius: 4px;
  }

  .popover-button:focus-visible {
    outline: 2px solid var(--lottie-player-toolbar-icon-active-color);
    border-radius: 4px;
    box-sizing: border-box;
  }

  .popover-button:hover {
    background-color: var(--lottie-player-toolbar-hover-background-color);
  }

  .popover-button-text {
    display: flex;
    color: #20272c;
    flex-direction: column;
    align-self: stretch;
    justify-content: center;
    font-family: inherit;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    letter-spacing: -0.28px;
  }

  .reset-btn {
    font-size: 12px;
    cursor: pointer;
    font-family: inherit;
    background: none;
    border: none;
    font-weight: 400;
    line-height: 18px;
    letter-spacing: 0em;
    text-align: left;
    color: #63727e;
    padding: 0;
    width: 31px;
    height: 18px;
  }
  .reset-btn:focus-visible {
    outline: 2px solid var(--lottie-player-toolbar-icon-active-color);
    border-radius: 4px;
    box-sizing: border-box;
  }
  .reset-btn:hover {
    color: #20272c;
  }

  .option-title-button {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 32px;
    align-items: center;
    gap: 4px;
    align-self: stretch;
    cursor: pointer;
    color: var(--lottie-player-toolbar-icon-color);
    border: none;
    background: none;
    padding: 4px;
    font-family: inherit;
    font-size: 16px;
    font-weight: 700;
    line-height: 150%;
    letter-spacing: -0.32px;
  }
  .option-title-button.themes {
    width: auto;
    padding: 0;
  }
  .option-title-button:hover {
    background-color: var(--lottie-player-toolbar-icon-hover-color);
  }

  .option-title-themes-row {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1 0 0;
  }
  .option-title-themes-row:hover {
    background-color: var(--lottie-player-toolbar-icon-hover-color);
  }

  .option-title-button:focus-visible {
    outline: 2px solid var(--lottie-player-toolbar-icon-active-color);
    border-radius: 4px;
    box-sizing: border-box;
  }

  .option-title-text {
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    letter-spacing: -0.32px;
  }

  .option-title-separator {
    margin: 8px -8px;
    border-bottom: 1px solid var(--lottie-player-options-separator);
  }

  .option-title-chevron {
    display: flex;
    padding: 4px;
    border-radius: 8px;
    justify-content: center;
    align-items: center;
    gap: 8px;
  }

  .option-row {
    display: flex;
    flex-direction: column;
  }
  .option-row > ul {
    padding: 0;
    margin: 0;
  }

  .option-button {
    width: 100%;
    background: none;
    border: none;
    font-family: inherit;
    display: flex;
    padding: 4px 8px;
    color: #20272c;
    overflow: hidden;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    cursor: pointer;
    height: 32px;
    font-family: inherit;
    font-size: 14px;
    border-radius: 4px;
  }
  .option-button:hover {
    background-color: var(--lottie-player-toolbar-hover-background-color);
  }
  .option-button:focus-visible {
    outline: 2px solid var(--lottie-player-toolbar-icon-active-color);
    border-radius: 4px;
    box-sizing: border-box;
  }

  .option-tick {
    display: flex;
    width: 24px;
    height: 24px;
    align-items: flex-start;
    gap: 8px;
  }

  .seeker {
    height: 4px;
    width: 95%;
    outline: none;
    -webkit-appearance: none;
    -moz-apperance: none;
    border-radius: 9999px;
    cursor: pointer;
    background-image: linear-gradient(
      to right,
      rgb(0, 221, 179) calc(var(--seeker) * 1%),
      rgb(217, 224, 230) calc(var(--seeker) * 1%)
    );
  }
  .seeker.to-left {
    background-image: linear-gradient(
      to right,
      rgb(217, 224, 230) calc(var(--seeker) * 1%),
      rgb(0, 221, 179) calc(var(--seeker) * 1%)
    );
  }
  .seeker::-webkit-slider-runnable-track:focus-visible {
    color: #f07167;
    accent-color: #00ddb3;
  }

  .seeker::-webkit-slider-runnable-track {
    width: 100%;
    height: 5px;
    cursor: pointer;
  }
  .seeker::-webkit-slider-thumb {
    -webkit-appearance: none;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background: var(--lottie-player-seeker-thumb-color);
    cursor: pointer;
    margin-top: -5px;
  }
  .seeker:focus-visible::-webkit-slider-thumb {
    background: var(--lottie-player-seeker-thumb-color);
    outline: 2px solid var(--lottie-player-seeker-track-color);
    border: 1.5px solid #ffffff;
  }
  .seeker::-webkit-slider-thumb:hover {
    background: #019d91;
  }
  .seeker::-moz-range-thumb {
    appearance: none;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background: var(--lottie-player-seeker-thumb-color);
    cursor: pointer;
    margin-top: -5px;
    border-color: transparent;
  }
  .seeker:focus-visible::-moz-range-thumb {
    background: var(--lottie-player-seeker-thumb-color);
    outline: 2px solid var(--lottie-player-seeker-track-color);
    border: 1.5px solid #ffffff;
  }

  .error {
    display: flex;
    justify-content: center;
    margin: auto;
    height: 100%;
    align-items: center;
  }
`,rt=n(4241),ot=(n(7493),n(5031)),at=(t,e)=>"method"===e.kind&&e.descriptor&&!("value"in e.descriptor)?{...e,finisher(i){i.createProperty(e.key,t)}}:{kind:"field",key:Symbol(),placement:"own",descriptor:{},originalKey:e.key,initializer(){"function"==typeof e.initializer&&(this[e.key]=e.initializer.call(this))},finisher(i){i.createProperty(e.key,t)}},lt=(t,e,i)=>{e.constructor.createProperty(i,t)};
/*! Bundled license information:

@lit/reactive-element/css-tag.js:
  (**
   * @license
   * Copyright 2019 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)

@lit/reactive-element/reactive-element.js:
  (**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)

lit-html/lit-html.js:
  (**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)

lit-element/lit-element.js:
  (**
   * @license
   * Copyright 2017 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)

lit-html/is-server.js:
  (**
   * @license
   * Copyright 2022 Google LLC
   * SPDX-License-Identifier: BSD-3-Clause
   *)
*/function dt(t){return(e,i)=>void 0!==i?lt(t,e,i):at(t,e)}var ht;null===(ht=window.HTMLSlotElement)||void 0===ht||ht.prototype.assignedElements;var ct="2.7.2",ut="dotlottie-player",pt=class extends it{defaultTheme="";container;playMode=rt.g.Normal;autoplay=!1;background="transparent";controls=!1;direction=1;hover=!1;loop;renderer="svg";speed=1;src;intermission=0;activeAnimationId=null;light=!1;worker=!1;activeStateId;_seeker=0;_dotLottieCommonPlayer;_io;_loop;_renderer="svg";_unsubscribeListeners;_hasMultipleAnimations=!1;_hasMultipleThemes=!1;_hasMultipleStates=!1;_popoverIsOpen=!1;_animationsTabIsOpen=!1;_statesTabIsOpen=!1;_styleTabIsOpen=!1;_themesForCurrentAnimation=[];_statesForCurrentAnimation=[];_parseLoop(t){let e=parseInt(t,10);return Number.isInteger(e)&&e>0?(this._loop=e,e):"string"==typeof t&&["true","false"].includes(t)?(this._loop="true"===t,this._loop):((0,rt.c)("loop must be a positive integer or a boolean"),!1)}_handleSeekChange(t){let e=t.currentTarget;try{let t=parseInt(e.value,10);if(!this._dotLottieCommonPlayer)return;let i=t/100*this._dotLottieCommonPlayer.totalFrames;this.seek(i)}catch{throw(0,rt.a)("Error while seeking animation")}}_initListeners(){let t=this._dotLottieCommonPlayer;void 0!==t?(this._unsubscribeListeners=t.state.subscribe(((t,e)=>{this._seeker=t.seeker,this.requestUpdate(),e.currentState!==t.currentState&&this.dispatchEvent(new CustomEvent(t.currentState)),this.dispatchEvent(new CustomEvent(rt.e.Frame,{detail:{frame:t.frame,seeker:t.seeker}})),this.dispatchEvent(new CustomEvent(rt.e.VisibilityChange,{detail:{visibilityPercentage:t.visibilityPercentage}}))})),t.addEventListener("complete",(()=>{this.dispatchEvent(new CustomEvent(rt.e.Complete))})),t.addEventListener("loopComplete",(()=>{this.dispatchEvent(new CustomEvent(rt.e.LoopComplete))})),t.addEventListener("DOMLoaded",(()=>{let t=this.getManifest();t&&t.themes&&(this._themesForCurrentAnimation=t.themes.filter((t=>t.animations.includes(this.getCurrentAnimationId()||"")))),t&&t.states&&(this._hasMultipleStates=t.states.length>0,this._statesForCurrentAnimation=[],t.states.forEach((t=>{this._statesForCurrentAnimation.push(t)}))),this.dispatchEvent(new CustomEvent(rt.e.Ready))})),t.addEventListener("data_ready",(()=>{this.dispatchEvent(new CustomEvent(rt.e.DataReady))})),t.addEventListener("data_failed",(()=>{this.dispatchEvent(new CustomEvent(rt.e.DataFail))})),window&&window.addEventListener("click",(t=>this._clickOutListener(t)))):(0,rt.c)("player not initialized - cannot add event listeners","dotlottie-player-component")}async load(t,e,i){if(!this.shadowRoot)return;this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.destroy(),this._dotLottieCommonPlayer=new rt.j(t,this.container,{rendererSettings:null!=e?e:{scaleMode:"noScale",clearCanvas:!0,progressiveLoad:!0,hideOnTransparent:!0},hover:this.hasAttribute("hover")?this.hover:void 0,renderer:this.hasAttribute("renderer")?this._renderer:void 0,loop:this.hasAttribute("loop")?this._loop:void 0,direction:this.hasAttribute("direction")?1===this.direction?1:-1:void 0,speed:this.hasAttribute("speed")?this.speed:void 0,intermission:this.hasAttribute("intermission")?Number(this.intermission):void 0,playMode:this.hasAttribute("playMode")?this.playMode:void 0,autoplay:this.hasAttribute("autoplay")?this.autoplay:void 0,activeAnimationId:this.hasAttribute("activeAnimationId")?this.activeAnimationId:void 0,defaultTheme:this.hasAttribute("defaultTheme")?this.defaultTheme:void 0,light:this.light,worker:this.worker,activeStateId:this.hasAttribute("activeStateId")?this.activeStateId:void 0}),await this._dotLottieCommonPlayer.load(i);let s=this.getManifest();this._hasMultipleAnimations=this.animationCount()>1,s&&(s.themes&&(this._themesForCurrentAnimation=s.themes.filter((t=>t.animations.includes(this.getCurrentAnimationId()||""))),this._hasMultipleThemes=s.themes.length>0),s.states&&(this._hasMultipleStates=s.states.length>0,this._statesForCurrentAnimation=[],s.states.forEach((t=>{this._statesForCurrentAnimation.push(t)})))),this._initListeners()}getCurrentAnimationId(){var t;return null==(t=this._dotLottieCommonPlayer)?void 0:t.currentAnimationId}animationCount(){var t;return this._dotLottieCommonPlayer&&(null==(t=this._dotLottieCommonPlayer.getManifest())?void 0:t.animations.length)||0}animations(){if(!this._dotLottieCommonPlayer)return[];let t=this._dotLottieCommonPlayer.getManifest();return(null==t?void 0:t.animations.map((t=>t.id)))||[]}currentAnimation(){return this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.currentAnimationId?this._dotLottieCommonPlayer.currentAnimationId:""}getState(){return this._dotLottieCommonPlayer?this._dotLottieCommonPlayer.getState():rt.i}getManifest(){var t;return null==(t=this._dotLottieCommonPlayer)?void 0:t.getManifest()}getLottie(){var t;return null==(t=this._dotLottieCommonPlayer)?void 0:t.getAnimationInstance()}getVersions(){return{lottieWebVersion:rt.j.getLottieWebVersion(),dotLottiePlayerVersion:`${ct}`}}previous(t){var e;null==(e=this._dotLottieCommonPlayer)||e.previous(t)}next(t){var e;null==(e=this._dotLottieCommonPlayer)||e.next(t)}reset(){var t;null==(t=this._dotLottieCommonPlayer)||t.reset()}play(t,e){this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.play(t,e)}pause(){this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.pause()}stop(){this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.stop()}playOnShow(t){this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.playOnShow(t)}stopPlayOnShow(){this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.stopPlayOnShow()}playOnScroll(t){this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.playOnScroll(t)}stopPlayOnScroll(){this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.stopPlayOnScroll()}seek(t){this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.seek(t)}snapshot(t=!0){if(!this.shadowRoot)return"";let e=this.shadowRoot.querySelector(".animation svg"),i=(new XMLSerializer).serializeToString(e);if(t){let t=document.createElement("a");t.href=`data:image/svg+xml;charset=utf-8,${encodeURIComponent(i)}`,t.download=`download_${this._seeker}.svg`,document.body.appendChild(t),t.click(),document.body.removeChild(t)}return i}setTheme(t){var e;null==(e=this._dotLottieCommonPlayer)||e.setDefaultTheme(t)}themes(){var t;if(!this._dotLottieCommonPlayer)return[];let e=this._dotLottieCommonPlayer.getManifest();return(null==(t=null==e?void 0:e.themes)?void 0:t.map((t=>t.id)))||[]}getDefaultTheme(){return this._dotLottieCommonPlayer?this._dotLottieCommonPlayer.defaultTheme:""}getActiveStateMachine(){return this._dotLottieCommonPlayer?this._dotLottieCommonPlayer.activeStateId:""}_freeze(){this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.freeze()}setSpeed(t=1){this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.setSpeed(t)}setDirection(t){this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.setDirection(t)}setLooping(t){this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.setLoop(t)}isLooping(){return!!this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.loop}togglePlay(){this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.togglePlay()}toggleLooping(){this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.toggleLoop()}setPlayMode(t){this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.setMode(t)}enterInteractiveMode(t){this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.enterInteractiveMode(t)}exitInteractiveMode(){this._dotLottieCommonPlayer&&this._dotLottieCommonPlayer.exitInteractiveMode()}revertToManifestValues(t){var e;null==(e=this._dotLottieCommonPlayer)||e.revertToManifestValues(t)}static get styles(){return nt}async firstUpdated(){var t;this.container=null==(t=this.shadowRoot)?void 0:t.querySelector("#animation"),"IntersectionObserver"in window&&(this._io=new IntersectionObserver((t=>{var e,i;void 0!==t[0]&&t[0].isIntersecting?(null==(e=this._dotLottieCommonPlayer)?void 0:e.currentState)===rt.f.Frozen&&this.play():(null==(i=this._dotLottieCommonPlayer)?void 0:i.currentState)===rt.f.Playing&&this._freeze()})),this._io.observe(this.container)),this.loop?this._parseLoop(this.loop):this.hasAttribute("loop")&&this._parseLoop("true"),"svg"===this.renderer?this._renderer="svg":"canvas"===this.renderer?this._renderer="canvas":"html"===this.renderer&&(this._renderer="html"),this.src&&await this.load(this.src)}disconnectedCallback(){var t,e;this._io&&(this._io.disconnect(),this._io=void 0),null==(t=this._dotLottieCommonPlayer)||t.destroy(),null==(e=this._unsubscribeListeners)||e.call(this),window&&window.removeEventListener("click",(t=>this._clickOutListener(t)))}_clickOutListener(t){!t.composedPath().some((t=>t instanceof HTMLElement&&(t.classList.contains("popover")||"lottie-animation-options"===t.id)))&&this._popoverIsOpen&&(this._popoverIsOpen=!1,this.requestUpdate())}renderControls(){var t,e,i,s,n;let r=(null==(t=this._dotLottieCommonPlayer)?void 0:t.currentState)===rt.f.Playing,o=(null==(e=this._dotLottieCommonPlayer)?void 0:e.currentState)===rt.f.Paused;return F`
      <div id="lottie-controls" aria-label="lottie-animation-controls" class="toolbar">
        ${this._hasMultipleAnimations?F`
              <button @click=${()=>this.previous()} aria-label="Previous animation" class="btn-spacing-left">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M1.69214 13.5C1.69214 13.7761 1.916 14 2.19214 14C2.46828 14 2.69214 13.7761 2.69214 13.5L2.69214 2.5C2.69214 2.22386 2.46828 2 2.19214 2C1.916 2 1.69214 2.22386 1.69214 2.5V13.5ZM12.5192 13.7828C13.1859 14.174 14.0254 13.6933 14.0254 12.9204L14.0254 3.0799C14.0254 2.30692 13.1859 1.8262 12.5192 2.21747L4.13612 7.13769C3.47769 7.52414 3.47769 8.4761 4.13612 8.86255L12.5192 13.7828Z"
                    fill="#20272C"
                  />
                </svg>
              </button>
            `:F``}
        <button
          id="lottie-play-button"
          @click=${()=>{this.togglePlay()}}
          class=${r||o?"active "+(this._hasMultipleAnimations?"btn-spacing-center":"btn-spacing-right"):""+(this._hasMultipleAnimations?"btn-spacing-center":"btn-spacing-right")}
          aria-label="play / pause animation"
        >
          ${r?F`
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M3.99996 2C3.26358 2 2.66663 2.59695 2.66663 3.33333V12.6667C2.66663 13.403 3.26358 14 3.99996 14H5.33329C6.06967 14 6.66663 13.403 6.66663 12.6667V3.33333C6.66663 2.59695 6.06967 2 5.33329 2H3.99996Z"
                    fill="#20272C"
                  />
                  <path
                    d="M10.6666 2C9.93025 2 9.33329 2.59695 9.33329 3.33333V12.6667C9.33329 13.403 9.93025 14 10.6666 14H12C12.7363 14 13.3333 13.403 13.3333 12.6667V3.33333C13.3333 2.59695 12.7363 2 12 2H10.6666Z"
                    fill="#20272C"
                  />
                </svg>
              `:F`
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M3.33337 3.46787C3.33337 2.52312 4.35948 1.93558 5.17426 2.41379L12.8961 6.94592C13.7009 7.41824 13.7009 8.58176 12.8961 9.05408L5.17426 13.5862C4.35948 14.0644 3.33337 13.4769 3.33337 12.5321V3.46787Z"
                    fill="#20272C"
                  />
                </svg>
              `}
        </button>
        ${this._hasMultipleAnimations?F`
              <button @click=${()=>this.next()} aria-label="Next animation" class="btn-spacing-right">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M14.3336 2.5C14.3336 2.22386 14.1097 2 13.8336 2C13.5574 2 13.3336 2.22386 13.3336 2.5V13.5C13.3336 13.7761 13.5574 14 13.8336 14C14.1097 14 14.3336 13.7761 14.3336 13.5V2.5ZM3.50618 2.21722C2.83954 1.82595 2 2.30667 2 3.07965V12.9201C2 13.6931 2.83954 14.1738 3.50618 13.7825L11.8893 8.86231C12.5477 8.47586 12.5477 7.52389 11.8893 7.13745L3.50618 2.21722Z"
                    fill="#20272C"
                  />
                </svg>
              </button>
            `:F``}
        <input
          id="lottie-seeker-input"
          class="seeker ${-1===(null==(i=this._dotLottieCommonPlayer)?void 0:i.direction)?"to-left":""}"
          type="range"
          min="0"
          step="1"
          max="100"
          .value=${this._seeker}
          @input=${t=>this._handleSeekChange(t)}
          @mousedown=${()=>{this._freeze()}}
          @mouseup=${()=>{var t;null==(t=this._dotLottieCommonPlayer)||t.unfreeze()}}
          aria-valuemin="1"
          aria-valuemax="100"
          role="slider"
          aria-valuenow=${this._seeker}
          aria-label="lottie-seek-input"
          style=${`--seeker: ${this._seeker}`}
        />
        <button
          id="lottie-loop-toggle"
          @click=${()=>this.toggleLooping()}
          class=${null!=(s=this._dotLottieCommonPlayer)&&s.loop?"active btn-spacing-left":"btn-spacing-left"}
          aria-label="loop-toggle"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M10.8654 2.31319C11.0607 2.11793 11.3772 2.11793 11.5725 2.31319L13.4581 4.19881C13.6534 4.39407 13.6534 4.71066 13.4581 4.90592L11.5725 6.79154C11.3772 6.9868 11.0607 6.9868 10.8654 6.79154C10.6701 6.59628 10.6701 6.27969 10.8654 6.08443L11.6162 5.33362H4V6.66695C4 7.03514 3.70152 7.33362 3.33333 7.33362C2.96514 7.33362 2.66666 7.03514 2.66666 6.66695L2.66666 4.66695C2.66666 4.29876 2.96514 4.00028 3.33333 4.00028H11.8454L10.8654 3.0203C10.6701 2.82504 10.6701 2.50846 10.8654 2.31319Z"
              fill="currentColor"
            />
            <path
              d="M12.4375 11.9999C12.8057 11.9999 13.1042 11.7014 13.1042 11.3332V9.33321C13.1042 8.96502 12.8057 8.66655 12.4375 8.66655C12.0693 8.66655 11.7708 8.96502 11.7708 9.33321V10.6665H4.15462L4.90543 9.91573C5.10069 9.72047 5.10069 9.40389 4.90543 9.20862C4.71017 9.01336 4.39359 9.01336 4.19832 9.20862L2.31271 11.0942C2.11744 11.2895 2.11744 11.6061 2.31271 11.8013L4.19832 13.687C4.39359 13.8822 4.71017 13.8822 4.90543 13.687C5.10069 13.4917 5.10069 13.1751 4.90543 12.9799L3.92545 11.9999H12.4375Z"
              fill="currentColor"
            />
          </svg>
        </button>
        ${this._hasMultipleAnimations||this._hasMultipleThemes||this._hasMultipleStates?F`
              <button
                id="lottie-animation-options"
                @click=${()=>{this._popoverIsOpen=!this._popoverIsOpen,this.requestUpdate()}}
                aria-label="options"
                class="btn-spacing-right"
                style=${"background-color: "+(this._popoverIsOpen?"var(--lottie-player-toolbar-icon-hover-color)":"")}
              >
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M8.33337 11.6666C7.78109 11.6666 7.33337 12.1143 7.33337 12.6666C7.33337 13.2189 7.78109 13.6666 8.33337 13.6666C8.88566 13.6666 9.33337 13.2189 9.33337 12.6666C9.33337 12.1143 8.88566 11.6666 8.33337 11.6666Z"
                    fill="#20272C"
                  />
                  <path
                    d="M7.33337 7.99992C7.33337 7.44763 7.78109 6.99992 8.33337 6.99992C8.88566 6.99992 9.33338 7.44763 9.33338 7.99992C9.33338 8.5522 8.88566 8.99992 8.33337 8.99992C7.78109 8.99992 7.33337 8.5522 7.33337 7.99992Z"
                    fill="#20272C"
                  />
                  <path
                    d="M7.33337 3.33325C7.33337 2.78097 7.78109 2.33325 8.33337 2.33325C8.88566 2.33325 9.33338 2.78097 9.33338 3.33325C9.33338 3.88554 8.88566 4.33325 8.33337 4.33325C7.78109 4.33325 7.33337 3.88554 7.33337 3.33325Z"
                    fill="#20272C"
                  />
                </svg>
              </button>
            `:F``}
      </div>
      ${this._popoverIsOpen?F`
            <div
              id="popover"
              class="popover"
              tabindex="0"
              aria-label="lottie animations themes popover"
              style="min-height: ${this.themes().length>0?"84px":"auto"}"
            >
              ${this._animationsTabIsOpen||this._styleTabIsOpen||this._statesTabIsOpen?F``:F`
                    <button
                      class="popover-button"
                      tabindex="0"
                      aria-label="animations"
                      @click=${()=>{this._animationsTabIsOpen=!this._animationsTabIsOpen,this.requestUpdate()}}
                      @keydown=${t=>{("Space"===t.code||"Enter"===t.code)&&(this._animationsTabIsOpen=!this._animationsTabIsOpen,this.requestUpdate())}}
                    >
                      <div class="popover-button-text">Animations</div>
                      <div>
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M10.4697 17.5303C10.1768 17.2374 10.1768 16.7626 10.4697 16.4697L14.9393 12L10.4697 7.53033C10.1768 7.23744 10.1768 6.76256 10.4697 6.46967C10.7626 6.17678 11.2374 6.17678 11.5303 6.46967L16.5303 11.4697C16.8232 11.7626 16.8232 12.2374 16.5303 12.5303L11.5303 17.5303C11.2374 17.8232 10.7626 17.8232 10.4697 17.5303Z"
                            fill="#4C5863"
                          />
                        </svg>
                      </div>
                    </button>
                  `}
              ${!this._hasMultipleThemes||this._styleTabIsOpen||this._animationsTabIsOpen||this._statesTabIsOpen?"":F` <button
                    class="popover-button"
                    aria-label="Themes"
                    @click=${()=>{this._styleTabIsOpen=!this._styleTabIsOpen,this.requestUpdate()}}
                    @keydown=${t=>{("Space"===t.code||"Enter"===t.code)&&(this._styleTabIsOpen=!this._styleTabIsOpen,this.requestUpdate())}}
                  >
                    <div class="popover-button-text">Themes</div>
                    <div>
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M10.4697 17.5303C10.1768 17.2374 10.1768 16.7626 10.4697 16.4697L14.9393 12L10.4697 7.53033C10.1768 7.23744 10.1768 6.76256 10.4697 6.46967C10.7626 6.17678 11.2374 6.17678 11.5303 6.46967L16.5303 11.4697C16.8232 11.7626 16.8232 12.2374 16.5303 12.5303L11.5303 17.5303C11.2374 17.8232 10.7626 17.8232 10.4697 17.5303Z"
                          fill="#4C5863"
                        />
                      </svg>
                    </div>
                  </button>`}
              ${!this._hasMultipleStates||this._styleTabIsOpen||this._animationsTabIsOpen||this._statesTabIsOpen?"":F` <button
                    class="popover-button"
                    aria-label="States"
                    @click=${()=>{this._statesTabIsOpen=!this._statesTabIsOpen,this.requestUpdate()}}
                    @keydown=${t=>{("Space"===t.code||"Enter"===t.code)&&(this._statesTabIsOpen=!this._statesTabIsOpen,this.requestUpdate())}}
                  >
                    <div class="popover-button-text">States</div>
                    <div>
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M10.4697 17.5303C10.1768 17.2374 10.1768 16.7626 10.4697 16.4697L14.9393 12L10.4697 7.53033C10.1768 7.23744 10.1768 6.76256 10.4697 6.46967C10.7626 6.17678 11.2374 6.17678 11.5303 6.46967L16.5303 11.4697C16.8232 11.7626 16.8232 12.2374 16.5303 12.5303L11.5303 17.5303C11.2374 17.8232 10.7626 17.8232 10.4697 17.5303Z"
                          fill="#4C5863"
                        />
                      </svg>
                    </div>
                  </button>`}
              ${this._animationsTabIsOpen?F`<button
                      class="option-title-button"
                      aria-label="Back to main popover menu"
                      @click=${()=>{this._animationsTabIsOpen=!this._animationsTabIsOpen,this.requestUpdate()}}
                    >
                      <div class="option-title-chevron">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M13.5303 6.46967C13.8232 6.76256 13.8232 7.23744 13.5303 7.53033L9.06066 12L13.5303 16.4697C13.8232 16.7626 13.8232 17.2374 13.5303 17.5303C13.2374 17.8232 12.7626 17.8232 12.4697 17.5303L7.46967 12.5303C7.17678 12.2374 7.17678 11.7626 7.46967 11.4697L12.4697 6.46967C12.7626 6.17678 13.2374 6.17678 13.5303 6.46967Z"
                            fill="#20272C"
                          />
                        </svg>
                      </div>
                      <div>Animations</div>
                    </button>
                    <div class="option-title-separator"></div>
                    <div class="option-row">
                      <ul>
                        ${this.animations().map((t=>F`
                            <li>
                              <button
                                class="option-button"
                                aria-label=${`${t}`}
                                @click=${()=>{this._animationsTabIsOpen=!this._animationsTabIsOpen,this._popoverIsOpen=!this._popoverIsOpen,this.play(t),this.requestUpdate()}}
                                @keydown=${e=>{("Space"===e.code||"Enter"===e.code)&&(this._animationsTabIsOpen=!this._animationsTabIsOpen,this._popoverIsOpen=!this._popoverIsOpen,this.play(t),this.requestUpdate())}}
                              >
                                <div class="option-tick">
                                  ${this.currentAnimation()===t?F`
                                        <svg
                                          width="24"
                                          height="24"
                                          viewBox="0 0 24 24"
                                          fill="none"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            fill-rule="evenodd"
                                            clip-rule="evenodd"
                                            d="M20.5281 5.9372C20.821 6.23009 20.821 6.70497 20.5281 6.99786L9.46297 18.063C9.32168 18.2043 9.12985 18.2833 8.93004 18.2826C8.73023 18.2819 8.53895 18.2015 8.39864 18.0593L3.46795 13.0596C3.1771 12.7647 3.1804 12.2898 3.47532 11.999C3.77024 11.7081 4.2451 11.7114 4.53595 12.0063L8.93634 16.4683L19.4675 5.9372C19.7604 5.64431 20.2352 5.64431 20.5281 5.9372Z"
                                            fill="#20272C"
                                          />
                                        </svg>
                                      `:F`<div style="width: 24px; height: 24px"></div>`}
                                </div>
                                <div>${t}</div>
                              </button>
                            </li>
                          `))}
                      </ul>
                    </div> `:F``}
              ${this._styleTabIsOpen?F`<div class="option-title-themes-row">
                      <button
                        class="option-title-button themes"
                        aria-label="Back to main popover menu"
                        @click=${()=>{this._styleTabIsOpen=!this._styleTabIsOpen,this.requestUpdate()}}
                      >
                        <div class="option-title-chevron">
                          <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M13.5303 6.46967C13.8232 6.76256 13.8232 7.23744 13.5303 7.53033L9.06066 12L13.5303 16.4697C13.8232 16.7626 13.8232 17.2374 13.5303 17.5303C13.2374 17.8232 12.7626 17.8232 12.4697 17.5303L7.46967 12.5303C7.17678 12.2374 7.17678 11.7626 7.46967 11.4697L12.4697 6.46967C12.7626 6.17678 13.2374 6.17678 13.5303 6.46967Z"
                              fill="#20272C"
                            />
                          </svg>
                        </div>
                        <div class="option-title-text">Themes</div>
                        ${""===(null==(n=this._dotLottieCommonPlayer)?void 0:n.defaultTheme)?F``:F`
                              <button
                                class="reset-btn"
                                @click=${()=>{this.setTheme(""),this.requestUpdate()}}
                              >
                                Reset
                              </button>
                            `}
                      </button>
                    </div>
                    <div class="option-title-separator"></div>
                    <div class="option-row">
                      <ul>
                        ${this._themesForCurrentAnimation.map((t=>F`
                            <li>
                              <button
                                class="option-button"
                                aria-label="${t.id}"
                                @click=${()=>{this.setTheme(t.id)}}
                                @keydown=${e=>{("Space"===e.code||"Enter"===e.code)&&this.setTheme(t.id)}}
                              >
                                <div class="option-tick">
                                  ${this.getDefaultTheme()===t.id?F`
                                        <svg
                                          width="24"
                                          height="24"
                                          viewBox="0 0 24 24"
                                          fill="none"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            fill-rule="evenodd"
                                            clip-rule="evenodd"
                                            d="M20.5281 5.9372C20.821 6.23009 20.821 6.70497 20.5281 6.99786L9.46297 18.063C9.32168 18.2043 9.12985 18.2833 8.93004 18.2826C8.73023 18.2819 8.53895 18.2015 8.39864 18.0593L3.46795 13.0596C3.1771 12.7647 3.1804 12.2898 3.47532 11.999C3.77024 11.7081 4.2451 11.7114 4.53595 12.0063L8.93634 16.4683L19.4675 5.9372C19.7604 5.64431 20.2352 5.64431 20.5281 5.9372Z"
                                            fill="#20272C"
                                          />
                                        </svg>
                                      `:F`<div style="width: 24px; height: 24px"></div>`}
                                </div>
                                <div>${t.id}</div>
                              </button>
                            </li>
                          `))}
                      </ul>
                    </div>`:F``}
              ${this._statesTabIsOpen?F`<div class="option-title-themes-row">
                      <button
                        class="option-title-button themes"
                        aria-label="Back to main popover menu"
                        @click=${()=>{this._statesTabIsOpen=!this._statesTabIsOpen,this.requestUpdate()}}
                      >
                        <div class="option-title-chevron">
                          <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              fill-rule="evenodd"
                              clip-rule="evenodd"
                              d="M13.5303 6.46967C13.8232 6.76256 13.8232 7.23744 13.5303 7.53033L9.06066 12L13.5303 16.4697C13.8232 16.7626 13.8232 17.2374 13.5303 17.5303C13.2374 17.8232 12.7626 17.8232 12.4697 17.5303L7.46967 12.5303C7.17678 12.2374 7.17678 11.7626 7.46967 11.4697L12.4697 6.46967C12.7626 6.17678 13.2374 6.17678 13.5303 6.46967Z"
                              fill="#20272C"
                            />
                          </svg>
                        </div>
                        <div class="option-title-text">States</div>
                        <button
                          class="reset-btn"
                          @click=${()=>{this.exitInteractiveMode(),this.requestUpdate()}}
                        >
                          Reset
                        </button>
                      </button>
                    </div>
                    <div class="option-title-separator"></div>
                    <div class="option-row">
                      <ul>
                        ${this._statesForCurrentAnimation.map((t=>F`
                            <li>
                              <button
                                class="option-button"
                                aria-label="${t}"
                                @click=${()=>{this.enterInteractiveMode(t)}}
                                @keydown=${e=>{("Space"===e.code||"Enter"===e.code)&&this.enterInteractiveMode(t)}}
                              >
                                <div class="option-tick">
                                  ${this.getActiveStateMachine()===t?F`
                                        <svg
                                          width="24"
                                          height="24"
                                          viewBox="0 0 24 24"
                                          fill="none"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            fill-rule="evenodd"
                                            clip-rule="evenodd"
                                            d="M20.5281 5.9372C20.821 6.23009 20.821 6.70497 20.5281 6.99786L9.46297 18.063C9.32168 18.2043 9.12985 18.2833 8.93004 18.2826C8.73023 18.2819 8.53895 18.2015 8.39864 18.0593L3.46795 13.0596C3.1771 12.7647 3.1804 12.2898 3.47532 11.999C3.77024 11.7081 4.2451 11.7114 4.53595 12.0063L8.93634 16.4683L19.4675 5.9372C19.7604 5.64431 20.2352 5.64431 20.5281 5.9372Z"
                                            fill="#20272C"
                                          />
                                        </svg>
                                      `:F`<div style="width: 24px; height: 24px"></div>`}
                                </div>
                                <div>${t}</div>
                              </button>
                            </li>
                          `))}
                      </ul>
                    </div>`:F``}
            </div>
          `:F``}
    `}render(){var t;let e=this.controls?"main controls":"main",i=this.controls?"animation controls":"animation";return F`
      <div id="animation-container" class=${e} lang="en" role="img" aria-label="lottie-animation-container">
        <div id="animation" class=${i} style="background:${this.background};">
          ${(null==(t=this._dotLottieCommonPlayer)?void 0:t.currentState)===rt.f.Error?F` <div class="error">⚠️</div> `:void 0}
        </div>
        ${this.controls?this.renderControls():void 0}
      </div>
    `}};(0,ot.a)([dt({type:String})],pt.prototype,"defaultTheme",2),(0,ot.a)([function(t,e){return(({finisher:t,descriptor:e})=>(i,s)=>{var n;if(void 0===s){let s=null!==(n=i.originalKey)&&void 0!==n?n:i.key,r=null!=e?{kind:"method",placement:"prototype",key:s,descriptor:e(i.key)}:{...i,key:s};return null!=t&&(r.finisher=function(e){t(e,s)}),r}{let n=i.constructor;void 0!==e&&Object.defineProperty(i,s,e(s)),null==t||t(n,s)}})({descriptor:i=>{let s={get(){var e,i;return null!==(i=null===(e=this.renderRoot)||void 0===e?void 0:e.querySelector(t))&&void 0!==i?i:null},enumerable:!0,configurable:!0};if(e){let e="symbol"==typeof i?Symbol():"__"+i;s.get=function(){var i,s;return void 0===this[e]&&(this[e]=null!==(s=null===(i=this.renderRoot)||void 0===i?void 0:i.querySelector(t))&&void 0!==s?s:null),this[e]}}return s}})}("#animation")],pt.prototype,"container",2),(0,ot.a)([dt()],pt.prototype,"playMode",2),(0,ot.a)([dt({type:Boolean})],pt.prototype,"autoplay",2),(0,ot.a)([dt({type:String})],pt.prototype,"background",2),(0,ot.a)([dt({type:Boolean})],pt.prototype,"controls",2),(0,ot.a)([dt({type:Number})],pt.prototype,"direction",2),(0,ot.a)([dt({type:Boolean})],pt.prototype,"hover",2),(0,ot.a)([dt({type:String})],pt.prototype,"loop",2),(0,ot.a)([dt({type:String})],pt.prototype,"renderer",2),(0,ot.a)([dt({type:Number})],pt.prototype,"speed",2),(0,ot.a)([dt({type:String})],pt.prototype,"src",2),(0,ot.a)([dt()],pt.prototype,"intermission",2),(0,ot.a)([dt({type:String})],pt.prototype,"activeAnimationId",2),(0,ot.a)([dt({type:Boolean})],pt.prototype,"light",2),(0,ot.a)([dt({type:Boolean})],pt.prototype,"worker",2),(0,ot.a)([dt({type:String})],pt.prototype,"activeStateId",2),(0,ot.a)([function(t){return dt({...t,state:!0})}()],pt.prototype,"_seeker",2),customElements.get(ut)||customElements.define(ut,pt);var mt={ACTIVE_DESCENDANT:"aria-activedescendant",EXPANDED:"aria-expanded",HIDDEN:"aria-hidden",SELECTED:"aria-selected"};var ft={ABOVE_BOTTOM:"above-bottom",ABOVE_HALFWAY:"above-halfway",ABOVE_VIEWPORT:"above-viewport",BELOW_BOTTOM:"below-bottom",BELOW_HALFWAY:"below-halfway",BELOW_VIEWPORT:"below-viewport",CLICK:"click",EXPANDED:"expanded",ERROR:"error",FINAL:"final",GEOALERT_LINK_HIDDEN:"geoalert__link--hidden",GEOALERT_HIDDEN:"geoalert--hidden",HEADER_PINNED:"header--pinned",HIDDEN:"hidden",IN_VIEWPORT:"in-viewport",MEERKAT_OPEN:"meerkat--open",MEERKAT_CLOSE:"meerkat--close",MEERKAT_SHOW:"meerkat--show",NAV_OPEN:"nav-open",OPEN:"open",PLAYING:"playing",POPUP_OPEN:"popup-open",STATE_VOTING_INFORMATION_HEADING_STATE:"statevotinginformation__heading--state",STATE_VOTING_INFORMATION_STATE:"usvotestatevotinginformation--state",SHOW:"show",US_VOTE_STATE_VOTING_INFORMATION_HEADING_STATE:"statevotinginformation__heading--state",US_VOTE_STATE_VOTING_INFORMATION_STATE:"usvotestatevotinginformation--state",VISIBLE:"visible"};var vt={ARROW_UP:"ArrowUp",ARROW_DOWN:"ArrowDown",BLUR:"blur",CHANGE:"change",CLICK:"click",ENTER:"Enter",FILTER_FORM_SUBMIT:"filterFormSubmit",FOCUS:"focus",FORM_SUBMIT:"formsubmit",INPUT:"input",KEY_DOWN:"keydown",LOADED_METADATA:"loadedmetadata",MOUSEENTER:"mouseenter",MOUSELEAVE:"mouseleave",MOUSEOUT:"mouseout",MOUSEOVER:"mouseover",PAUSE_VIDEO:"pausevideo",POINTER_DOWN:"pointerdown",POP_STATE:"popstate",PUSH_STATE:"pushstate",READY:"ready",RESIZE:"resize",SCROLL:"scroll",SUBMIT:"submit",TOUCH_CAN:"touchcancdocument",TOUCH_END:"touchend",TOUCH_MOVE:"touchmove",TOUCH_START:"touchstart",TRANSITIONEND:"transitionend",UPDATE_IN_VIEWPORT_MODULES:"updateinviewportmodules",WHEEL:"wheel"};var gt={ALL:"all",BEFOREEND:"beforeend",BEM_ELEMENT:"__",BEM_MODIFIER:"--",CHANGE:"Change ",CLEAR_FILTER:"Clear Filter",CLOSE:"Close",CLOSE_FILTERS:"Close Filters",COUNTRY_PARAM:"vsa_country",DATA_VISIBLE:"data-visible",ELECTION_PARAM:"vsa_election_filter",ELECTION_TYPE:"election_type",EVENT_PARAM:"vsa_event_filter",EVENT_TYPE:"event_type",EYE_BLINK:"EyeBlink",FALSE:"false",FILTERS:"Filters",fURL1:"//www.facebook.com/sharer.php?u=",HEIGHT:"height",INCLUDE_EMAIL_SIGNUP:"include_email_signup",IN_VIEWPORT:"in-viewport",MENU:"Menu",MQ_HOVER:"(hover: hover)",MQ_NO_ANY:"(any-hover: none), (any-pointer: coarse)",lURL1:"//www.linkedin.com/shareArticle?mini=true&url=",lURL2:"&title=",lURL3:"&summary=",lURL4:"&source=",MEERKAT:"meerkat",mURL1:"mailto:",mURL2:"?subject=",mURL3:"&body=",NAME:"name",NO:"no",NO_RESULTS:"There are no posts that match that filter combination.",POPUP:"popup",POST:"post",POSTS:"posts",PUBLISH:"publish",OCD_ID_PARAM:"vsa_ocd_id",SQUERY:"&s=",STATE_PARAM:"vsa_state",SUBMITTED:"submitted",TABINDEX:"tabindex",TRUE:"true",tURL1:"https://twitter.com/share?url=",tURLText:"&text=",tURLVia:"&via=BSD",VALUE:"value",WIDTH:"width",ZIP_PARAM:"vsa_zip",VSA_COOKIE:"VSACOOKIE"};var _t={ESCAPE:27,SPACEBAR:32};var yt={ACCORDION:".accordion",ACCORDION_BUTTON:".accordion__button",ACCORDION_PANEL:".accordion__panel",AUTOCOMPLETE_CONTAINER:".autocomplete__container",AUTOCOMPLETE_DROPDOWN_ARROW:".autocomplete__dropdown-arrow",AUTOCOMPLETE_INPUT:".autocomplete__input",AUTOCOMPLETE_RESULTS:".autocomplete__results",TAKEACTION_CLEAR:".takeactionsteptwo__clear",DATA_BOTTOM:"data-bottomposition",DATA_HALFWAY:"data-halfway",DATA_HAS_ANIMATED:"data-has-animated",DATA_POSITION:"data-position",DATA_VISIBLE:"[data-visible]",DIV:"div",FIND_YOUR_TEAM_HEADING_STATE:".findyourteam__heading--state",FIND_YOUR_TEAM_RESTART:".findyourteam__restart",FIND_YOUR_TEAM_RESTART_LINK:".findyourteam__restart-link",FIND_YOUR_TEAM_TITLE_PART_ONE:".findyourteam__part--part1",FIND_YOUR_TEAM_TITLE_PART_TWO:".findyourteam__part--part2",FOOTER_FORM:".footer__form",FOOTER_ACTIONNETWORK:".footer__actionnetwork",FORM_ACTIONNETWORK:".form__actionnetwork",FORM_FORM:".form__form",FORM_INPUT_LAST_NAME:".form__form-input--last-name",FORM_INPUT_FIRST_NAME:".form__form-input--first-name",FORM_INPUT_EMAIL:".form__form-input--email",FORM_INPUT_PHONE:".form__form-input--phone",FORM_INPUT_ZIP:".form__form-input--zip",FORM_POST_SUBMIT:".form__post-submit",GEOALERT_LINK:".geoalert__link",GEOALERT_PARA:".geoalert__para",GEOALERT_TEXT:".geoalert__text",INPUT_GROUP:".input-group",INPUT_GROUP_FIRST_NAME:".input-group--first-name",INPUT_GROUP_LAST_NAME:".input-group--last-name",INPUT_GROUP_EMAIL:".input-group--email",INPUT_GROUP_PHONE:".input-group--phone",INPUT_GROUP_ZIP:".input-group--zip",MEERKAT_OPEN:".meerkat__open",MEERKAT_CLOSE:".meerkat__close",MEERKAT_DIALOG:".meerkat__dialog",MEERKAT_ICON:".meerkat__icon",POPUP_CLOSE:".popup__close",POPUP_ICON_HOUSE:".popup__icon--whitehouse",POST_SUBMIT:".footer__post-submit",STATE_CROSS_LINK:"#state-cross-link",STATE_CROSS_LINK_TEXT:"#state-cross-link-text",STATE_SELECTOR:".stateselector",STATE_SELECTOR_SELECT:".stateselector__select",STATE_VOTING_INFORMATION_HEADING_STATE:".statevotinginformation__heading--state",STATE_VOTING_INFORMATION_RESTART:".statevotinginformation__restart",STATE_VOTING_INFORMATION_RESTART_LINK:".statevotinginformation__restart-link",STATE_VOTING_INFORMATION_TITLE_PART_ONE:".statevotinginformation__title--part1",STATE_VOTING_INFORMATION_TITLE_PART_TWO:".statevotinginformation__title--part2",TAKEACTION_APPLIED_FILTERS:".takeactionsteptwo__applied-filters",TAKEACTION_APPLIED_FILTER_ELECTION:".takeactionsteptwo__applied-filter-election",TAKEACTION_APPLIED_FILTER_EVENT:".takeactionsteptwo__applied-filter-event",TAKEACTION_EVENT_LIST:".takeactionsteptwo__event-list",TAKEACTION_FILTERS_FORM:".takeactionsteptwo__filtersform",TAKEACTION_HEADING_STATE:".takeactionsteptwo__heading--state",TAKEACTION_LOADER:".takeactionsteptwo__loader",TAKEACTION_NO_RESULTS:".takeactionsteptwo__no-results",TAKEACTION_RESTART:".takeactionsteptwo__restart",TAKEACTION_TITLE_PART_ONE:".takeactionsteptwo__title--part1",TAKEACTION_TITLE_PART_TWO:".takeactionsteptwo__title--part2",TAKEACTION_RESULTS:".takeactionsteptwo__results",TOOLTIP_BUTTON:".tooltip__trigger",TOOLTIP_CONTENT:".tooltip__content",US_VOTE_STATE_VOTING_INFORMATION_HEADING_STATE:"#state-selected-heading",US_VOTE_STATE_VOTING_INFORMATION_RESTART:".usvotestatevotinginformation__restart",US_VOTE_STATE_VOTING_INFORMATION_RESTART_LINK:".usvotestatevotinginformation__restart-link",US_VOTE_STATE_VOTING_INFORMATION_TITLE_PART_ONE:".usvotestatevotinginformation__title--part1",US_VOTE_STATE_VOTING_INFORMATION_TITLE_PART_TWO:".usvotestatevotinginformation__title--part2",VIDEO_TRIGGER:".video__poster",VIDEO_EMBED:".video__embed"};var bt=(t,e)=>{let i=t;const s=i.matches||i.webkitMatchesSelector||i.mozMatchesSelector||i.msMatchesSelector;for(;i&&!s.call(i,e);)i=i.parentElement;return i};var Et=(t,e,i)=>{let s;return function(...n){const r=this,o=i&&!s;clearTimeout(s),s=setTimeout((function(){s=null,i||t.apply(r,n)}),e),o&&t.apply(r,n)}};var wt=t=>{let e=null;const i=`${t}=`,s=document.cookie.split(";");return Array.isArray(s)&&s.forEach((t=>{const s=t.trim();0===s.indexOf(i)&&(e=s.substring(i.length,s.length))})),e};
/*! @license DOMPurify 3.2.3 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.3/LICENSE */
const{entries:Tt,setPrototypeOf:St,isFrozen:At,getPrototypeOf:Lt,getOwnPropertyDescriptor:Ot}=Object;let{freeze:Ct,seal:It,create:Pt}=Object,{apply:Nt,construct:xt}="undefined"!=typeof Reflect&&Reflect;Ct||(Ct=function(t){return t}),It||(It=function(t){return t}),Nt||(Nt=function(t,e,i){return t.apply(e,i)}),xt||(xt=function(t,e){return new t(...e)});const Mt=Wt(Array.prototype.forEach),kt=Wt(Array.prototype.pop),Dt=Wt(Array.prototype.push),Rt=Wt(String.prototype.toLowerCase),$t=Wt(String.prototype.toString),Ft=Wt(String.prototype.match),Ht=Wt(String.prototype.replace),Ut=Wt(String.prototype.indexOf),zt=Wt(String.prototype.trim),Bt=Wt(Object.prototype.hasOwnProperty),Vt=Wt(RegExp.prototype.test),qt=(Gt=TypeError,function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return xt(Gt,e)});var Gt;function Wt(t){return function(e){for(var i=arguments.length,s=new Array(i>1?i-1:0),n=1;n<i;n++)s[n-1]=arguments[n];return Nt(t,e,s)}}function Kt(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Rt;St&&St(t,null);let s=e.length;for(;s--;){let n=e[s];if("string"==typeof n){const t=i(n);t!==n&&(At(e)||(e[s]=t),n=t)}t[n]=!0}return t}function jt(t){for(let e=0;e<t.length;e++){Bt(t,e)||(t[e]=null)}return t}function Yt(t){const e=Pt(null);for(const[i,s]of Tt(t)){Bt(t,i)&&(Array.isArray(s)?e[i]=jt(s):s&&"object"==typeof s&&s.constructor===Object?e[i]=Yt(s):e[i]=s)}return e}function Zt(t,e){for(;null!==t;){const i=Ot(t,e);if(i){if(i.get)return Wt(i.get);if("function"==typeof i.value)return Wt(i.value)}t=Lt(t)}return function(){return null}}const Xt=Ct(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Jt=Ct(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),Qt=Ct(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),te=Ct(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),ee=Ct(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),ie=Ct(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),se=Ct(["#text"]),ne=Ct(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),re=Ct(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),oe=Ct(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),ae=Ct(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),le=It(/\{\{[\w\W]*|[\w\W]*\}\}/gm),de=It(/<%[\w\W]*|[\w\W]*%>/gm),he=It(/\$\{[\w\W]*}/gm),ce=It(/^data-[\-\w.\u00B7-\uFFFF]+$/),ue=It(/^aria-[\-\w]+$/),pe=It(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),me=It(/^(?:\w+script|data):/i),fe=It(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),ve=It(/^html$/i),ge=It(/^[a-z][.\w]*(-[.\w]+)+$/i);var _e=Object.freeze({__proto__:null,ARIA_ATTR:ue,ATTR_WHITESPACE:fe,CUSTOM_ELEMENT:ge,DATA_ATTR:ce,DOCTYPE_NAME:ve,ERB_EXPR:de,IS_ALLOWED_URI:pe,IS_SCRIPT_OR_DATA:me,MUSTACHE_EXPR:le,TMPLIT_EXPR:he});const ye=1,be=3,Ee=7,we=8,Te=9,Se=function(){return"undefined"==typeof window?null:window};var Ae=function t(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Se();const i=e=>t(e);if(i.version="3.2.3",i.removed=[],!e||!e.document||e.document.nodeType!==Te)return i.isSupported=!1,i;let{document:s}=e;const n=s,r=n.currentScript,{DocumentFragment:o,HTMLTemplateElement:a,Node:l,Element:d,NodeFilter:h,NamedNodeMap:c=e.NamedNodeMap||e.MozNamedAttrMap,HTMLFormElement:u,DOMParser:p,trustedTypes:m}=e,f=d.prototype,v=Zt(f,"cloneNode"),g=Zt(f,"remove"),_=Zt(f,"nextSibling"),y=Zt(f,"childNodes"),b=Zt(f,"parentNode");if("function"==typeof a){const t=s.createElement("template");t.content&&t.content.ownerDocument&&(s=t.content.ownerDocument)}let E,w="";const{implementation:T,createNodeIterator:S,createDocumentFragment:A,getElementsByTagName:L}=s,{importNode:O}=n;let C={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]};i.isSupported="function"==typeof Tt&&"function"==typeof b&&T&&void 0!==T.createHTMLDocument;const{MUSTACHE_EXPR:I,ERB_EXPR:P,TMPLIT_EXPR:N,DATA_ATTR:x,ARIA_ATTR:M,IS_SCRIPT_OR_DATA:k,ATTR_WHITESPACE:D,CUSTOM_ELEMENT:R}=_e;let{IS_ALLOWED_URI:$}=_e,F=null;const H=Kt({},[...Xt,...Jt,...Qt,...ee,...se]);let U=null;const z=Kt({},[...ne,...re,...oe,...ae]);let B=Object.seal(Pt(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),V=null,q=null,G=!0,W=!0,K=!1,j=!0,Y=!1,Z=!0,X=!1,J=!1,Q=!1,tt=!1,et=!1,it=!1,st=!0,nt=!1,rt=!0,ot=!1,at={},lt=null;const dt=Kt({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let ht=null;const ct=Kt({},["audio","video","img","source","image","track"]);let ut=null;const pt=Kt({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),mt="http://www.w3.org/1998/Math/MathML",ft="http://www.w3.org/2000/svg",vt="http://www.w3.org/1999/xhtml";let gt=vt,_t=!1,yt=null;const bt=Kt({},[mt,ft,vt],$t);let Et=Kt({},["mi","mo","mn","ms","mtext"]),wt=Kt({},["annotation-xml"]);const St=Kt({},["title","style","font","a","script"]);let At=null;const Lt=["application/xhtml+xml","text/html"];let Ot=null,It=null;const Nt=s.createElement("form"),xt=function(t){return t instanceof RegExp||t instanceof Function},Gt=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!It||It!==t){if(t&&"object"==typeof t||(t={}),t=Yt(t),At=-1===Lt.indexOf(t.PARSER_MEDIA_TYPE)?"text/html":t.PARSER_MEDIA_TYPE,Ot="application/xhtml+xml"===At?$t:Rt,F=Bt(t,"ALLOWED_TAGS")?Kt({},t.ALLOWED_TAGS,Ot):H,U=Bt(t,"ALLOWED_ATTR")?Kt({},t.ALLOWED_ATTR,Ot):z,yt=Bt(t,"ALLOWED_NAMESPACES")?Kt({},t.ALLOWED_NAMESPACES,$t):bt,ut=Bt(t,"ADD_URI_SAFE_ATTR")?Kt(Yt(pt),t.ADD_URI_SAFE_ATTR,Ot):pt,ht=Bt(t,"ADD_DATA_URI_TAGS")?Kt(Yt(ct),t.ADD_DATA_URI_TAGS,Ot):ct,lt=Bt(t,"FORBID_CONTENTS")?Kt({},t.FORBID_CONTENTS,Ot):dt,V=Bt(t,"FORBID_TAGS")?Kt({},t.FORBID_TAGS,Ot):{},q=Bt(t,"FORBID_ATTR")?Kt({},t.FORBID_ATTR,Ot):{},at=!!Bt(t,"USE_PROFILES")&&t.USE_PROFILES,G=!1!==t.ALLOW_ARIA_ATTR,W=!1!==t.ALLOW_DATA_ATTR,K=t.ALLOW_UNKNOWN_PROTOCOLS||!1,j=!1!==t.ALLOW_SELF_CLOSE_IN_ATTR,Y=t.SAFE_FOR_TEMPLATES||!1,Z=!1!==t.SAFE_FOR_XML,X=t.WHOLE_DOCUMENT||!1,tt=t.RETURN_DOM||!1,et=t.RETURN_DOM_FRAGMENT||!1,it=t.RETURN_TRUSTED_TYPE||!1,Q=t.FORCE_BODY||!1,st=!1!==t.SANITIZE_DOM,nt=t.SANITIZE_NAMED_PROPS||!1,rt=!1!==t.KEEP_CONTENT,ot=t.IN_PLACE||!1,$=t.ALLOWED_URI_REGEXP||pe,gt=t.NAMESPACE||vt,Et=t.MATHML_TEXT_INTEGRATION_POINTS||Et,wt=t.HTML_INTEGRATION_POINTS||wt,B=t.CUSTOM_ELEMENT_HANDLING||{},t.CUSTOM_ELEMENT_HANDLING&&xt(t.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(B.tagNameCheck=t.CUSTOM_ELEMENT_HANDLING.tagNameCheck),t.CUSTOM_ELEMENT_HANDLING&&xt(t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(B.attributeNameCheck=t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),t.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(B.allowCustomizedBuiltInElements=t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Y&&(W=!1),et&&(tt=!0),at&&(F=Kt({},se),U=[],!0===at.html&&(Kt(F,Xt),Kt(U,ne)),!0===at.svg&&(Kt(F,Jt),Kt(U,re),Kt(U,ae)),!0===at.svgFilters&&(Kt(F,Qt),Kt(U,re),Kt(U,ae)),!0===at.mathMl&&(Kt(F,ee),Kt(U,oe),Kt(U,ae))),t.ADD_TAGS&&(F===H&&(F=Yt(F)),Kt(F,t.ADD_TAGS,Ot)),t.ADD_ATTR&&(U===z&&(U=Yt(U)),Kt(U,t.ADD_ATTR,Ot)),t.ADD_URI_SAFE_ATTR&&Kt(ut,t.ADD_URI_SAFE_ATTR,Ot),t.FORBID_CONTENTS&&(lt===dt&&(lt=Yt(lt)),Kt(lt,t.FORBID_CONTENTS,Ot)),rt&&(F["#text"]=!0),X&&Kt(F,["html","head","body"]),F.table&&(Kt(F,["tbody"]),delete V.tbody),t.TRUSTED_TYPES_POLICY){if("function"!=typeof t.TRUSTED_TYPES_POLICY.createHTML)throw qt('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof t.TRUSTED_TYPES_POLICY.createScriptURL)throw qt('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');E=t.TRUSTED_TYPES_POLICY,w=E.createHTML("")}else void 0===E&&(E=function(t,e){if("object"!=typeof t||"function"!=typeof t.createPolicy)return null;let i=null;const s="data-tt-policy-suffix";e&&e.hasAttribute(s)&&(i=e.getAttribute(s));const n="dompurify"+(i?"#"+i:"");try{return t.createPolicy(n,{createHTML(t){return t},createScriptURL(t){return t}})}catch(t){return null}}(m,r)),null!==E&&"string"==typeof w&&(w=E.createHTML(""));Ct&&Ct(t),It=t}},Wt=Kt({},[...Jt,...Qt,...te]),jt=Kt({},[...ee,...ie]),le=function(t){Dt(i.removed,{element:t});try{b(t).removeChild(t)}catch(e){g(t)}},de=function(t,e){try{Dt(i.removed,{attribute:e.getAttributeNode(t),from:e})}catch(t){Dt(i.removed,{attribute:null,from:e})}if(e.removeAttribute(t),"is"===t)if(tt||et)try{le(e)}catch(t){}else try{e.setAttribute(t,"")}catch(t){}},he=function(t){let e=null,i=null;if(Q)t="<remove></remove>"+t;else{const e=Ft(t,/^[\r\n\t ]+/);i=e&&e[0]}"application/xhtml+xml"===At&&gt===vt&&(t='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+t+"</body></html>");const n=E?E.createHTML(t):t;if(gt===vt)try{e=(new p).parseFromString(n,At)}catch(t){}if(!e||!e.documentElement){e=T.createDocument(gt,"template",null);try{e.documentElement.innerHTML=_t?w:n}catch(t){}}const r=e.body||e.documentElement;return t&&i&&r.insertBefore(s.createTextNode(i),r.childNodes[0]||null),gt===vt?L.call(e,X?"html":"body")[0]:X?e.documentElement:r},ce=function(t){return S.call(t.ownerDocument||t,t,h.SHOW_ELEMENT|h.SHOW_COMMENT|h.SHOW_TEXT|h.SHOW_PROCESSING_INSTRUCTION|h.SHOW_CDATA_SECTION,null)},ue=function(t){return t instanceof u&&("string"!=typeof t.nodeName||"string"!=typeof t.textContent||"function"!=typeof t.removeChild||!(t.attributes instanceof c)||"function"!=typeof t.removeAttribute||"function"!=typeof t.setAttribute||"string"!=typeof t.namespaceURI||"function"!=typeof t.insertBefore||"function"!=typeof t.hasChildNodes)},me=function(t){return"function"==typeof l&&t instanceof l};function fe(t,e,s){Mt(t,(t=>{t.call(i,e,s,It)}))}const ge=function(t){let e=null;if(fe(C.beforeSanitizeElements,t,null),ue(t))return le(t),!0;const s=Ot(t.nodeName);if(fe(C.uponSanitizeElement,t,{tagName:s,allowedTags:F}),t.hasChildNodes()&&!me(t.firstElementChild)&&Vt(/<[/\w]/g,t.innerHTML)&&Vt(/<[/\w]/g,t.textContent))return le(t),!0;if(t.nodeType===Ee)return le(t),!0;if(Z&&t.nodeType===we&&Vt(/<[/\w]/g,t.data))return le(t),!0;if(!F[s]||V[s]){if(!V[s]&&Le(s)){if(B.tagNameCheck instanceof RegExp&&Vt(B.tagNameCheck,s))return!1;if(B.tagNameCheck instanceof Function&&B.tagNameCheck(s))return!1}if(rt&&!lt[s]){const e=b(t)||t.parentNode,i=y(t)||t.childNodes;if(i&&e){for(let s=i.length-1;s>=0;--s){const n=v(i[s],!0);n.__removalCount=(t.__removalCount||0)+1,e.insertBefore(n,_(t))}}}return le(t),!0}return t instanceof d&&!function(t){let e=b(t);e&&e.tagName||(e={namespaceURI:gt,tagName:"template"});const i=Rt(t.tagName),s=Rt(e.tagName);return!!yt[t.namespaceURI]&&(t.namespaceURI===ft?e.namespaceURI===vt?"svg"===i:e.namespaceURI===mt?"svg"===i&&("annotation-xml"===s||Et[s]):Boolean(Wt[i]):t.namespaceURI===mt?e.namespaceURI===vt?"math"===i:e.namespaceURI===ft?"math"===i&&wt[s]:Boolean(jt[i]):t.namespaceURI===vt?!(e.namespaceURI===ft&&!wt[s])&&!(e.namespaceURI===mt&&!Et[s])&&!jt[i]&&(St[i]||!Wt[i]):!("application/xhtml+xml"!==At||!yt[t.namespaceURI]))}(t)?(le(t),!0):"noscript"!==s&&"noembed"!==s&&"noframes"!==s||!Vt(/<\/no(script|embed|frames)/i,t.innerHTML)?(Y&&t.nodeType===be&&(e=t.textContent,Mt([I,P,N],(t=>{e=Ht(e,t," ")})),t.textContent!==e&&(Dt(i.removed,{element:t.cloneNode()}),t.textContent=e)),fe(C.afterSanitizeElements,t,null),!1):(le(t),!0)},Ae=function(t,e,i){if(st&&("id"===e||"name"===e)&&(i in s||i in Nt))return!1;if(W&&!q[e]&&Vt(x,e));else if(G&&Vt(M,e));else if(!U[e]||q[e]){if(!(Le(t)&&(B.tagNameCheck instanceof RegExp&&Vt(B.tagNameCheck,t)||B.tagNameCheck instanceof Function&&B.tagNameCheck(t))&&(B.attributeNameCheck instanceof RegExp&&Vt(B.attributeNameCheck,e)||B.attributeNameCheck instanceof Function&&B.attributeNameCheck(e))||"is"===e&&B.allowCustomizedBuiltInElements&&(B.tagNameCheck instanceof RegExp&&Vt(B.tagNameCheck,i)||B.tagNameCheck instanceof Function&&B.tagNameCheck(i))))return!1}else if(ut[e]);else if(Vt($,Ht(i,D,"")));else if("src"!==e&&"xlink:href"!==e&&"href"!==e||"script"===t||0!==Ut(i,"data:")||!ht[t]){if(K&&!Vt(k,Ht(i,D,"")));else if(i)return!1}else;return!0},Le=function(t){return"annotation-xml"!==t&&Ft(t,R)},Oe=function(t){fe(C.beforeSanitizeAttributes,t,null);const{attributes:e}=t;if(!e||ue(t))return;const s={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:U,forceKeepAttr:void 0};let n=e.length;for(;n--;){const r=e[n],{name:o,namespaceURI:a,value:l}=r,d=Ot(o);let h="value"===o?l:zt(l);if(s.attrName=d,s.attrValue=h,s.keepAttr=!0,s.forceKeepAttr=void 0,fe(C.uponSanitizeAttribute,t,s),h=s.attrValue,!nt||"id"!==d&&"name"!==d||(de(o,t),h="user-content-"+h),Z&&Vt(/((--!?|])>)|<\/(style|title)/i,h)){de(o,t);continue}if(s.forceKeepAttr)continue;if(de(o,t),!s.keepAttr)continue;if(!j&&Vt(/\/>/i,h)){de(o,t);continue}Y&&Mt([I,P,N],(t=>{h=Ht(h,t," ")}));const c=Ot(t.nodeName);if(Ae(c,d,h)){if(E&&"object"==typeof m&&"function"==typeof m.getAttributeType)if(a);else switch(m.getAttributeType(c,d)){case"TrustedHTML":h=E.createHTML(h);break;case"TrustedScriptURL":h=E.createScriptURL(h)}try{a?t.setAttributeNS(a,o,h):t.setAttribute(o,h),ue(t)?le(t):kt(i.removed)}catch(t){}}}fe(C.afterSanitizeAttributes,t,null)},Ce=function t(e){let i=null;const s=ce(e);for(fe(C.beforeSanitizeShadowDOM,e,null);i=s.nextNode();)fe(C.uponSanitizeShadowNode,i,null),ge(i),Oe(i),i.content instanceof o&&t(i.content);fe(C.afterSanitizeShadowDOM,e,null)};return i.sanitize=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=null,r=null,a=null,d=null;if(_t=!t,_t&&(t="\x3c!--\x3e"),"string"!=typeof t&&!me(t)){if("function"!=typeof t.toString)throw qt("toString is not a function");if("string"!=typeof(t=t.toString()))throw qt("dirty is not a string, aborting")}if(!i.isSupported)return t;if(J||Gt(e),i.removed=[],"string"==typeof t&&(ot=!1),ot){if(t.nodeName){const e=Ot(t.nodeName);if(!F[e]||V[e])throw qt("root node is forbidden and cannot be sanitized in-place")}}else if(t instanceof l)s=he("\x3c!----\x3e"),r=s.ownerDocument.importNode(t,!0),r.nodeType===ye&&"BODY"===r.nodeName||"HTML"===r.nodeName?s=r:s.appendChild(r);else{if(!tt&&!Y&&!X&&-1===t.indexOf("<"))return E&&it?E.createHTML(t):t;if(s=he(t),!s)return tt?null:it?w:""}s&&Q&&le(s.firstChild);const h=ce(ot?t:s);for(;a=h.nextNode();)ge(a),Oe(a),a.content instanceof o&&Ce(a.content);if(ot)return t;if(tt){if(et)for(d=A.call(s.ownerDocument);s.firstChild;)d.appendChild(s.firstChild);else d=s;return(U.shadowroot||U.shadowrootmode)&&(d=O.call(n,d,!0)),d}let c=X?s.outerHTML:s.innerHTML;return X&&F["!doctype"]&&s.ownerDocument&&s.ownerDocument.doctype&&s.ownerDocument.doctype.name&&Vt(ve,s.ownerDocument.doctype.name)&&(c="<!DOCTYPE "+s.ownerDocument.doctype.name+">\n"+c),Y&&Mt([I,P,N],(t=>{c=Ht(c,t," ")})),E&&it?E.createHTML(c):c},i.setConfig=function(){Gt(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),J=!0},i.clearConfig=function(){It=null,J=!1},i.isValidAttribute=function(t,e,i){It||Gt({});const s=Ot(t),n=Ot(e);return Ae(s,n,i)},i.addHook=function(t,e){"function"==typeof e&&Dt(C[t],e)},i.removeHook=function(t){return kt(C[t])},i.removeHooks=function(t){C[t]=[]},i.removeAllHooks=function(){C={afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}},i}();var Le=()=>{let t="",e=Ae.sanitize(window.location.search);const i=Ae.sanitize(window.location.hash),s=i.indexOf("?");return-1===s||t||(e=i.substring(s)),t=new URLSearchParams(e),{queryParams:t,hash:i}};var Oe=t=>{let e="";const i=t.replace(/(>|<)/gi,"").split(/(vi\/|v=|\/v\/|youtu\.be\/|\/embed\/)/);return void 0!==i[2]?[e]=i[2].split(/[^0-9a-z_-]/i):e=i,Array.isArray(e)?e[0]:e};var Ce=t=>{const e=t.getBoundingClientRect();return e.top<window.innerHeight&&e.bottom>=0};class Ie{sendEvent(t,e,i){const s=document.createEvent("CustomEvent");s.initCustomEvent(t,!1,!1,e),(i||this.element).dispatchEvent(s)}}var Pe=t=>{let e="";const i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";for(let s=0;s<t;s+=1)e+=i.charAt(Math.floor(62*Math.random()));return e};var Ne=(t,e,i)=>{const s=new Date;s.setTime(s.getTime()+24*i*60*60*1e3);const n=`expires=${s.toUTCString()}`,r=`;domain=${window.location.hostname.replace(/^www\./,"")}`;document.cookie=`${t}=${e};${n}${r};path=/;secure;SameSite=Strict`};var xe,Me=t=>{if(!t)return!1;const e=t.split("@");if(2!==e.length)return!1;const i=e[0],s=e[1];if(i.length>64||s.length>255)return!1;return!s.split(".").some((t=>t.length>63))&&/^[-!#$%&'*+\/0-9=?A-Z^_a-z`{|}~](\.?[-!#$%&'*+\/0-9=?A-Z^_a-z`{|}~])*@[a-zA-Z0-9](-*\.?[a-zA-Z0-9])*\.[a-zA-Z](-?[a-zA-Z0-9])+$/.test(t)};!function(t){t.UK="UK",t.GB="GB",t.JE="JE",t.GG="GG",t.IM="IM",t.US="US",t.CA="CA",t.IE="IE",t.DE="DE",t.JP="JP",t.FR="FR",t.AU="AU",t.IT="IT",t.CH="CH",t.AT="AT",t.ES="ES",t.NL="NL",t.BE="BE",t.DK="DK",t.SE="SE",t.NO="NO",t.BR="BR",t.PT="PT",t.FI="FI",t.AX="AX",t.KR="KR",t.CN="CN",t.TW="TW",t.SG="SG",t.DZ="DZ",t.AD="AD",t.AR="AR",t.AM="AM",t.AZ="AZ",t.BH="BH",t.BD="BD",t.BB="BB",t.BY="BY",t.BM="BM",t.BA="BA",t.IO="IO",t.BN="BN",t.BG="BG",t.KH="KH",t.CV="CV",t.CL="CL",t.CR="CR",t.HR="HR",t.CY="CY",t.CZ="CZ",t.DO="DO",t.EC="EC",t.EG="EG",t.EE="EE",t.FO="FO",t.GE="GE",t.GR="GR",t.GL="GL",t.GT="GT",t.HT="HT",t.HN="HN",t.HU="HU",t.IS="IS",t.IN="IN",t.ID="ID",t.IL="IL",t.JO="JO",t.KZ="KZ",t.KE="KE",t.KW="KW",t.KY="KY",t.LA="LA",t.LV="LV",t.LB="LB",t.LI="LI",t.LT="LT",t.LU="LU",t.MK="MK",t.MY="MY",t.MV="MV",t.MT="MT",t.MU="MU",t.MX="MX",t.MD="MD",t.MC="MC",t.MA="MA",t.NP="NP",t.NZ="NZ",t.NI="NI",t.NG="NG",t.OM="OM",t.PA="PA",t.PK="PK",t.PY="PY",t.PH="PH",t.PL="PL",t.PR="PR",t.RO="RO",t.RU="RU",t.SM="SM",t.SA="SA",t.SN="SN",t.SK="SK",t.SI="SI",t.ZA="ZA",t.LK="LK",t.TJ="TJ",t.TH="TH",t.TN="TN",t.TR="TR",t.TM="TM",t.UA="UA",t.UY="UY",t.UZ="UZ",t.VA="VA",t.VE="VE",t.ZM="ZM",t.AS="AS",t.CC="CC",t.CK="CK",t.RS="RS",t.ME="ME",t.CS="CS",t.YU="YU",t.CX="CX",t.ET="ET",t.FK="FK",t.NF="NF",t.FM="FM",t.GF="GF",t.GN="GN",t.GP="GP",t.GS="GS",t.GU="GU",t.GW="GW",t.HM="HM",t.IQ="IQ",t.KG="KG",t.LR="LR",t.LS="LS",t.MG="MG",t.MH="MH",t.MN="MN",t.MP="MP",t.MQ="MQ",t.NC="NC",t.NE="NE",t.VI="VI",t.VN="VN",t.PF="PF",t.PG="PG",t.PM="PM",t.PN="PN",t.PW="PW",t.RE="RE",t.SH="SH",t.SJ="SJ",t.SO="SO",t.SZ="SZ",t.TC="TC",t.WF="WF",t.XK="XK",t.YT="YT",t.PE="PE",t.INTL="INTL"}(xe||(xe={}));const ke=new Map([[xe.UK,/^([A-Z]){1}([0-9][0-9]|[0-9]|[A-Z][0-9][A-Z]|[A-Z][0-9][0-9]|[A-Z][0-9]|[0-9][A-Z]){1}([ ])?([0-9][A-z][A-z]){1}$/i],[xe.GB,/^([A-Z]){1}([0-9][0-9]|[0-9]|[A-Z][0-9][A-Z]|[A-Z][0-9][0-9]|[A-Z][0-9]|[0-9][A-Z]){1}([ ])?([0-9][A-z][A-z]){1}$/i],[xe.JE,/^JE\d[\dA-Z]?[ ]?\d[ABD-HJLN-UW-Z]{2}$/],[xe.GG,/^GY\d[\dA-Z]?[ ]?\d[ABD-HJLN-UW-Z]{2}$/],[xe.IM,/^IM\d[\dA-Z]?[ ]?\d[ABD-HJLN-UW-Z]{2}$/],[xe.US,/^([0-9]{5})(?:-([0-9]{4}))?$/],[xe.CA,/^([ABCEGHJKLMNPRSTVXY][0-9][ABCEGHJKLMNPRSTVWXYZ])\s*([0-9][ABCEGHJKLMNPRSTVWXYZ][0-9])$/i],[xe.IE,/^([AC-FHKNPRTV-Y][0-9]{2}|D6W)[ -]?[0-9AC-FHKNPRTV-Y]{4}$/],[xe.DE,/^\d{5}$/],[xe.JP,/^\d{3}-\d{4}$/],[xe.FR,/^\d{2}[ ]?\d{3}$/],[xe.AU,/^\d{4}$/],[xe.IT,/^\d{5}$/],[xe.CH,/^\d{4}$/],[xe.AT,/^(?!0)\d{4}$/],[xe.ES,/^(?:0[1-9]|[1-4]\d|5[0-2])\d{3}$/],[xe.NL,/^\d{4}[ ]?[A-Z]{2}$/],[xe.BE,/^\d{4}$/],[xe.DK,/^\d{4}$/],[xe.SE,/^(SE-)?\d{3}[ ]?\d{2}$/],[xe.NO,/^\d{4}$/],[xe.BR,/^\d{5}[\-]?\d{3}$/],[xe.PT,/^\d{4}([\-]\d{3})?$/],[xe.FI,/^(FI-|AX-)?\d{5}$/],[xe.AX,/^22\d{3}$/],[xe.KR,/^\d{5}$/],[xe.CN,/^\d{6}$/],[xe.TW,/^\d{3}(\d{2,3})?$/],[xe.SG,/^\d{6}$/],[xe.DZ,/^\d{5}$/],[xe.AD,/^AD\d{3}$/],[xe.AR,/^([A-HJ-NP-Z])?\d{4}([A-Z]{3})?$/],[xe.AM,/^(37)?\d{4}$/],[xe.AZ,/^\d{4}$/],[xe.BH,/^((1[0-2]|[2-9])\d{2})?$/],[xe.BD,/^\d{4}$/],[xe.BB,/^(BB\d{5})?$/],[xe.BY,/^\d{6}$/],[xe.BM,/^[A-Z]{2}[ ]?[A-Z0-9]{2}$/],[xe.BA,/^\d{5}$/],[xe.IO,/^BBND 1ZZ$/],[xe.BN,/^[A-Z]{2}[ ]?\d{4}$/],[xe.BG,/^\d{4}$/],[xe.KH,/^\d{5}$/],[xe.CV,/^\d{4}$/],[xe.CL,/^\d{7}$/],[xe.CR,/^(\d{4,5}|\d{3}-\d{4})$/],[xe.HR,/^(HR-)?\d{5}$/],[xe.CY,/^\d{4}$/],[xe.CZ,/^\d{3}[ ]?\d{2}$/],[xe.DO,/^\d{5}$/],[xe.EC,/^([A-Z]\d{4}[A-Z]|(?:[A-Z]{2})?\d{6})?$/],[xe.EG,/^\d{5}$/],[xe.EE,/^\d{5}$/],[xe.FO,/^\d{3}$/],[xe.GE,/^\d{4}$/],[xe.GR,/^\d{3}[ ]?\d{2}$/],[xe.GL,/^39\d{2}$/],[xe.GT,/^\d{5}$/],[xe.HT,/^\d{4}$/],[xe.HN,/^(?:\d{5})?$/],[xe.HU,/^\d{4}$/],[xe.IS,/^\d{3}$/],[xe.IN,/^\d{6}$/],[xe.ID,/^\d{5}$/],[xe.IL,/^\d{5,7}$/],[xe.JO,/^\d{5}$/],[xe.KZ,/^\d{6}$/],[xe.KE,/^\d{5}$/],[xe.KW,/^\d{5}$/],[xe.KY,/^KY[123]-\d{4}$/],[xe.LA,/^\d{5}$/],[xe.LV,/^(LV-)?\d{4}$/],[xe.LB,/^(\d{4}([ ]?\d{4})?)?$/],[xe.LI,/^(948[5-9])|(949[0-7])$/],[xe.LT,/^(LT-)?\d{5}$/],[xe.LU,/^(L-)?\d{4}$/],[xe.MK,/^\d{4}$/],[xe.MY,/^\d{5}$/],[xe.MV,/^\d{5}$/],[xe.MT,/^[A-Z]{3}[ ]?\d{2,4}$/],[xe.MU,/^((\d|[A-Z])\d{4})?$/],[xe.MX,/^\d{5}$/],[xe.MD,/^\d{4}$/],[xe.MC,/^980\d{2}$/],[xe.MA,/^\d{5}$/],[xe.NP,/^\d{5}$/],[xe.NZ,/^\d{4}$/],[xe.NI,/^((\d{4}-)?\d{3}-\d{3}(-\d{1})?)?$/],[xe.NG,/^(\d{6})?$/],[xe.OM,/^(PC )?\d{3}$/],[xe.PA,/^\d{4}$/],[xe.PK,/^\d{5}$/],[xe.PY,/^\d{4}$/],[xe.PH,/^\d{4}$/],[xe.PL,/^\d{2}-\d{3}$/],[xe.PR,/^00[679]\d{2}([ \-]\d{4})?$/],[xe.RO,/^\d{6}$/],[xe.RU,/^\d{6}$/],[xe.SM,/^4789\d$/],[xe.SA,/^\d{5}$/],[xe.SN,/^\d{5}$/],[xe.SK,/^\d{3}[ ]?\d{2}$/],[xe.SI,/^(SI-)?\d{4}$/],[xe.ZA,/^\d{4}$/],[xe.LK,/^\d{5}$/],[xe.TJ,/^\d{6}$/],[xe.TH,/^\d{5}$/],[xe.TN,/^\d{4}$/],[xe.TR,/^\d{5}$/],[xe.TM,/^\d{6}$/],[xe.UA,/^\d{5}$/],[xe.UY,/^\d{5}$/],[xe.UZ,/^\d{6}$/],[xe.VA,/^00120$/],[xe.VE,/^\d{4}$/],[xe.ZM,/^\d{5}$/],[xe.AS,/^96799$/],[xe.CC,/^6799$/],[xe.CK,/^\d{4}$/],[xe.RS,/^\d{5,6}$/],[xe.ME,/^8\d{4}$/],[xe.CS,/^\d{5}$/],[xe.YU,/^\d{5}$/],[xe.CX,/^6798$/],[xe.ET,/^\d{4}$/],[xe.FK,/^FIQQ 1ZZ$/],[xe.NF,/^2899$/],[xe.FM,/^(9694[1-4])([ \-]\d{4})?$/],[xe.GF,/^9[78]3\d{2}$/],[xe.GN,/^\d{3}$/],[xe.GP,/^9[78][01]\d{2}$/],[xe.GS,/^SIQQ 1ZZ$/],[xe.GU,/^969[123]\d([ \-]\d{4})?$/],[xe.GW,/^\d{4}$/],[xe.HM,/^\d{4}$/],[xe.IQ,/^\d{5}$/],[xe.KG,/^\d{6}$/],[xe.LR,/^\d{4}$/],[xe.LS,/^\d{3}$/],[xe.MG,/^\d{3}$/],[xe.MH,/^969[67]\d([ \-]\d{4})?$/],[xe.MN,/^\d{6}$/],[xe.MP,/^9695[012]([ \-]\d{4})?$/],[xe.MQ,/^9[78]2\d{2}$/],[xe.NC,/^988\d{2}$/],[xe.NE,/^\d{4}$/],[xe.VI,/^008(([0-4]\d)|(5[01]))([ \-]\d{4})?$/],[xe.VN,/^\d{6}$/],[xe.PF,/^987\d{2}$/],[xe.PG,/^\d{3}$/],[xe.PM,/^9[78]5\d{2}$/],[xe.PN,/^PCRN 1ZZ$/],[xe.PW,/^96940$/],[xe.RE,/^9[78]4\d{2}$/],[xe.SH,/^(ASCN|STHL) 1ZZ$/],[xe.SJ,/^\d{4}$/],[xe.SO,/^\d{5}$/],[xe.SZ,/^[HLMS]\d{3}$/],[xe.TC,/^TKCA 1ZZ$/],[xe.WF,/^986\d{2}$/],[xe.XK,/^\d{5}$/],[xe.YT,/^976\d{2}$/],[xe.PE,/^\d{5}$/],[xe.INTL,/^(?:[A-Z0-9]+([- ]?[A-Z0-9]+)*)?$/i]]),De=(t,e)=>{if(!ke.has(e))throw Error(`Invalid country code: ${e}`);return ke.get(e).test(t)};var Re=t=>De(t,"US")?{valid:De(t,"US"),country:"US"}:De(t,"CA")?{valid:De(t,"CA"),country:"CA"}:De(t,"GB")?{valid:De(t,"GB"),country:"GB"}:De(t,"IE")?{valid:De(t,"IE"),country:"IE"}:De(t,"FR")?{valid:De(t,"FR"),country:"FR"}:De(t,"DE")?{valid:De(t,"DE"),country:"DE"}:De(t,"FI")?{valid:De(t,"FI"),country:"FI"}:De(t,"DK")?{valid:De(t,"DK"),country:"DK"}:De(t,"INTL");class $e{constructor(t){this.ScrollService=t.ScrollService,this.init()}init(){return this.cacheDomReferences().setupHandlers().enable(),this}cacheDomReferences(){return this.modules=document.querySelectorAll(yt.DATA_VISIBLE),this}setupHandlers(){return this.onScrollHandler=this.onScroll.bind(this),this.onModuleUpdateHandler=this.updateModules.bind(this),this}enable(){return window.setTimeout(this.onScrollHandler,500),this.ScrollService.addCallback(this.onScrollHandler),document.body.addEventListener(vt.UPDATE_IN_VIEWPORT_MODULES,this.onModuleUpdateHandler),this}onScroll(){return Array.prototype.forEach.call(this.modules,(t=>{Ce(t)?("false"===t.getAttribute(gt.DATA_VISIBLE)&&t.setAttribute(gt.DATA_VISIBLE,!0),t.hasAttribute(yt.DATA_HAS_ANIMATED)||t.setAttribute(yt.DATA_HAS_ANIMATED,!0)):"true"===t.getAttribute(gt.DATA_VISIBLE)&&t.setAttribute(gt.DATA_VISIBLE,!1);const e=t.getBoundingClientRect(),i=t.getAttribute(yt.DATA_POSITION),s=e.top>=window.innerHeight?ft.BELOW_VIEWPORT:ft.IN_VIEWPORT,n=e.bottom<0?ft.ABOVE_VIEWPORT:s,r=e.bottom>window.innerHeight?ft.BELOW_BOTTOM:ft.ABOVE_BOTTOM,o=e.bottom<=window.innerHeight/1.25?ft.ABOVE_HALFWAY:ft.BELOW_HALFWAY;i!==n&&t.setAttribute(yt.DATA_POSITION,n),t.setAttribute(yt.DATA_BOTTOM,r),t.setAttribute(yt.DATA_HALFWAY,o)})),this}updateModules(){return this.cacheDomReferences().onScroll(),this}}class Fe{constructor(t){this.element=t,this.init()}init(){return this.cacheDomReferences().setupHandlers().enable().setupWindowHandlers(),this}cacheDomReferences(){return this.accordionButton=this.element.querySelector(yt.ACCORDION_BUTTON),this.accordionPanel=this.element.querySelector(yt.ACCORDION_PANEL),this.accordionPanel.getAttribute(mt.HIDDEN)===gt.TRUE&&(this.accordionPanel.style.height="0px"),this}setupHandlers(){return this.onClick=this.onClick.bind(this),this}setupWindowHandlers(){window.addEventListener("load",(()=>{window.location.hash&&this.constructor.expandSectionWithHash(window.location.hash)})),window.addEventListener("hashchange",(()=>{window.location.hash&&this.constructor.expandSectionWithHash(window.location.hash)}))}enable(){return this.accordionButton.addEventListener(vt.CLICK,this.onClick),this.element.addEventListener(vt.FILTER_FORM_SUBMIT,this.onClick),this}onClick(){const t=this.accordionButton.getAttribute(mt.EXPANDED)===gt.TRUE;return this.accordionButton.setAttribute(mt.EXPANDED,!t),this.accordionPanel.setAttribute(mt.HIDDEN,t),t?this.constructor.collapseSection(this.accordionPanel):this.constructor.expandSection(this.accordionPanel),this}static expandSectionWithHash(t){const e=document.querySelector(t);if(e){const t=e.querySelector(yt.ACCORDION_BUTTON),i=e.querySelector(yt.ACCORDION_PANEL);t.setAttribute(mt.EXPANDED,gt.TRUE),i.setAttribute(mt.HIDDEN,gt.FALSE),this.expandSection(i)}}static collapseSection(t){const e=t,i=e.scrollHeight,s=e.style.transition;e.style.transition="",requestAnimationFrame((()=>{e.style.height=`${i}px`,e.style.transition=s,requestAnimationFrame((()=>{e.style.height="0px"}))}))}static expandSection(t){const e=t,i=e.scrollHeight;e.style.height=`${i}px`,e.addEventListener(vt.TRANSITIONEND,(()=>{e.getAttribute(mt.HIDDEN)===gt.FALSE?e.style.height="auto":e.style.height="0px",e.removeEventListener(vt.TRANSITIONEND,(()=>{e.style.height=null}))}))}}class He extends Ie{constructor(t){super(),this.element=t,this.init()}init(){return this.cacheDomReferences().setupHandlers().enable(),this}cacheDomReferences(){this.form=this.element.querySelector(yt.FORM_ACTIONNETWORK),this.firstName=this.element.querySelector(yt.INPUT_GROUP_FIRST_NAME),this.firstNameInput=this.element.querySelector(yt.FORM_INPUT_FIRST_NAME),this.lastName=this.element.querySelector(yt.INPUT_GROUP_LAST_NAME),this.lastNameInput=this.element.querySelector(yt.FORM_INPUT_LAST_NAME),this.email=this.element.querySelector(yt.INPUT_GROUP_EMAIL),this.emailInput=this.element.querySelector(yt.FORM_INPUT_EMAIL),this.prevEmail="",this.prevZip="",this.zip=this.element.querySelector(yt.INPUT_GROUP_ZIP),this.zipInput=this.element.querySelector(yt.FORM_INPUT_ZIP),this.targetGroups=this.element.querySelector(".cdtargetgroups"),this.formPart=this.element.querySelector(".cdformpart"),this.resetDiv=this.element.querySelector(".cdtargetgroups__restart"),this.loader=this.element.querySelector(".cdformpart__loader"),this.resetLink=this.element.querySelector(".cdtargetgroups__restart-link"),this.endpoint=this.form&&this.form.dataset.actionNetwork?`${this.form.dataset.actionNetwork}?background_request=true`:"https://actionnetwork.org/api/v2/forms/03ce9525-efc2-40e9-8b33-25b893d93355/submissions?background_request=true",this.currentUrl=window.location.href;const{queryParams:t}=Le()||{};this.urlParams=t,this.source=this.urlParams.get("source"),this.refcode=this.urlParams.get("refcode"),this.utmSource=this.urlParams.get("utm_source"),this.emailReferrer=this.urlParams.get("email_referrer"),this.autoresponse=!1,this.form&&(this.autoresponse=this.form.dataset.autoresponse),this.sourcing=window.sourcing||{},this.resetDefaultText="",this.resetDiv&&(this.resetDefaultText=this.resetDiv.dataset.text),this.ocdId=wt(gt.OCD_ID_PARAM);const e=this.urlParams.get(gt.OCD_ID_PARAM);e&&(this.ocdId=e,Ne(gt.OCD_ID_PARAM,e,180)),this.zipcode=wt(gt.ZIP_PARAM);const i=this.urlParams.get(gt.ZIP_PARAM);i&&(this.zipcode=i,Ne(gt.ZIP_PARAM,i,180)),this.country=wt(gt.COUNTRY_PARAM);const s=this.urlParams.get(gt.COUNTRY_PARAM);return s&&(this.country=s,Ne(gt.COUNTRY_PARAM,s,180)),this.cdsData=window.cds||{},this.tgsData=window.tgs||{},this}setupHandlers(){return this.onSubmit=this.onSubmit.bind(this),this.fetchData=this.fetchData.bind(this),this.setTargetGroup=this.setTargetGroup.bind(this),this.reset=this.reset.bind(this),this}enable(){return this.form.addEventListener(vt.SUBMIT,this.onSubmit),this.zipInput&&this.zipInput.addEventListener("invalid",(()=>{this.zip.classList.add("error")})),this.emailInput&&this.emailInput.addEventListener("invalid",(()=>{this.email.classList.add("error")})),this.firstNameInput&&this.firstNameInput.addEventListener("invalid",(()=>{this.firstName.classList.add("error")})),this.lastNameInput&&this.lastNameInput.addEventListener("invalid",(()=>{this.lastName.classList.add("error")})),this.ocdId&&this.zipcode&&this.setTargetGroup(this.ocdId),this.resetLink&&this.resetLink.addEventListener(vt.CLICK,this.reset),this}onSubmit(t){t.preventDefault();const e=new FormData(this.form),i=e.get("email"),s=e.get("zip"),n=e.get("first-name"),r=e.get("last-name"),o=e.get("default_source");let a="",l=!1,d=!1;if(i&&(l=Me(i),l?this.email.classList.remove(ft.ERROR):this.email.classList.add(ft.ERROR)),s&&(d=Re(s),d.valid?this.zip.classList.remove(ft.ERROR):this.zip.classList.add(ft.ERROR)),d.valid&&l){const t={address:i,status:"subscribed"},e={postal_code:s};d.valid&&"N/A"!==d.country&&(e.country=d.country,a=d.country);const l={person:{}};l.person.email_addresses=[t],l.person.postal_addresses=[e],n&&(l.person.given_name=n),r&&(l.person.family_name=r),(o||this.source||this.emailReferrer)&&(l["action_network:referrer_data"]={},o&&o.includes("__team")?l["action_network:referrer_data"].source=o:(o||this.source)&&(l["action_network:referrer_data"].source=this.source||this.refcode||this.utmSource||o),this.emailReferrer&&(l["action_network:referrer_data"].email_referrer=this.emailReferrer),l["action_network:referrer_data"].website=this.currentUrl),this.autoresponse&&(l.triggers={autoresponse:{enabled:!0}}),fetch(this.endpoint,{method:"POST",body:JSON.stringify(l),headers:{"Content-Type":"application/json"}}).then((t=>{if(!t.ok)throw new Error(t.status);return t.json()})).then((()=>{this.form.dispatchEvent(new CustomEvent(vt.FORM_SUBMIT)),this.prevEmail=i,this.prevZip=s,s!==this.zipcode&&(Ne(gt.ZIP_PARAM,s,180),this.zipcode=s,He.navigate(s,gt.ZIP_PARAM)),a!==this.country&&(Ne(gt.COUNTRY_PARAM,a,180),this.country=a,He.navigate(a,gt.COUNTRY_PARAM)),this.form.classList.add(ft.HIDDEN),this.loader.classList.remove(ft.HIDDEN),this.attempt=1;const t=`${window.window.geo_api_proxy_url}?action=geo_api_proxy&zip=${s}`;"US"===a?this.fetchData(t):this.setTargetGroup("default")})).catch((t=>{}))}return this}fetchData(t){fetch(t).then((t=>{if(!t.ok)throw new Error(`HTTP ${t.status}`);return t.json()})).then((t=>{const e=t?.results?t:t?.data,i=e?.results?.[0]?.fields?.congressional_districts;if(this.cdsData&&i?.length>0){let t="default",e=11;i.forEach((i=>{if(i.ocd_id){const s=i.ocd_id,n=this.cdsData[s];this.tgsData[n]<=e&&(t=s,e=this.tgsData[n])}})),this.setTargetGroup(t)}else this.setTargetGroup("default")})).catch((e=>{this.attempt<3?(this.attempt+=1,this.fetchData(t)):this.setTargetGroup("default")}))}reset(t){t.preventDefault(),this.loader.classList.remove(ft.HIDDEN),this.element.classList.remove("target"),this.targetGroups.classList.add(ft.HIDDEN);const e=this.element.querySelector(`#${this.groupId}`);e&&e.classList.add(ft.HIDDEN),Ne(gt.ZIP_PARAM,"",180),Ne(gt.OCD_ID_PARAM,"",180),Ne(gt.COUNTRY_PARAM,"",180),this.zipcode="",this.ocdId="",this.country="",this.urlParams.delete(gt.COUNTRY_PARAM),this.urlParams.delete(gt.ZIP_PARAM),this.urlParams.delete(gt.OCD_ID_PARAM),window.history.pushState(null,null,`?${this.urlParams.toString()}`),this.formPart.classList.remove(ft.HIDDEN),this.form.classList.remove(ft.HIDDEN),this.loader.classList.add(ft.HIDDEN),this.emailInput.value=this.prevEmail,this.zipInput.value=this.prevZip,this.formPart.scrollIntoView({block:"start",inline:"nearest"})}setTargetGroup(t){const e=this.cdsData[t];this.loader.classList.add(ft.HIDDEN),t!==this.ocdId&&(Ne(gt.OCD_ID_PARAM,t,180),this.ocdId=t,He.navigate(t,gt.OCD_ID_PARAM)),this.formPart&&(this.formPart.classList.add(ft.HIDDEN),this.element.classList.add("target")),this.groupId!==e&&(this.groupId=e);const i=this.element.querySelector(`#${e}`);i&&i.classList.remove(ft.HIDDEN),this.resetLink.textContent=this.resetDefaultText.replace(/\[\[ZIP_CODE\]\]/g,this.zipcode),this.targetGroups&&(this.targetGroups.classList.remove(ft.HIDDEN),this.targetGroups.scrollIntoView({block:"start",inline:"nearest"}))}static navigate(t,e){const i=new URLSearchParams(window.location.search);i.set(e,t),t||i.delete(e),window.history.pushState({value:t},"",`?${i.toString()}`)}}var Ue=n(893);
/*!
* tabbable 6.2.0
* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
*/
var ze=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],Be=ze.join(","),Ve="undefined"==typeof Element,qe=Ve?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,Ge=!Ve&&Element.prototype.getRootNode?function(t){var e;return null==t||null===(e=t.getRootNode)||void 0===e?void 0:e.call(t)}:function(t){return null==t?void 0:t.ownerDocument},We=function t(e,i){var s;void 0===i&&(i=!0);var n=null==e||null===(s=e.getAttribute)||void 0===s?void 0:s.call(e,"inert");return""===n||"true"===n||i&&e&&t(e.parentNode)},Ke=function(t,e,i){if(We(t))return[];var s=Array.prototype.slice.apply(t.querySelectorAll(Be));return e&&qe.call(t,Be)&&s.unshift(t),s=s.filter(i)},je=function t(e,i,s){for(var n=[],r=Array.from(e);r.length;){var o=r.shift();if(!We(o,!1))if("SLOT"===o.tagName){var a=o.assignedElements(),l=t(a.length?a:o.children,!0,s);s.flatten?n.push.apply(n,l):n.push({scopeParent:o,candidates:l})}else{qe.call(o,Be)&&s.filter(o)&&(i||!e.includes(o))&&n.push(o);var d=o.shadowRoot||"function"==typeof s.getShadowRoot&&s.getShadowRoot(o),h=!We(d,!1)&&(!s.shadowRootFilter||s.shadowRootFilter(o));if(d&&h){var c=t(!0===d?o.children:d.children,!0,s);s.flatten?n.push.apply(n,c):n.push({scopeParent:o,candidates:c})}else r.unshift.apply(r,o.children)}}return n},Ye=function(t){return!isNaN(parseInt(t.getAttribute("tabindex"),10))},Ze=function(t){if(!t)throw new Error("No node provided");return t.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(t.tagName)||function(t){var e,i=null==t||null===(e=t.getAttribute)||void 0===e?void 0:e.call(t,"contenteditable");return""===i||"true"===i}(t))&&!Ye(t)?0:t.tabIndex},Xe=function(t,e){return t.tabIndex===e.tabIndex?t.documentOrder-e.documentOrder:t.tabIndex-e.tabIndex},Je=function(t){return"INPUT"===t.tagName},Qe=function(t){return function(t){return Je(t)&&"radio"===t.type}(t)&&!function(t){if(!t.name)return!0;var e,i=t.form||Ge(t),s=function(t){return i.querySelectorAll('input[type="radio"][name="'+t+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)e=s(window.CSS.escape(t.name));else try{e=s(t.name)}catch(t){return!1}var n=function(t,e){for(var i=0;i<t.length;i++)if(t[i].checked&&t[i].form===e)return t[i]}(e,t.form);return!n||n===t}(t)},ti=function(t){var e=t.getBoundingClientRect(),i=e.width,s=e.height;return 0===i&&0===s},ei=function(t,e){var i=e.displayCheck,s=e.getShadowRoot;if("hidden"===getComputedStyle(t).visibility)return!0;var n=qe.call(t,"details>summary:first-of-type")?t.parentElement:t;if(qe.call(n,"details:not([open]) *"))return!0;if(i&&"full"!==i&&"legacy-full"!==i){if("non-zero-area"===i)return ti(t)}else{if("function"==typeof s){for(var r=t;t;){var o=t.parentElement,a=Ge(t);if(o&&!o.shadowRoot&&!0===s(o))return ti(t);t=t.assignedSlot?t.assignedSlot:o||a===t.ownerDocument?o:a.host}t=r}if(function(t){var e,i,s,n,r=t&&Ge(t),o=null===(e=r)||void 0===e?void 0:e.host,a=!1;if(r&&r!==t)for(a=!!(null!==(i=o)&&void 0!==i&&null!==(s=i.ownerDocument)&&void 0!==s&&s.contains(o)||null!=t&&null!==(n=t.ownerDocument)&&void 0!==n&&n.contains(t));!a&&o;){var l,d,h;a=!(null===(d=o=null===(l=r=Ge(o))||void 0===l?void 0:l.host)||void 0===d||null===(h=d.ownerDocument)||void 0===h||!h.contains(o))}return a}(t))return!t.getClientRects().length;if("legacy-full"!==i)return!0}return!1},ii=function(t,e){return!(e.disabled||We(e)||function(t){return Je(t)&&"hidden"===t.type}(e)||ei(e,t)||function(t){return"DETAILS"===t.tagName&&Array.prototype.slice.apply(t.children).some((function(t){return"SUMMARY"===t.tagName}))}(e)||function(t){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(t.tagName))for(var e=t.parentElement;e;){if("FIELDSET"===e.tagName&&e.disabled){for(var i=0;i<e.children.length;i++){var s=e.children.item(i);if("LEGEND"===s.tagName)return!!qe.call(e,"fieldset[disabled] *")||!s.contains(t)}return!0}e=e.parentElement}return!1}(e))},si=function(t,e){return!(Qe(e)||Ze(e)<0||!ii(t,e))},ni=function(t){var e=parseInt(t.getAttribute("tabindex"),10);return!!(isNaN(e)||e>=0)},ri=function t(e){var i=[],s=[];return e.forEach((function(e,n){var r=!!e.scopeParent,o=r?e.scopeParent:e,a=function(t,e){var i=Ze(t);return i<0&&e&&!Ye(t)?0:i}(o,r),l=r?t(e.candidates):o;0===a?r?i.push.apply(i,l):i.push(o):s.push({documentOrder:n,tabIndex:a,item:e,isScope:r,content:l})})),s.sort(Xe).reduce((function(t,e){return e.isScope?t.push.apply(t,e.content):t.push(e.content),t}),[]).concat(i)},oi=function(t,e){if(e=e||{},!t)throw new Error("No node provided");return!1!==qe.call(t,Be)&&si(e,t)},ai=ze.concat("iframe").join(","),li=function(t,e){if(e=e||{},!t)throw new Error("No node provided");return!1!==qe.call(t,ai)&&ii(e,t)};
/*!
* focus-trap 7.5.4
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/
function di(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,s)}return i}function hi(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?di(Object(i),!0).forEach((function(e){ci(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):di(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function ci(t,e,i){return(e=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var s=i.call(t,e||"default");if("object"!=typeof s)return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var ui=function(t,e){if(t.length>0){var i=t[t.length-1];i!==e&&i.pause()}var s=t.indexOf(e);-1===s||t.splice(s,1),t.push(e)},pi=function(t,e){var i=t.indexOf(e);-1!==i&&t.splice(i,1),t.length>0&&t[t.length-1].unpause()},mi=function(t){return"Tab"===(null==t?void 0:t.key)||9===(null==t?void 0:t.keyCode)},fi=function(t){return mi(t)&&!t.shiftKey},vi=function(t){return mi(t)&&t.shiftKey},gi=function(t){return setTimeout(t,0)},_i=function(t,e){var i=-1;return t.every((function(t,s){return!e(t)||(i=s,!1)})),i},yi=function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),s=1;s<e;s++)i[s-1]=arguments[s];return"function"==typeof t?t.apply(void 0,i):t},bi=function(t){return t.target.shadowRoot&&"function"==typeof t.composedPath?t.composedPath()[0]:t.target},Ei=[],wi=function(t,e){var i,s=(null==e?void 0:e.document)||document,n=(null==e?void 0:e.trapStack)||Ei,r=hi({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:fi,isKeyBackward:vi},e),o={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0,recentNavEvent:void 0},a=function(t,e,i){return t&&void 0!==t[e]?t[e]:r[i||e]},l=function(t,e){var i="function"==typeof(null==e?void 0:e.composedPath)?e.composedPath():void 0;return o.containerGroups.findIndex((function(e){var s=e.container,n=e.tabbableNodes;return s.contains(t)||(null==i?void 0:i.includes(s))||n.find((function(e){return e===t}))}))},d=function(t){var e=r[t];if("function"==typeof e){for(var i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];e=e.apply(void 0,n)}if(!0===e&&(e=void 0),!e){if(void 0===e||!1===e)return e;throw new Error("`".concat(t,"` was specified but was not a node, or did not return a node"))}var a=e;if("string"==typeof e&&!(a=s.querySelector(e)))throw new Error("`".concat(t,"` as selector refers to no known node"));return a},h=function(){var t=d("initialFocus");if(!1===t)return!1;if(void 0===t||!li(t,r.tabbableOptions))if(l(s.activeElement)>=0)t=s.activeElement;else{var e=o.tabbableGroups[0];t=e&&e.firstTabbableNode||d("fallbackFocus")}if(!t)throw new Error("Your focus-trap needs to have at least one focusable element");return t},c=function(){if(o.containerGroups=o.containers.map((function(t){var e=function(t,e){var i;return i=(e=e||{}).getShadowRoot?je([t],e.includeContainer,{filter:si.bind(null,e),flatten:!1,getShadowRoot:e.getShadowRoot,shadowRootFilter:ni}):Ke(t,e.includeContainer,si.bind(null,e)),ri(i)}(t,r.tabbableOptions),i=function(t,e){return(e=e||{}).getShadowRoot?je([t],e.includeContainer,{filter:ii.bind(null,e),flatten:!0,getShadowRoot:e.getShadowRoot}):Ke(t,e.includeContainer,ii.bind(null,e))}(t,r.tabbableOptions),s=e.length>0?e[0]:void 0,n=e.length>0?e[e.length-1]:void 0,o=i.find((function(t){return oi(t)})),a=i.slice().reverse().find((function(t){return oi(t)})),l=!!e.find((function(t){return Ze(t)>0}));return{container:t,tabbableNodes:e,focusableNodes:i,posTabIndexesFound:l,firstTabbableNode:s,lastTabbableNode:n,firstDomTabbableNode:o,lastDomTabbableNode:a,nextTabbableNode:function(t){var s=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=e.indexOf(t);return n<0?s?i.slice(i.indexOf(t)+1).find((function(t){return oi(t)})):i.slice(0,i.indexOf(t)).reverse().find((function(t){return oi(t)})):e[n+(s?1:-1)]}}})),o.tabbableGroups=o.containerGroups.filter((function(t){return t.tabbableNodes.length>0})),o.tabbableGroups.length<=0&&!d("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");if(o.containerGroups.find((function(t){return t.posTabIndexesFound}))&&o.containerGroups.length>1)throw new Error("At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.")},u=function t(e){var i=e.activeElement;if(i)return i.shadowRoot&&null!==i.shadowRoot.activeElement?t(i.shadowRoot):i},p=function t(e){!1!==e&&e!==u(document)&&(e&&e.focus?(e.focus({preventScroll:!!r.preventScroll}),o.mostRecentlyFocusedNode=e,function(t){return t.tagName&&"input"===t.tagName.toLowerCase()&&"function"==typeof t.select}(e)&&e.select()):t(h()))},m=function(t){var e=d("setReturnFocus",t);return e||!1!==e&&t},f=function(t){var e=t.target,i=t.event,s=t.isBackward,n=void 0!==s&&s;e=e||bi(i),c();var a=null;if(o.tabbableGroups.length>0){var h=l(e,i),u=h>=0?o.containerGroups[h]:void 0;if(h<0)a=n?o.tabbableGroups[o.tabbableGroups.length-1].lastTabbableNode:o.tabbableGroups[0].firstTabbableNode;else if(n){var p=_i(o.tabbableGroups,(function(t){var i=t.firstTabbableNode;return e===i}));if(p<0&&(u.container===e||li(e,r.tabbableOptions)&&!oi(e,r.tabbableOptions)&&!u.nextTabbableNode(e,!1))&&(p=h),p>=0){var m=0===p?o.tabbableGroups.length-1:p-1,f=o.tabbableGroups[m];a=Ze(e)>=0?f.lastTabbableNode:f.lastDomTabbableNode}else mi(i)||(a=u.nextTabbableNode(e,!1))}else{var v=_i(o.tabbableGroups,(function(t){var i=t.lastTabbableNode;return e===i}));if(v<0&&(u.container===e||li(e,r.tabbableOptions)&&!oi(e,r.tabbableOptions)&&!u.nextTabbableNode(e))&&(v=h),v>=0){var g=v===o.tabbableGroups.length-1?0:v+1,_=o.tabbableGroups[g];a=Ze(e)>=0?_.firstTabbableNode:_.firstDomTabbableNode}else mi(i)||(a=u.nextTabbableNode(e))}}else a=d("fallbackFocus");return a},v=function(t){var e=bi(t);l(e,t)>=0||(yi(r.clickOutsideDeactivates,t)?i.deactivate({returnFocus:r.returnFocusOnDeactivate}):yi(r.allowOutsideClick,t)||t.preventDefault())},g=function(t){var e=bi(t),i=l(e,t)>=0;if(i||e instanceof Document)i&&(o.mostRecentlyFocusedNode=e);else{var s;t.stopImmediatePropagation();var n=!0;if(o.mostRecentlyFocusedNode)if(Ze(o.mostRecentlyFocusedNode)>0){var a=l(o.mostRecentlyFocusedNode),d=o.containerGroups[a].tabbableNodes;if(d.length>0){var c=d.findIndex((function(t){return t===o.mostRecentlyFocusedNode}));c>=0&&(r.isKeyForward(o.recentNavEvent)?c+1<d.length&&(s=d[c+1],n=!1):c-1>=0&&(s=d[c-1],n=!1))}}else o.containerGroups.some((function(t){return t.tabbableNodes.some((function(t){return Ze(t)>0}))}))||(n=!1);else n=!1;n&&(s=f({target:o.mostRecentlyFocusedNode,isBackward:r.isKeyBackward(o.recentNavEvent)})),p(s||(o.mostRecentlyFocusedNode||h()))}o.recentNavEvent=void 0},_=function(t){if(!(e=t,"Escape"!==(null==e?void 0:e.key)&&"Esc"!==(null==e?void 0:e.key)&&27!==(null==e?void 0:e.keyCode)||!1===yi(r.escapeDeactivates,t)))return t.preventDefault(),void i.deactivate();var e;(r.isKeyForward(t)||r.isKeyBackward(t))&&function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];o.recentNavEvent=t;var i=f({event:t,isBackward:e});i&&(mi(t)&&t.preventDefault(),p(i))}(t,r.isKeyBackward(t))},y=function(t){var e=bi(t);l(e,t)>=0||yi(r.clickOutsideDeactivates,t)||yi(r.allowOutsideClick,t)||(t.preventDefault(),t.stopImmediatePropagation())},b=function(){if(o.active)return ui(n,i),o.delayInitialFocusTimer=r.delayInitialFocus?gi((function(){p(h())})):p(h()),s.addEventListener("focusin",g,!0),s.addEventListener("mousedown",v,{capture:!0,passive:!1}),s.addEventListener("touchstart",v,{capture:!0,passive:!1}),s.addEventListener("click",y,{capture:!0,passive:!1}),s.addEventListener("keydown",_,{capture:!0,passive:!1}),i},E=function(){if(o.active)return s.removeEventListener("focusin",g,!0),s.removeEventListener("mousedown",v,!0),s.removeEventListener("touchstart",v,!0),s.removeEventListener("click",y,!0),s.removeEventListener("keydown",_,!0),i},w="undefined"!=typeof window&&"MutationObserver"in window?new MutationObserver((function(t){var e=t.some((function(t){return Array.from(t.removedNodes).some((function(t){return t===o.mostRecentlyFocusedNode}))}));e&&p(h())})):void 0,T=function(){w&&(w.disconnect(),o.active&&!o.paused&&o.containers.map((function(t){w.observe(t,{subtree:!0,childList:!0})})))};return(i={get active(){return o.active},get paused(){return o.paused},activate:function(t){if(o.active)return this;var e=a(t,"onActivate"),i=a(t,"onPostActivate"),n=a(t,"checkCanFocusTrap");n||c(),o.active=!0,o.paused=!1,o.nodeFocusedBeforeActivation=s.activeElement,null==e||e();var r=function(){n&&c(),b(),T(),null==i||i()};return n?(n(o.containers.concat()).then(r,r),this):(r(),this)},deactivate:function(t){if(!o.active)return this;var e=hi({onDeactivate:r.onDeactivate,onPostDeactivate:r.onPostDeactivate,checkCanReturnFocus:r.checkCanReturnFocus},t);clearTimeout(o.delayInitialFocusTimer),o.delayInitialFocusTimer=void 0,E(),o.active=!1,o.paused=!1,T(),pi(n,i);var s=a(e,"onDeactivate"),l=a(e,"onPostDeactivate"),d=a(e,"checkCanReturnFocus"),h=a(e,"returnFocus","returnFocusOnDeactivate");null==s||s();var c=function(){gi((function(){h&&p(m(o.nodeFocusedBeforeActivation)),null==l||l()}))};return h&&d?(d(m(o.nodeFocusedBeforeActivation)).then(c,c),this):(c(),this)},pause:function(t){if(o.paused||!o.active)return this;var e=a(t,"onPause"),i=a(t,"onPostPause");return o.paused=!0,null==e||e(),E(),T(),null==i||i(),this},unpause:function(t){if(!o.paused||!o.active)return this;var e=a(t,"onUnpause"),i=a(t,"onPostUnpause");return o.paused=!1,null==e||e(),c(),b(),T(),null==i||i(),this},updateContainerElements:function(t){var e=[].concat(t).filter(Boolean);return o.containers=e.map((function(t){return"string"==typeof t?s.querySelector(t):t})),o.active&&c(),T(),this}}).updateContainerElements(t),i};function Ti(t){return t}function Si(t,e){return"string"==typeof e&&(e=t.objects[e]),"GeometryCollection"===e.type?{type:"FeatureCollection",features:e.geometries.map((function(e){return Ai(t,e)}))}:Ai(t,e)}function Ai(t,e){var i=e.id,s=e.bbox,n=null==e.properties?{}:e.properties,r=function(t,e){var i=function(t){if(null==t)return Ti;var e,i,s=t.scale[0],n=t.scale[1],r=t.translate[0],o=t.translate[1];return function(t,a){a||(e=i=0);var l=2,d=t.length,h=new Array(d);for(h[0]=(e+=t[0])*s+r,h[1]=(i+=t[1])*n+o;l<d;)h[l]=t[l],++l;return h}}(t.transform),s=t.arcs;function n(t,e){e.length&&e.pop();for(var n=s[t<0?~t:t],r=0,o=n.length;r<o;++r)e.push(i(n[r],r));t<0&&function(t,e){for(var i,s=t.length,n=s-e;n<--s;)i=t[n],t[n++]=t[s],t[s]=i}(e,o)}function r(t){return i(t)}function o(t){for(var e=[],i=0,s=t.length;i<s;++i)n(t[i],e);return e.length<2&&e.push(e[0]),e}function a(t){for(var e=o(t);e.length<4;)e.push(e[0]);return e}function l(t){return t.map(a)}function d(t){var e,i=t.type;switch(i){case"GeometryCollection":return{type:i,geometries:t.geometries.map(d)};case"Point":e=r(t.coordinates);break;case"MultiPoint":e=t.coordinates.map(r);break;case"LineString":e=o(t.arcs);break;case"MultiLineString":e=t.arcs.map(o);break;case"Polygon":e=l(t.arcs);break;case"MultiPolygon":e=t.arcs.map(l);break;default:return null}return{type:i,coordinates:e}}return d(e)}(t,e);return null==i&&null==s?{type:"Feature",properties:n,geometry:r}:null==s?{type:"Feature",id:i,properties:n,geometry:r}:{type:"Feature",id:i,bbox:s,properties:n,geometry:r}}var Li=1e-6,Oi=1e-12,Ci=Math.PI,Ii=Ci/2,Pi=Ci/4,Ni=2*Ci,xi=180/Ci,Mi=Ci/180,ki=Math.abs,Di=Math.atan,Ri=Math.atan2,$i=Math.cos,Fi=(Math.ceil,Math.exp,Math.floor,Math.hypot,Math.log,Math.pow,Math.sin),Hi=Math.sign||function(t){return t>0?1:t<0?-1:0},Ui=Math.sqrt;Math.tan;function zi(t){return t>1?Ii:t<-1?-Ii:Math.asin(t)}function Bi(){}function Vi(){var t,e=[];return{point:function(e,i,s){t.push([e,i,s])},lineStart:function(){e.push(t=[])},lineEnd:Bi,rejoin:function(){e.length>1&&e.push(e.pop().concat(e.shift()))},result:function(){var i=e;return e=[],t=null,i}}}function qi(t,e){return ki(t[0]-e[0])<Li&&ki(t[1]-e[1])<Li}function Gi(t,e,i,s){this.x=t,this.z=e,this.o=i,this.e=s,this.v=!1,this.n=this.p=null}function Wi(t,e,i,s,n){var r,o,a=[],l=[];if(t.forEach((function(t){if(!((e=t.length-1)<=0)){var e,i,s=t[0],o=t[e];if(qi(s,o)){if(!s[2]&&!o[2]){for(n.lineStart(),r=0;r<e;++r)n.point((s=t[r])[0],s[1]);return void n.lineEnd()}o[0]+=2*Li}a.push(i=new Gi(s,t,null,!0)),l.push(i.o=new Gi(s,null,i,!1)),a.push(i=new Gi(o,t,null,!1)),l.push(i.o=new Gi(o,null,i,!0))}})),a.length){for(l.sort(e),Ki(a),Ki(l),r=0,o=l.length;r<o;++r)l[r].e=i=!i;for(var d,h,c=a[0];;){for(var u=c,p=!0;u.v;)if((u=u.n)===c)return;d=u.z,n.lineStart();do{if(u.v=u.o.v=!0,u.e){if(p)for(r=0,o=d.length;r<o;++r)n.point((h=d[r])[0],h[1]);else s(u.x,u.n.x,1,n);u=u.n}else{if(p)for(d=u.p.z,r=d.length-1;r>=0;--r)n.point((h=d[r])[0],h[1]);else s(u.x,u.p.x,-1,n);u=u.p}d=(u=u.o).z,p=!p}while(!u.v);n.lineEnd()}}}function Ki(t){if(e=t.length){for(var e,i,s=0,n=t[0];++s<e;)n.n=i=t[s],i.p=n,n=i;n.n=i=t[0],i.p=n}}class ji{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){const e=this._partials;let i=0;for(let s=0;s<this._n&&s<32;s++){const n=e[s],r=t+n,o=Math.abs(t)<Math.abs(n)?t-(r-n):n-(r-t);o&&(e[i++]=o),t=r}return e[i]=t,this._n=i+1,this}valueOf(){const t=this._partials;let e,i,s,n=this._n,r=0;if(n>0){for(r=t[--n];n>0&&(e=r,i=t[--n],r=e+i,s=i-(r-e),!s););n>0&&(s<0&&t[n-1]<0||s>0&&t[n-1]>0)&&(i=2*s,e=r+i,i==e-r&&(r=e))}return r}}function Yi(t){return[Ri(t[1],t[0]),zi(t[2])]}function Zi(t){var e=t[0],i=t[1],s=$i(i);return[s*$i(e),s*Fi(e),Fi(i)]}function Xi(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]}function Ji(t,e){return[t[1]*e[2]-t[2]*e[1],t[2]*e[0]-t[0]*e[2],t[0]*e[1]-t[1]*e[0]]}function Qi(t,e){t[0]+=e[0],t[1]+=e[1],t[2]+=e[2]}function ts(t,e){return[t[0]*e,t[1]*e,t[2]*e]}function es(t){var e=Ui(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=e,t[1]/=e,t[2]/=e}function is(t){return ki(t[0])<=Ci?t[0]:Hi(t[0])*((ki(t[0])+Ci)%Ni-Ci)}function ss(t){return Array.from(function*(t){for(const e of t)yield*e}(t))}function ns(t,e,i,s){return function(n){var r,o,a,l=e(n),d=Vi(),h=e(d),c=!1,u={point:p,lineStart:f,lineEnd:v,polygonStart:function(){u.point=g,u.lineStart=_,u.lineEnd=y,o=[],r=[]},polygonEnd:function(){u.point=p,u.lineStart=f,u.lineEnd=v,o=ss(o);var t=function(t,e){var i=is(e),s=e[1],n=Fi(s),r=[Fi(i),-$i(i),0],o=0,a=0,l=new ji;1===n?s=Ii+Li:-1===n&&(s=-Ii-Li);for(var d=0,h=t.length;d<h;++d)if(u=(c=t[d]).length)for(var c,u,p=c[u-1],m=is(p),f=p[1]/2+Pi,v=Fi(f),g=$i(f),_=0;_<u;++_,m=b,v=w,g=T,p=y){var y=c[_],b=is(y),E=y[1]/2+Pi,w=Fi(E),T=$i(E),S=b-m,A=S>=0?1:-1,L=A*S,O=L>Ci,C=v*w;if(l.add(Ri(C*A*Fi(L),g*T+C*$i(L))),o+=O?S+A*Ni:S,O^m>=i^b>=i){var I=Ji(Zi(p),Zi(y));es(I);var P=Ji(r,I);es(P);var N=(O^S>=0?-1:1)*zi(P[2]);(s>N||s===N&&(I[0]||I[1]))&&(a+=O^S>=0?1:-1)}}return(o<-Li||o<Li&&l<-Oi)^1&a}(r,s);o.length?(c||(n.polygonStart(),c=!0),Wi(o,os,t,i,n)):t&&(c||(n.polygonStart(),c=!0),n.lineStart(),i(null,null,1,n),n.lineEnd()),c&&(n.polygonEnd(),c=!1),o=r=null},sphere:function(){n.polygonStart(),n.lineStart(),i(null,null,1,n),n.lineEnd(),n.polygonEnd()}};function p(e,i){t(e,i)&&n.point(e,i)}function m(t,e){l.point(t,e)}function f(){u.point=m,l.lineStart()}function v(){u.point=p,l.lineEnd()}function g(t,e){a.push([t,e]),h.point(t,e)}function _(){h.lineStart(),a=[]}function y(){g(a[0][0],a[0][1]),h.lineEnd();var t,e,i,s,l=h.clean(),u=d.result(),p=u.length;if(a.pop(),r.push(a),a=null,p)if(1&l){if((e=(i=u[0]).length-1)>0){for(c||(n.polygonStart(),c=!0),n.lineStart(),t=0;t<e;++t)n.point((s=i[t])[0],s[1]);n.lineEnd()}}else p>1&&2&l&&u.push(u.pop().concat(u.shift())),o.push(u.filter(rs))}return u}}function rs(t){return t.length>1}function os(t,e){return((t=t.x)[0]<0?t[1]-Ii-Li:Ii-t[1])-((e=e.x)[0]<0?e[1]-Ii-Li:Ii-e[1])}var as=ns((function(){return!0}),(function(t){var e,i=NaN,s=NaN,n=NaN;return{lineStart:function(){t.lineStart(),e=1},point:function(r,o){var a=r>0?Ci:-Ci,l=ki(r-i);ki(l-Ci)<Li?(t.point(i,s=(s+o)/2>0?Ii:-Ii),t.point(n,s),t.lineEnd(),t.lineStart(),t.point(a,s),t.point(r,s),e=0):n!==a&&l>=Ci&&(ki(i-n)<Li&&(i-=n*Li),ki(r-a)<Li&&(r-=a*Li),s=function(t,e,i,s){var n,r,o=Fi(t-i);return ki(o)>Li?Di((Fi(e)*(r=$i(s))*Fi(i)-Fi(s)*(n=$i(e))*Fi(t))/(n*r*o)):(e+s)/2}(i,s,r,o),t.point(n,s),t.lineEnd(),t.lineStart(),t.point(a,s),e=0),t.point(i=r,s=o),n=a},lineEnd:function(){t.lineEnd(),i=s=NaN},clean:function(){return 2-e}}}),(function(t,e,i,s){var n;if(null==t)n=i*Ii,s.point(-Ci,n),s.point(0,n),s.point(Ci,n),s.point(Ci,0),s.point(Ci,-n),s.point(0,-n),s.point(-Ci,-n),s.point(-Ci,0),s.point(-Ci,n);else if(ki(t[0]-e[0])>Li){var r=t[0]<e[0]?Ci:-Ci;n=i*r/2,s.point(-r,n),s.point(0,n),s.point(r,n)}else s.point(e[0],e[1])}),[-Ci,-Ii]);function ls(t,e,i,s,n,r){if(i){var o=$i(e),a=Fi(e),l=s*i;null==n?(n=e+s*Ni,r=e-l/2):(n=ds(o,n),r=ds(o,r),(s>0?n<r:n>r)&&(n+=s*Ni));for(var d,h=n;s>0?h>r:h<r;h-=l)d=Yi([o,-a*$i(h),-a*Fi(h)]),t.point(d[0],d[1])}}function ds(t,e){(e=Zi(e))[0]-=t,es(e);var i=function(t){return t>1?0:t<-1?Ci:Math.acos(t)}(-e[1]);return((-e[2]<0?-i:i)+Ni-Li)%Ni}var hs=1e9,cs=-hs;function us(t,e,i,s){function n(n,r){return t<=n&&n<=i&&e<=r&&r<=s}function r(n,r,a,d){var h=0,c=0;if(null==n||(h=o(n,a))!==(c=o(r,a))||l(n,r)<0^a>0)do{d.point(0===h||3===h?t:i,h>1?s:e)}while((h=(h+a+4)%4)!==c);else d.point(r[0],r[1])}function o(s,n){return ki(s[0]-t)<Li?n>0?0:3:ki(s[0]-i)<Li?n>0?2:1:ki(s[1]-e)<Li?n>0?1:0:n>0?3:2}function a(t,e){return l(t.x,e.x)}function l(t,e){var i=o(t,1),s=o(e,1);return i!==s?i-s:0===i?e[1]-t[1]:1===i?t[0]-e[0]:2===i?t[1]-e[1]:e[0]-t[0]}return function(o){var l,d,h,c,u,p,m,f,v,g,_,y=o,b=Vi(),E={point:w,lineStart:function(){E.point=T,d&&d.push(h=[]);g=!0,v=!1,m=f=NaN},lineEnd:function(){l&&(T(c,u),p&&v&&b.rejoin(),l.push(b.result()));E.point=w,v&&y.lineEnd()},polygonStart:function(){y=b,l=[],d=[],_=!0},polygonEnd:function(){var e=function(){for(var e=0,i=0,n=d.length;i<n;++i)for(var r,o,a=d[i],l=1,h=a.length,c=a[0],u=c[0],p=c[1];l<h;++l)r=u,o=p,u=(c=a[l])[0],p=c[1],o<=s?p>s&&(u-r)*(s-o)>(p-o)*(t-r)&&++e:p<=s&&(u-r)*(s-o)<(p-o)*(t-r)&&--e;return e}(),i=_&&e,n=(l=ss(l)).length;(i||n)&&(o.polygonStart(),i&&(o.lineStart(),r(null,null,1,o),o.lineEnd()),n&&Wi(l,a,e,r,o),o.polygonEnd());y=o,l=d=h=null}};function w(t,e){n(t,e)&&y.point(t,e)}function T(r,o){var a=n(r,o);if(d&&h.push([r,o]),g)c=r,u=o,p=a,g=!1,a&&(y.lineStart(),y.point(r,o));else if(a&&v)y.point(r,o);else{var l=[m=Math.max(cs,Math.min(hs,m)),f=Math.max(cs,Math.min(hs,f))],b=[r=Math.max(cs,Math.min(hs,r)),o=Math.max(cs,Math.min(hs,o))];!function(t,e,i,s,n,r){var o,a=t[0],l=t[1],d=0,h=1,c=e[0]-a,u=e[1]-l;if(o=i-a,c||!(o>0)){if(o/=c,c<0){if(o<d)return;o<h&&(h=o)}else if(c>0){if(o>h)return;o>d&&(d=o)}if(o=n-a,c||!(o<0)){if(o/=c,c<0){if(o>h)return;o>d&&(d=o)}else if(c>0){if(o<d)return;o<h&&(h=o)}if(o=s-l,u||!(o>0)){if(o/=u,u<0){if(o<d)return;o<h&&(h=o)}else if(u>0){if(o>h)return;o>d&&(d=o)}if(o=r-l,u||!(o<0)){if(o/=u,u<0){if(o>h)return;o>d&&(d=o)}else if(u>0){if(o<d)return;o<h&&(h=o)}return d>0&&(t[0]=a+d*c,t[1]=l+d*u),h<1&&(e[0]=a+h*c,e[1]=l+h*u),!0}}}}}(l,b,t,e,i,s)?a&&(y.lineStart(),y.point(r,o),_=!1):(v||(y.lineStart(),y.point(l[0],l[1])),y.point(b[0],b[1]),a||y.lineEnd(),_=!1)}m=r,f=o,v=a}return E}}function ps(t,e){function i(i,s){return i=t(i,s),e(i[0],i[1])}return t.invert&&e.invert&&(i.invert=function(i,s){return(i=e.invert(i,s))&&t.invert(i[0],i[1])}),i}var ms=t=>t;function fs(t,e){return ki(t)>Ci&&(t-=Math.round(t/Ni)*Ni),[t,e]}function vs(t,e,i){return(t%=Ni)?e||i?ps(_s(t),ys(e,i)):_s(t):e||i?ys(e,i):fs}function gs(t){return function(e,i){return ki(e+=t)>Ci&&(e-=Math.round(e/Ni)*Ni),[e,i]}}function _s(t){var e=gs(t);return e.invert=gs(-t),e}function ys(t,e){var i=$i(t),s=Fi(t),n=$i(e),r=Fi(e);function o(t,e){var o=$i(e),a=$i(t)*o,l=Fi(t)*o,d=Fi(e),h=d*i+a*s;return[Ri(l*n-h*r,a*i-d*s),zi(h*n+l*r)]}return o.invert=function(t,e){var o=$i(e),a=$i(t)*o,l=Fi(t)*o,d=Fi(e),h=d*n-l*r;return[Ri(l*n+d*r,a*i+h*s),zi(h*i-a*s)]},o}function bs(t){return function(e){var i=new Es;for(var s in t)i[s]=t[s];return i.stream=e,i}}function Es(){}function ws(t,e){t&&Ss.hasOwnProperty(t.type)&&Ss[t.type](t,e)}fs.invert=fs,Es.prototype={constructor:Es,point:function(t,e){this.stream.point(t,e)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};var Ts={Feature:function(t,e){ws(t.geometry,e)},FeatureCollection:function(t,e){for(var i=t.features,s=-1,n=i.length;++s<n;)ws(i[s].geometry,e)}},Ss={Sphere:function(t,e){e.sphere()},Point:function(t,e){t=t.coordinates,e.point(t[0],t[1],t[2])},MultiPoint:function(t,e){for(var i=t.coordinates,s=-1,n=i.length;++s<n;)t=i[s],e.point(t[0],t[1],t[2])},LineString:function(t,e){As(t.coordinates,e,0)},MultiLineString:function(t,e){for(var i=t.coordinates,s=-1,n=i.length;++s<n;)As(i[s],e,0)},Polygon:function(t,e){Ls(t.coordinates,e)},MultiPolygon:function(t,e){for(var i=t.coordinates,s=-1,n=i.length;++s<n;)Ls(i[s],e)},GeometryCollection:function(t,e){for(var i=t.geometries,s=-1,n=i.length;++s<n;)ws(i[s],e)}};function As(t,e,i){var s,n=-1,r=t.length-i;for(e.lineStart();++n<r;)s=t[n],e.point(s[0],s[1],s[2]);e.lineEnd()}function Ls(t,e){var i=-1,s=t.length;for(e.polygonStart();++i<s;)As(t[i],e,1);e.polygonEnd()}function Os(t,e){t&&Ts.hasOwnProperty(t.type)?Ts[t.type](t,e):ws(t,e)}var Cs=1/0,Is=Cs,Ps=-Cs,Ns=Ps,xs={point:function(t,e){t<Cs&&(Cs=t);t>Ps&&(Ps=t);e<Is&&(Is=e);e>Ns&&(Ns=e)},lineStart:Bi,lineEnd:Bi,polygonStart:Bi,polygonEnd:Bi,result:function(){var t=[[Cs,Is],[Ps,Ns]];return Ps=Ns=-(Is=Cs=1/0),t}};var Ms=xs;function ks(t,e,i){var s=t.clipExtent&&t.clipExtent();return t.scale(150).translate([0,0]),null!=s&&t.clipExtent(null),Os(i,t.stream(Ms)),e(Ms.result()),null!=s&&t.clipExtent(s),t}function Ds(t,e,i){return ks(t,(function(i){var s=e[1][0]-e[0][0],n=e[1][1]-e[0][1],r=Math.min(s/(i[1][0]-i[0][0]),n/(i[1][1]-i[0][1])),o=+e[0][0]+(s-r*(i[1][0]+i[0][0]))/2,a=+e[0][1]+(n-r*(i[1][1]+i[0][1]))/2;t.scale(150*r).translate([o,a])}),i)}function Rs(t,e,i){return Ds(t,[[0,0],e],i)}function $s(t,e,i){return ks(t,(function(i){var s=+e,n=s/(i[1][0]-i[0][0]),r=(s-n*(i[1][0]+i[0][0]))/2,o=-n*i[0][1];t.scale(150*n).translate([r,o])}),i)}function Fs(t,e,i){return ks(t,(function(i){var s=+e,n=s/(i[1][1]-i[0][1]),r=-n*i[0][0],o=(s-n*(i[1][1]+i[0][1]))/2;t.scale(150*n).translate([r,o])}),i)}var Hs=16,Us=$i(30*Mi);function zs(t,e){return+e?function(t,e){function i(s,n,r,o,a,l,d,h,c,u,p,m,f,v){var g=d-s,_=h-n,y=g*g+_*_;if(y>4*e&&f--){var b=o+u,E=a+p,w=l+m,T=Ui(b*b+E*E+w*w),S=zi(w/=T),A=ki(ki(w)-1)<Li||ki(r-c)<Li?(r+c)/2:Ri(E,b),L=t(A,S),O=L[0],C=L[1],I=O-s,P=C-n,N=_*I-g*P;(N*N/y>e||ki((g*I+_*P)/y-.5)>.3||o*u+a*p+l*m<Us)&&(i(s,n,r,o,a,l,O,C,A,b/=T,E/=T,w,f,v),v.point(O,C),i(O,C,A,b,E,w,d,h,c,u,p,m,f,v))}}return function(e){var s,n,r,o,a,l,d,h,c,u,p,m,f={point:v,lineStart:g,lineEnd:y,polygonStart:function(){e.polygonStart(),f.lineStart=b},polygonEnd:function(){e.polygonEnd(),f.lineStart=g}};function v(i,s){i=t(i,s),e.point(i[0],i[1])}function g(){h=NaN,f.point=_,e.lineStart()}function _(s,n){var r=Zi([s,n]),o=t(s,n);i(h,c,d,u,p,m,h=o[0],c=o[1],d=s,u=r[0],p=r[1],m=r[2],Hs,e),e.point(h,c)}function y(){f.point=v,e.lineEnd()}function b(){g(),f.point=E,f.lineEnd=w}function E(t,e){_(s=t,e),n=h,r=c,o=u,a=p,l=m,f.point=_}function w(){i(h,c,d,u,p,m,n,r,s,o,a,l,Hs,e),f.lineEnd=y,y()}return f}}(t,e):function(t){return bs({point:function(e,i){e=t(e,i),this.stream.point(e[0],e[1])}})}(t)}var Bs=bs({point:function(t,e){this.stream.point(t*Mi,e*Mi)}});function Vs(t,e,i,s,n,r){if(!r)return function(t,e,i,s,n){function r(r,o){return[e+t*(r*=s),i-t*(o*=n)]}return r.invert=function(r,o){return[(r-e)/t*s,(i-o)/t*n]},r}(t,e,i,s,n);var o=$i(r),a=Fi(r),l=o*t,d=a*t,h=o/t,c=a/t,u=(a*i-o*e)/t,p=(a*e+o*i)/t;function m(t,r){return[l*(t*=s)-d*(r*=n)+e,i-d*t-l*r]}return m.invert=function(t,e){return[s*(h*t-c*e+u),n*(p-c*t-h*e)]},m}function qs(t){var e,i,s,n,r,o,a,l,d,h,c=150,u=480,p=250,m=0,f=0,v=0,g=0,_=0,y=0,b=1,E=1,w=null,T=as,S=null,A=ms,L=.5;function O(t){return l(t[0]*Mi,t[1]*Mi)}function C(t){return(t=l.invert(t[0],t[1]))&&[t[0]*xi,t[1]*xi]}function I(){var t=Vs(c,0,0,b,E,y).apply(null,e(m,f)),s=Vs(c,u-t[0],p-t[1],b,E,y);return i=vs(v,g,_),a=ps(e,s),l=ps(i,a),o=zs(a,L),P()}function P(){return d=h=null,O}return O.stream=function(t){return d&&h===t?d:d=Bs(function(t){return bs({point:function(e,i){var s=t(e,i);return this.stream.point(s[0],s[1])}})}(i)(T(o(A(h=t)))))},O.preclip=function(t){return arguments.length?(T=t,w=void 0,P()):T},O.postclip=function(t){return arguments.length?(A=t,S=s=n=r=null,P()):A},O.clipAngle=function(t){return arguments.length?(T=+t?function(t){var e=$i(t),i=2*Mi,s=e>0,n=ki(e)>Li;function r(t,i){return $i(t)*$i(i)>e}function o(t,i,s){var n=[1,0,0],r=Ji(Zi(t),Zi(i)),o=Xi(r,r),a=r[0],l=o-a*a;if(!l)return!s&&t;var d=e*o/l,h=-e*a/l,c=Ji(n,r),u=ts(n,d);Qi(u,ts(r,h));var p=c,m=Xi(u,p),f=Xi(p,p),v=m*m-f*(Xi(u,u)-1);if(!(v<0)){var g=Ui(v),_=ts(p,(-m-g)/f);if(Qi(_,u),_=Yi(_),!s)return _;var y,b=t[0],E=i[0],w=t[1],T=i[1];E<b&&(y=b,b=E,E=y);var S=E-b,A=ki(S-Ci)<Li;if(!A&&T<w&&(y=w,w=T,T=y),A||S<Li?A?w+T>0^_[1]<(ki(_[0]-b)<Li?w:T):w<=_[1]&&_[1]<=T:S>Ci^(b<=_[0]&&_[0]<=E)){var L=ts(p,(-m+g)/f);return Qi(L,u),[_,Yi(L)]}}}function a(e,i){var n=s?t:Ci-t,r=0;return e<-n?r|=1:e>n&&(r|=2),i<-n?r|=4:i>n&&(r|=8),r}return ns(r,(function(t){var e,i,l,d,h;return{lineStart:function(){d=l=!1,h=1},point:function(c,u){var p,m=[c,u],f=r(c,u),v=s?f?0:a(c,u):f?a(c+(c<0?Ci:-Ci),u):0;if(!e&&(d=l=f)&&t.lineStart(),f!==l&&(!(p=o(e,m))||qi(e,p)||qi(m,p))&&(m[2]=1),f!==l)h=0,f?(t.lineStart(),p=o(m,e),t.point(p[0],p[1])):(p=o(e,m),t.point(p[0],p[1],2),t.lineEnd()),e=p;else if(n&&e&&s^f){var g;v&i||!(g=o(m,e,!0))||(h=0,s?(t.lineStart(),t.point(g[0][0],g[0][1]),t.point(g[1][0],g[1][1]),t.lineEnd()):(t.point(g[1][0],g[1][1]),t.lineEnd(),t.lineStart(),t.point(g[0][0],g[0][1],3)))}!f||e&&qi(e,m)||t.point(m[0],m[1]),e=m,l=f,i=v},lineEnd:function(){l&&t.lineEnd(),e=null},clean:function(){return h|(d&&l)<<1}}}),(function(e,s,n,r){ls(r,t,i,n,e,s)}),s?[0,-t]:[-Ci,t-Ci])}(w=t*Mi):(w=null,as),P()):w*xi},O.clipExtent=function(t){return arguments.length?(A=null==t?(S=s=n=r=null,ms):us(S=+t[0][0],s=+t[0][1],n=+t[1][0],r=+t[1][1]),P()):null==S?null:[[S,s],[n,r]]},O.scale=function(t){return arguments.length?(c=+t,I()):c},O.translate=function(t){return arguments.length?(u=+t[0],p=+t[1],I()):[u,p]},O.center=function(t){return arguments.length?(m=t[0]%360*Mi,f=t[1]%360*Mi,I()):[m*xi,f*xi]},O.rotate=function(t){return arguments.length?(v=t[0]%360*Mi,g=t[1]%360*Mi,_=t.length>2?t[2]%360*Mi:0,I()):[v*xi,g*xi,_*xi]},O.angle=function(t){return arguments.length?(y=t%360*Mi,I()):y*xi},O.reflectX=function(t){return arguments.length?(b=t?-1:1,I()):b<0},O.reflectY=function(t){return arguments.length?(E=t?-1:1,I()):E<0},O.precision=function(t){return arguments.length?(o=zs(a,L=t*t),P()):Ui(L)},O.fitExtent=function(t,e){return Ds(O,t,e)},O.fitSize=function(t,e){return Rs(O,t,e)},O.fitWidth=function(t,e){return $s(O,t,e)},O.fitHeight=function(t,e){return Fs(O,t,e)},function(){return e=t.apply(this,arguments),O.invert=e.invert&&C,I()}}function Gs(t,e){var i=Fi(t),s=(i+Fi(e))/2;if(ki(s)<Li)return function(t){var e=$i(t);function i(t,i){return[t*e,Fi(i)/e]}return i.invert=function(t,i){return[t/e,zi(i*e)]},i}(t);var n=1+i*(2*s-i),r=Ui(n)/s;function o(t,e){var i=Ui(n-2*s*Fi(e))/s;return[i*Fi(t*=s),r-i*$i(t)]}return o.invert=function(t,e){var i=r-e,o=Ri(t,ki(i))*Hi(i);return i*s<0&&(o-=Ci*Hi(t)*Hi(i)),[o/s,zi((n-(t*t+i*i)*s*s)/(2*s))]},o}function Ws(){return function(t){var e=0,i=Ci/3,s=qs(t),n=s(e,i);return n.parallels=function(t){return arguments.length?s(e=t[0]*Mi,i=t[1]*Mi):[e*xi,i*xi]},n}(Gs).scale(155.424).center([0,33.6442])}var Ks,js,Ys,Zs,Xs=new ji,Js=new ji,Qs={point:Bi,lineStart:Bi,lineEnd:Bi,polygonStart:function(){Qs.lineStart=tn,Qs.lineEnd=nn},polygonEnd:function(){Qs.lineStart=Qs.lineEnd=Qs.point=Bi,Xs.add(ki(Js)),Js=new ji},result:function(){var t=Xs/2;return Xs=new ji,t}};function tn(){Qs.point=en}function en(t,e){Qs.point=sn,Ks=Ys=t,js=Zs=e}function sn(t,e){Js.add(Zs*t-Ys*e),Ys=t,Zs=e}function nn(){sn(Ks,js)}var rn,on,an,ln,dn=Qs,hn=0,cn=0,un=0,pn=0,mn=0,fn=0,vn=0,gn=0,_n=0,yn={point:bn,lineStart:En,lineEnd:Sn,polygonStart:function(){yn.lineStart=An,yn.lineEnd=Ln},polygonEnd:function(){yn.point=bn,yn.lineStart=En,yn.lineEnd=Sn},result:function(){var t=_n?[vn/_n,gn/_n]:fn?[pn/fn,mn/fn]:un?[hn/un,cn/un]:[NaN,NaN];return hn=cn=un=pn=mn=fn=vn=gn=_n=0,t}};function bn(t,e){hn+=t,cn+=e,++un}function En(){yn.point=wn}function wn(t,e){yn.point=Tn,bn(an=t,ln=e)}function Tn(t,e){var i=t-an,s=e-ln,n=Ui(i*i+s*s);pn+=n*(an+t)/2,mn+=n*(ln+e)/2,fn+=n,bn(an=t,ln=e)}function Sn(){yn.point=bn}function An(){yn.point=On}function Ln(){Cn(rn,on)}function On(t,e){yn.point=Cn,bn(rn=an=t,on=ln=e)}function Cn(t,e){var i=t-an,s=e-ln,n=Ui(i*i+s*s);pn+=n*(an+t)/2,mn+=n*(ln+e)/2,fn+=n,vn+=(n=ln*t-an*e)*(an+t),gn+=n*(ln+e),_n+=3*n,bn(an=t,ln=e)}var In=yn;function Pn(t){this._context=t}Pn.prototype={_radius:4.5,pointRadius:function(t){return this._radius=t,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._context.closePath(),this._point=NaN},point:function(t,e){switch(this._point){case 0:this._context.moveTo(t,e),this._point=1;break;case 1:this._context.lineTo(t,e);break;default:this._context.moveTo(t+this._radius,e),this._context.arc(t,e,this._radius,0,Ni)}},result:Bi};var Nn,xn,Mn,kn,Dn,Rn=new ji,$n={point:Bi,lineStart:function(){$n.point=Fn},lineEnd:function(){Nn&&Hn(xn,Mn),$n.point=Bi},polygonStart:function(){Nn=!0},polygonEnd:function(){Nn=null},result:function(){var t=+Rn;return Rn=new ji,t}};function Fn(t,e){$n.point=Hn,xn=kn=t,Mn=Dn=e}function Hn(t,e){kn-=t,Dn-=e,Rn.add(Ui(kn*kn+Dn*Dn)),kn=t,Dn=e}var Un=$n;let zn,Bn,Vn,qn;class Gn{constructor(t){this._append=null==t?Wn:function(t){const e=Math.floor(t);if(!(e>=0))throw new RangeError(`invalid digits: ${t}`);if(e>15)return Wn;if(e!==zn){const t=10**e;zn=e,Bn=function(e){let i=1;this._+=e[0];for(const s=e.length;i<s;++i)this._+=Math.round(arguments[i]*t)/t+e[i]}}return Bn}(t),this._radius=4.5,this._=""}pointRadius(t){return this._radius=+t,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){0===this._line&&(this._+="Z"),this._point=NaN}point(t,e){switch(this._point){case 0:this._append`M${t},${e}`,this._point=1;break;case 1:this._append`L${t},${e}`;break;default:if(this._append`M${t},${e}`,this._radius!==Vn||this._append!==Bn){const t=this._radius,e=this._;this._="",this._append`m0,${t}a${t},${t} 0 1,1 0,${-2*t}a${t},${t} 0 1,1 0,${2*t}z`,Vn=t,Bn=this._append,qn=this._,this._=e}this._+=qn}}result(){const t=this._;return this._="",t.length?t:null}}function Wn(t){let e=1;this._+=t[0];for(const i=t.length;e<i;++e)this._+=arguments[e]+t[e]}function Kn(){}function jn(t){return null==t?Kn:function(){return this.querySelector(t)}}function Yn(t){return null==t?[]:Array.isArray(t)?t:Array.from(t)}function Zn(){return[]}function Xn(t){return function(e){return e.matches(t)}}var Jn=Array.prototype.find;function Qn(){return this.firstElementChild}var tr=Array.prototype.filter;function er(){return Array.from(this.children)}function ir(t){return new Array(t.length)}function sr(t,e){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=e}function nr(t,e,i,s,n,r){for(var o,a=0,l=e.length,d=r.length;a<d;++a)(o=e[a])?(o.__data__=r[a],s[a]=o):i[a]=new sr(t,r[a]);for(;a<l;++a)(o=e[a])&&(n[a]=o)}function rr(t,e,i,s,n,r,o){var a,l,d,h=new Map,c=e.length,u=r.length,p=new Array(c);for(a=0;a<c;++a)(l=e[a])&&(p[a]=d=o.call(l,l.__data__,a,e)+"",h.has(d)?n[a]=l:h.set(d,l));for(a=0;a<u;++a)d=o.call(t,r[a],a,r)+"",(l=h.get(d))?(s[a]=l,l.__data__=r[a],h.delete(d)):i[a]=new sr(t,r[a]);for(a=0;a<c;++a)(l=e[a])&&h.get(p[a])===l&&(n[a]=l)}function or(t){return t.__data__}function ar(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function lr(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}sr.prototype={constructor:sr,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,e){return this._parent.insertBefore(t,e)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var dr="http://www.w3.org/1999/xhtml",hr={svg:"http://www.w3.org/2000/svg",xhtml:dr,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function cr(t){var e=t+="",i=e.indexOf(":");return i>=0&&"xmlns"!==(e=t.slice(0,i))&&(t=t.slice(i+1)),hr.hasOwnProperty(e)?{space:hr[e],local:t}:t}function ur(t){return function(){this.removeAttribute(t)}}function pr(t){return function(){this.removeAttributeNS(t.space,t.local)}}function mr(t,e){return function(){this.setAttribute(t,e)}}function fr(t,e){return function(){this.setAttributeNS(t.space,t.local,e)}}function vr(t,e){return function(){var i=e.apply(this,arguments);null==i?this.removeAttribute(t):this.setAttribute(t,i)}}function gr(t,e){return function(){var i=e.apply(this,arguments);null==i?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,i)}}function _r(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function yr(t){return function(){this.style.removeProperty(t)}}function br(t,e,i){return function(){this.style.setProperty(t,e,i)}}function Er(t,e,i){return function(){var s=e.apply(this,arguments);null==s?this.style.removeProperty(t):this.style.setProperty(t,s,i)}}function wr(t){return function(){delete this[t]}}function Tr(t,e){return function(){this[t]=e}}function Sr(t,e){return function(){var i=e.apply(this,arguments);null==i?delete this[t]:this[t]=i}}function Ar(t){return t.trim().split(/^|\s+/)}function Lr(t){return t.classList||new Or(t)}function Or(t){this._node=t,this._names=Ar(t.getAttribute("class")||"")}function Cr(t,e){for(var i=Lr(t),s=-1,n=e.length;++s<n;)i.add(e[s])}function Ir(t,e){for(var i=Lr(t),s=-1,n=e.length;++s<n;)i.remove(e[s])}function Pr(t){return function(){Cr(this,t)}}function Nr(t){return function(){Ir(this,t)}}function xr(t,e){return function(){(e.apply(this,arguments)?Cr:Ir)(this,t)}}function Mr(){this.textContent=""}function kr(t){return function(){this.textContent=t}}function Dr(t){return function(){var e=t.apply(this,arguments);this.textContent=null==e?"":e}}function Rr(){this.innerHTML=""}function $r(t){return function(){this.innerHTML=t}}function Fr(t){return function(){var e=t.apply(this,arguments);this.innerHTML=null==e?"":e}}function Hr(){this.nextSibling&&this.parentNode.appendChild(this)}function Ur(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function zr(t){return function(){var e=this.ownerDocument,i=this.namespaceURI;return i===dr&&e.documentElement.namespaceURI===dr?e.createElement(t):e.createElementNS(i,t)}}function Br(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}function Vr(t){var e=cr(t);return(e.local?Br:zr)(e)}function qr(){return null}function Gr(){var t=this.parentNode;t&&t.removeChild(this)}function Wr(){var t=this.cloneNode(!1),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function Kr(){var t=this.cloneNode(!0),e=this.parentNode;return e?e.insertBefore(t,this.nextSibling):t}function jr(t){return function(){var e=this.__on;if(e){for(var i,s=0,n=-1,r=e.length;s<r;++s)i=e[s],t.type&&i.type!==t.type||i.name!==t.name?e[++n]=i:this.removeEventListener(i.type,i.listener,i.options);++n?e.length=n:delete this.__on}}}function Yr(t,e,i){return function(){var s,n=this.__on,r=function(t){return function(e){t.call(this,e,this.__data__)}}(e);if(n)for(var o=0,a=n.length;o<a;++o)if((s=n[o]).type===t.type&&s.name===t.name)return this.removeEventListener(s.type,s.listener,s.options),this.addEventListener(s.type,s.listener=r,s.options=i),void(s.value=e);this.addEventListener(t.type,r,i),s={type:t.type,name:t.name,value:e,listener:r,options:i},n?n.push(s):this.__on=[s]}}function Zr(t,e,i){var s=_r(t),n=s.CustomEvent;"function"==typeof n?n=new n(e,i):(n=s.document.createEvent("Event"),i?(n.initEvent(e,i.bubbles,i.cancelable),n.detail=i.detail):n.initEvent(e,!1,!1)),t.dispatchEvent(n)}function Xr(t,e){return function(){return Zr(this,t,e)}}function Jr(t,e){return function(){return Zr(this,t,e.apply(this,arguments))}}Or.prototype={add:function(t){this._names.indexOf(t)<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var e=this._names.indexOf(t);e>=0&&(this._names.splice(e,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var Qr=[null];function to(t,e){this._groups=t,this._parents=e}function eo(){return new to([[document.documentElement]],Qr)}to.prototype=eo.prototype={constructor:to,select:function(t){"function"!=typeof t&&(t=jn(t));for(var e=this._groups,i=e.length,s=new Array(i),n=0;n<i;++n)for(var r,o,a=e[n],l=a.length,d=s[n]=new Array(l),h=0;h<l;++h)(r=a[h])&&(o=t.call(r,r.__data__,h,a))&&("__data__"in r&&(o.__data__=r.__data__),d[h]=o);return new to(s,this._parents)},selectAll:function(t){t="function"==typeof t?function(t){return function(){return Yn(t.apply(this,arguments))}}(t):function(t){return null==t?Zn:function(){return this.querySelectorAll(t)}}(t);for(var e=this._groups,i=e.length,s=[],n=[],r=0;r<i;++r)for(var o,a=e[r],l=a.length,d=0;d<l;++d)(o=a[d])&&(s.push(t.call(o,o.__data__,d,a)),n.push(o));return new to(s,n)},selectChild:function(t){return this.select(null==t?Qn:function(t){return function(){return Jn.call(this.children,t)}}("function"==typeof t?t:Xn(t)))},selectChildren:function(t){return this.selectAll(null==t?er:function(t){return function(){return tr.call(this.children,t)}}("function"==typeof t?t:Xn(t)))},filter:function(t){"function"!=typeof t&&(t=function(t){return function(){return this.matches(t)}}(t));for(var e=this._groups,i=e.length,s=new Array(i),n=0;n<i;++n)for(var r,o=e[n],a=o.length,l=s[n]=[],d=0;d<a;++d)(r=o[d])&&t.call(r,r.__data__,d,o)&&l.push(r);return new to(s,this._parents)},data:function(t,e){if(!arguments.length)return Array.from(this,or);var i=e?rr:nr,s=this._parents,n=this._groups;"function"!=typeof t&&(t=function(t){return function(){return t}}(t));for(var r=n.length,o=new Array(r),a=new Array(r),l=new Array(r),d=0;d<r;++d){var h=s[d],c=n[d],u=c.length,p=ar(t.call(h,h&&h.__data__,d,s)),m=p.length,f=a[d]=new Array(m),v=o[d]=new Array(m);i(h,c,f,v,l[d]=new Array(u),p,e);for(var g,_,y=0,b=0;y<m;++y)if(g=f[y]){for(y>=b&&(b=y+1);!(_=v[b])&&++b<m;);g._next=_||null}}return(o=new to(o,s))._enter=a,o._exit=l,o},enter:function(){return new to(this._enter||this._groups.map(ir),this._parents)},exit:function(){return new to(this._exit||this._groups.map(ir),this._parents)},join:function(t,e,i){var s=this.enter(),n=this,r=this.exit();return"function"==typeof t?(s=t(s))&&(s=s.selection()):s=s.append(t+""),null!=e&&(n=e(n))&&(n=n.selection()),null==i?r.remove():i(r),s&&n?s.merge(n).order():n},merge:function(t){for(var e=t.selection?t.selection():t,i=this._groups,s=e._groups,n=i.length,r=s.length,o=Math.min(n,r),a=new Array(n),l=0;l<o;++l)for(var d,h=i[l],c=s[l],u=h.length,p=a[l]=new Array(u),m=0;m<u;++m)(d=h[m]||c[m])&&(p[m]=d);for(;l<n;++l)a[l]=i[l];return new to(a,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,e=-1,i=t.length;++e<i;)for(var s,n=t[e],r=n.length-1,o=n[r];--r>=0;)(s=n[r])&&(o&&4^s.compareDocumentPosition(o)&&o.parentNode.insertBefore(s,o),o=s);return this},sort:function(t){function e(e,i){return e&&i?t(e.__data__,i.__data__):!e-!i}t||(t=lr);for(var i=this._groups,s=i.length,n=new Array(s),r=0;r<s;++r){for(var o,a=i[r],l=a.length,d=n[r]=new Array(l),h=0;h<l;++h)(o=a[h])&&(d[h]=o);d.sort(e)}return new to(n,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,e=0,i=t.length;e<i;++e)for(var s=t[e],n=0,r=s.length;n<r;++n){var o=s[n];if(o)return o}return null},size:function(){let t=0;for(const e of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var e=this._groups,i=0,s=e.length;i<s;++i)for(var n,r=e[i],o=0,a=r.length;o<a;++o)(n=r[o])&&t.call(n,n.__data__,o,r);return this},attr:function(t,e){var i=cr(t);if(arguments.length<2){var s=this.node();return i.local?s.getAttributeNS(i.space,i.local):s.getAttribute(i)}return this.each((null==e?i.local?pr:ur:"function"==typeof e?i.local?gr:vr:i.local?fr:mr)(i,e))},style:function(t,e,i){return arguments.length>1?this.each((null==e?yr:"function"==typeof e?Er:br)(t,e,null==i?"":i)):function(t,e){return t.style.getPropertyValue(e)||_r(t).getComputedStyle(t,null).getPropertyValue(e)}(this.node(),t)},property:function(t,e){return arguments.length>1?this.each((null==e?wr:"function"==typeof e?Sr:Tr)(t,e)):this.node()[t]},classed:function(t,e){var i=Ar(t+"");if(arguments.length<2){for(var s=Lr(this.node()),n=-1,r=i.length;++n<r;)if(!s.contains(i[n]))return!1;return!0}return this.each(("function"==typeof e?xr:e?Pr:Nr)(i,e))},text:function(t){return arguments.length?this.each(null==t?Mr:("function"==typeof t?Dr:kr)(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?Rr:("function"==typeof t?Fr:$r)(t)):this.node().innerHTML},raise:function(){return this.each(Hr)},lower:function(){return this.each(Ur)},append:function(t){var e="function"==typeof t?t:Vr(t);return this.select((function(){return this.appendChild(e.apply(this,arguments))}))},insert:function(t,e){var i="function"==typeof t?t:Vr(t),s=null==e?qr:"function"==typeof e?e:jn(e);return this.select((function(){return this.insertBefore(i.apply(this,arguments),s.apply(this,arguments)||null)}))},remove:function(){return this.each(Gr)},clone:function(t){return this.select(t?Kr:Wr)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,e,i){var s,n,r=function(t){return t.trim().split(/^|\s+/).map((function(t){var e="",i=t.indexOf(".");return i>=0&&(e=t.slice(i+1),t=t.slice(0,i)),{type:t,name:e}}))}(t+""),o=r.length;if(!(arguments.length<2)){for(a=e?Yr:jr,s=0;s<o;++s)this.each(a(r[s],e,i));return this}var a=this.node().__on;if(a)for(var l,d=0,h=a.length;d<h;++d)for(s=0,l=a[d];s<o;++s)if((n=r[s]).type===l.type&&n.name===l.name)return l.value},dispatch:function(t,e){return this.each(("function"==typeof e?Jr:Xr)(t,e))},[Symbol.iterator]:function*(){for(var t=this._groups,e=0,i=t.length;e<i;++e)for(var s,n=t[e],r=0,o=n.length;r<o;++r)(s=n[r])&&(yield s)}};const io={geoAlbersUsa:function(){var t,e,i,s,n,r,o=Ws().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7]),a=Ws().rotate([154,0]).center([-2,58.5]).parallels([55,65]),l=Ws().rotate([157,0]).center([-3,19.9]).parallels([8,18]),d={point:function(t,e){r=[t,e]}};function h(t){var e=t[0],o=t[1];return r=null,i.point(e,o),r||(s.point(e,o),r)||(n.point(e,o),r)}function c(){return t=e=null,h}return h.invert=function(t){var e=o.scale(),i=o.translate(),s=(t[0]-i[0])/e,n=(t[1]-i[1])/e;return(n>=.12&&n<.234&&s>=-.425&&s<-.214?a:n>=.166&&n<.234&&s>=-.214&&s<-.115?l:o).invert(t)},h.stream=function(i){return t&&e===i?t:(s=[o.stream(e=i),a.stream(i),l.stream(i)],n=s.length,t={point:function(t,e){for(var i=-1;++i<n;)s[i].point(t,e)},sphere:function(){for(var t=-1;++t<n;)s[t].sphere()},lineStart:function(){for(var t=-1;++t<n;)s[t].lineStart()},lineEnd:function(){for(var t=-1;++t<n;)s[t].lineEnd()},polygonStart:function(){for(var t=-1;++t<n;)s[t].polygonStart()},polygonEnd:function(){for(var t=-1;++t<n;)s[t].polygonEnd()}});var s,n},h.precision=function(t){return arguments.length?(o.precision(t),a.precision(t),l.precision(t),c()):o.precision()},h.scale=function(t){return arguments.length?(o.scale(t),a.scale(.35*t),l.scale(t),h.translate(o.translate())):o.scale()},h.translate=function(t){if(!arguments.length)return o.translate();var e=o.scale(),r=+t[0],h=+t[1];return i=o.translate(t).clipExtent([[r-.455*e,h-.238*e],[r+.455*e,h+.238*e]]).stream(d),s=a.translate([r-.307*e,h+.201*e]).clipExtent([[r-.425*e+Li,h+.12*e+Li],[r-.214*e-Li,h+.234*e-Li]]).stream(d),n=l.translate([r-.205*e,h+.212*e]).clipExtent([[r-.214*e+Li,h+.166*e+Li],[r-.115*e-Li,h+.234*e-Li]]).stream(d),c()},h.fitExtent=function(t,e){return Ds(h,t,e)},h.fitSize=function(t,e){return Rs(h,t,e)},h.fitWidth=function(t,e){return $s(h,t,e)},h.fitHeight=function(t,e){return Fs(h,t,e)},h.scale(1070)},geoPath:function(t,e){let i,s,n=3,r=4.5;function o(t){return t&&("function"==typeof r&&s.pointRadius(+r.apply(this,arguments)),Os(t,i(s))),s.result()}return o.area=function(t){return Os(t,i(dn)),dn.result()},o.measure=function(t){return Os(t,i(Un)),Un.result()},o.bounds=function(t){return Os(t,i(Ms)),Ms.result()},o.centroid=function(t){return Os(t,i(In)),In.result()},o.projection=function(e){return arguments.length?(i=null==e?(t=null,ms):(t=e).stream,o):t},o.context=function(t){return arguments.length?(s=null==t?(e=null,new Gn(n)):new Pn(e=t),"function"!=typeof r&&s.pointRadius(r),o):e},o.pointRadius=function(t){return arguments.length?(r="function"==typeof t?t:(s.pointRadius(+t),+t),o):r},o.digits=function(t){if(!arguments.length)return n;if(null==t)n=null;else{const e=Math.floor(t);if(!(e>=0))throw new RangeError(`invalid digits: ${t}`);n=e}return null===e&&(s=new Gn(n)),o},o.projection(t).digits(n).context(e)},select:function(t){return"string"==typeof t?new to([[document.querySelector(t)]],[document.documentElement]):new to([[t]],Qr)},selectAll:function(t){return"string"==typeof t?new to([document.querySelectorAll(t)],[document.documentElement]):new to([Yn(t)],Qr)}};function so(t){return null!==t&&"object"==typeof t&&"constructor"in t&&t.constructor===Object}function no(t,e){void 0===t&&(t={}),void 0===e&&(e={}),Object.keys(e).forEach((i=>{void 0===t[i]?t[i]=e[i]:so(e[i])&&so(t[i])&&Object.keys(e[i]).length>0&&no(t[i],e[i])}))}const ro={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function oo(){const t="undefined"!=typeof document?document:{};return no(t,ro),t}const ao={document:ro,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(t){return"undefined"==typeof setTimeout?(t(),null):setTimeout(t,0)},cancelAnimationFrame(t){"undefined"!=typeof setTimeout&&clearTimeout(t)}};function lo(){const t="undefined"!=typeof window?window:{};return no(t,ao),t}function ho(t,e){return void 0===e&&(e=0),setTimeout(t,e)}function co(){return Date.now()}function uo(t,e){void 0===e&&(e="x");const i=lo();let s,n,r;const o=function(t){const e=lo();let i;return e.getComputedStyle&&(i=e.getComputedStyle(t,null)),!i&&t.currentStyle&&(i=t.currentStyle),i||(i=t.style),i}(t);return i.WebKitCSSMatrix?(n=o.transform||o.webkitTransform,n.split(",").length>6&&(n=n.split(", ").map((t=>t.replace(",","."))).join(", ")),r=new i.WebKitCSSMatrix("none"===n?"":n)):(r=o.MozTransform||o.OTransform||o.MsTransform||o.msTransform||o.transform||o.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),s=r.toString().split(",")),"x"===e&&(n=i.WebKitCSSMatrix?r.m41:16===s.length?parseFloat(s[12]):parseFloat(s[4])),"y"===e&&(n=i.WebKitCSSMatrix?r.m42:16===s.length?parseFloat(s[13]):parseFloat(s[5])),n||0}function po(t){return"object"==typeof t&&null!==t&&t.constructor&&"Object"===Object.prototype.toString.call(t).slice(8,-1)}function mo(t){return"undefined"!=typeof window&&void 0!==window.HTMLElement?t instanceof HTMLElement:t&&(1===t.nodeType||11===t.nodeType)}function fo(){const t=Object(arguments.length<=0?void 0:arguments[0]),e=["__proto__","constructor","prototype"];for(let i=1;i<arguments.length;i+=1){const s=i<0||arguments.length<=i?void 0:arguments[i];if(null!=s&&!mo(s)){const i=Object.keys(Object(s)).filter((t=>e.indexOf(t)<0));for(let e=0,n=i.length;e<n;e+=1){const n=i[e],r=Object.getOwnPropertyDescriptor(s,n);void 0!==r&&r.enumerable&&(po(t[n])&&po(s[n])?s[n].__swiper__?t[n]=s[n]:fo(t[n],s[n]):!po(t[n])&&po(s[n])?(t[n]={},s[n].__swiper__?t[n]=s[n]:fo(t[n],s[n])):t[n]=s[n])}}}return t}function vo(t,e,i){t.style.setProperty(e,i)}function go(t){let{swiper:e,targetPosition:i,side:s}=t;const n=lo(),r=-e.translate;let o,a=null;const l=e.params.speed;e.wrapperEl.style.scrollSnapType="none",n.cancelAnimationFrame(e.cssModeFrameID);const d=i>r?"next":"prev",h=(t,e)=>"next"===d&&t>=e||"prev"===d&&t<=e,c=()=>{o=(new Date).getTime(),null===a&&(a=o);const t=Math.max(Math.min((o-a)/l,1),0),d=.5-Math.cos(t*Math.PI)/2;let u=r+d*(i-r);if(h(u,i)&&(u=i),e.wrapperEl.scrollTo({[s]:u}),h(u,i))return e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.scrollSnapType="",setTimeout((()=>{e.wrapperEl.style.overflow="",e.wrapperEl.scrollTo({[s]:u})})),void n.cancelAnimationFrame(e.cssModeFrameID);e.cssModeFrameID=n.requestAnimationFrame(c)};c()}function _o(t,e){return void 0===e&&(e=""),[...t.children].filter((t=>t.matches(e)))}function yo(t){try{return}catch(t){}}function bo(t,e){void 0===e&&(e=[]);const i=document.createElement(t);return i.classList.add(...Array.isArray(e)?e:function(t){return void 0===t&&(t=""),t.trim().split(" ").filter((t=>!!t.trim()))}(e)),i}function Eo(t,e){return lo().getComputedStyle(t,null).getPropertyValue(e)}function wo(t){let e,i=t;if(i){for(e=0;null!==(i=i.previousSibling);)1===i.nodeType&&(e+=1);return e}}function To(t,e,i){const s=lo();return i?t["width"===e?"offsetWidth":"offsetHeight"]+parseFloat(s.getComputedStyle(t,null).getPropertyValue("width"===e?"margin-right":"margin-top"))+parseFloat(s.getComputedStyle(t,null).getPropertyValue("width"===e?"margin-left":"margin-bottom")):t.offsetWidth}let So,Ao,Lo;function Oo(){return So||(So=function(){const t=lo(),e=oo();return{smoothScroll:e.documentElement&&e.documentElement.style&&"scrollBehavior"in e.documentElement.style,touch:!!("ontouchstart"in t||t.DocumentTouch&&e instanceof t.DocumentTouch)}}()),So}function Co(t){return void 0===t&&(t={}),Ao||(Ao=function(t){let{userAgent:e}=void 0===t?{}:t;const i=Oo(),s=lo(),n=s.navigator.platform,r=e||s.navigator.userAgent,o={ios:!1,android:!1},a=s.screen.width,l=s.screen.height,d=r.match(/(Android);?[\s\/]+([\d.]+)?/);let h=r.match(/(iPad).*OS\s([\d_]+)/);const c=r.match(/(iPod)(.*OS\s([\d_]+))?/),u=!h&&r.match(/(iPhone\sOS|iOS)\s([\d_]+)/),p="Win32"===n;let m="MacIntel"===n;return!h&&m&&i.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${a}x${l}`)>=0&&(h=r.match(/(Version)\/([\d.]+)/),h||(h=[0,1,"13_0_0"]),m=!1),d&&!p&&(o.os="android",o.android=!0),(h||u||c)&&(o.os="ios",o.ios=!0),o}(t)),Ao}function Io(){return Lo||(Lo=function(){const t=lo();let e=!1;function i(){const e=t.navigator.userAgent.toLowerCase();return e.indexOf("safari")>=0&&e.indexOf("chrome")<0&&e.indexOf("android")<0}if(i()){const i=String(t.navigator.userAgent);if(i.includes("Version/")){const[t,s]=i.split("Version/")[1].split(" ")[0].split(".").map((t=>Number(t)));e=t<16||16===t&&s<2}}return{isSafari:e||i(),needPerspectiveFix:e,isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(t.navigator.userAgent)}}()),Lo}var Po={on(t,e,i){const s=this;if(!s.eventsListeners||s.destroyed)return s;if("function"!=typeof e)return s;const n=i?"unshift":"push";return t.split(" ").forEach((t=>{s.eventsListeners[t]||(s.eventsListeners[t]=[]),s.eventsListeners[t][n](e)})),s},once(t,e,i){const s=this;if(!s.eventsListeners||s.destroyed)return s;if("function"!=typeof e)return s;function n(){s.off(t,n),n.__emitterProxy&&delete n.__emitterProxy;for(var i=arguments.length,r=new Array(i),o=0;o<i;o++)r[o]=arguments[o];e.apply(s,r)}return n.__emitterProxy=e,s.on(t,n,i)},onAny(t,e){const i=this;if(!i.eventsListeners||i.destroyed)return i;if("function"!=typeof t)return i;const s=e?"unshift":"push";return i.eventsAnyListeners.indexOf(t)<0&&i.eventsAnyListeners[s](t),i},offAny(t){const e=this;if(!e.eventsListeners||e.destroyed)return e;if(!e.eventsAnyListeners)return e;const i=e.eventsAnyListeners.indexOf(t);return i>=0&&e.eventsAnyListeners.splice(i,1),e},off(t,e){const i=this;return!i.eventsListeners||i.destroyed?i:i.eventsListeners?(t.split(" ").forEach((t=>{void 0===e?i.eventsListeners[t]=[]:i.eventsListeners[t]&&i.eventsListeners[t].forEach(((s,n)=>{(s===e||s.__emitterProxy&&s.__emitterProxy===e)&&i.eventsListeners[t].splice(n,1)}))})),i):i},emit(){const t=this;if(!t.eventsListeners||t.destroyed)return t;if(!t.eventsListeners)return t;let e,i,s;for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];"string"==typeof r[0]||Array.isArray(r[0])?(e=r[0],i=r.slice(1,r.length),s=t):(e=r[0].events,i=r[0].data,s=r[0].context||t),i.unshift(s);return(Array.isArray(e)?e:e.split(" ")).forEach((e=>{t.eventsAnyListeners&&t.eventsAnyListeners.length&&t.eventsAnyListeners.forEach((t=>{t.apply(s,[e,...i])})),t.eventsListeners&&t.eventsListeners[e]&&t.eventsListeners[e].forEach((t=>{t.apply(s,i)}))})),t}};const No=(t,e)=>{if(!t||t.destroyed||!t.params)return;const i=e.closest(t.isElement?"swiper-slide":`.${t.params.slideClass}`);if(i){let e=i.querySelector(`.${t.params.lazyPreloaderClass}`);!e&&t.isElement&&(i.shadowRoot?e=i.shadowRoot.querySelector(`.${t.params.lazyPreloaderClass}`):requestAnimationFrame((()=>{i.shadowRoot&&(e=i.shadowRoot.querySelector(`.${t.params.lazyPreloaderClass}`),e&&e.remove())}))),e&&e.remove()}},xo=(t,e)=>{if(!t.slides[e])return;const i=t.slides[e].querySelector('[loading="lazy"]');i&&i.removeAttribute("loading")},Mo=t=>{if(!t||t.destroyed||!t.params)return;let e=t.params.lazyPreloadPrevNext;const i=t.slides.length;if(!i||!e||e<0)return;e=Math.min(e,i);const s="auto"===t.params.slidesPerView?t.slidesPerViewDynamic():Math.ceil(t.params.slidesPerView),n=t.activeIndex;if(t.params.grid&&t.params.grid.rows>1){const i=n,r=[i-e];return r.push(...Array.from({length:e}).map(((t,e)=>i+s+e))),void t.slides.forEach(((e,i)=>{r.includes(e.column)&&xo(t,i)}))}const r=n+s-1;if(t.params.rewind||t.params.loop)for(let s=n-e;s<=r+e;s+=1){const e=(s%i+i)%i;(e<n||e>r)&&xo(t,e)}else for(let s=Math.max(n-e,0);s<=Math.min(r+e,i-1);s+=1)s!==n&&(s>r||s<n)&&xo(t,s)};var ko={updateSize:function(){const t=this;let e,i;const s=t.el;e=void 0!==t.params.width&&null!==t.params.width?t.params.width:s.clientWidth,i=void 0!==t.params.height&&null!==t.params.height?t.params.height:s.clientHeight,0===e&&t.isHorizontal()||0===i&&t.isVertical()||(e=e-parseInt(Eo(s,"padding-left")||0,10)-parseInt(Eo(s,"padding-right")||0,10),i=i-parseInt(Eo(s,"padding-top")||0,10)-parseInt(Eo(s,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(i)&&(i=0),Object.assign(t,{width:e,height:i,size:t.isHorizontal()?e:i}))},updateSlides:function(){const t=this;function e(e,i){return parseFloat(e.getPropertyValue(t.getDirectionLabel(i))||0)}const i=t.params,{wrapperEl:s,slidesEl:n,size:r,rtlTranslate:o,wrongRTL:a}=t,l=t.virtual&&i.virtual.enabled,d=l?t.virtual.slides.length:t.slides.length,h=_o(n,`.${t.params.slideClass}, swiper-slide`),c=l?t.virtual.slides.length:h.length;let u=[];const p=[],m=[];let f=i.slidesOffsetBefore;"function"==typeof f&&(f=i.slidesOffsetBefore.call(t));let v=i.slidesOffsetAfter;"function"==typeof v&&(v=i.slidesOffsetAfter.call(t));const g=t.snapGrid.length,_=t.slidesGrid.length;let y=i.spaceBetween,b=-f,E=0,w=0;if(void 0===r)return;"string"==typeof y&&y.indexOf("%")>=0?y=parseFloat(y.replace("%",""))/100*r:"string"==typeof y&&(y=parseFloat(y)),t.virtualSize=-y,h.forEach((t=>{o?t.style.marginLeft="":t.style.marginRight="",t.style.marginBottom="",t.style.marginTop=""})),i.centeredSlides&&i.cssMode&&(vo(s,"--swiper-centered-offset-before",""),vo(s,"--swiper-centered-offset-after",""));const T=i.grid&&i.grid.rows>1&&t.grid;let S;T?t.grid.initSlides(h):t.grid&&t.grid.unsetSlides();const A="auto"===i.slidesPerView&&i.breakpoints&&Object.keys(i.breakpoints).filter((t=>void 0!==i.breakpoints[t].slidesPerView)).length>0;for(let s=0;s<c;s+=1){let n;if(S=0,h[s]&&(n=h[s]),T&&t.grid.updateSlide(s,n,h),!h[s]||"none"!==Eo(n,"display")){if("auto"===i.slidesPerView){A&&(h[s].style[t.getDirectionLabel("width")]="");const r=getComputedStyle(n),o=n.style.transform,a=n.style.webkitTransform;if(o&&(n.style.transform="none"),a&&(n.style.webkitTransform="none"),i.roundLengths)S=t.isHorizontal()?To(n,"width",!0):To(n,"height",!0);else{const t=e(r,"width"),i=e(r,"padding-left"),s=e(r,"padding-right"),o=e(r,"margin-left"),a=e(r,"margin-right"),l=r.getPropertyValue("box-sizing");if(l&&"border-box"===l)S=t+o+a;else{const{clientWidth:e,offsetWidth:r}=n;S=t+i+s+o+a+(r-e)}}o&&(n.style.transform=o),a&&(n.style.webkitTransform=a),i.roundLengths&&(S=Math.floor(S))}else S=(r-(i.slidesPerView-1)*y)/i.slidesPerView,i.roundLengths&&(S=Math.floor(S)),h[s]&&(h[s].style[t.getDirectionLabel("width")]=`${S}px`);h[s]&&(h[s].swiperSlideSize=S),m.push(S),i.centeredSlides?(b=b+S/2+E/2+y,0===E&&0!==s&&(b=b-r/2-y),0===s&&(b=b-r/2-y),Math.abs(b)<.001&&(b=0),i.roundLengths&&(b=Math.floor(b)),w%i.slidesPerGroup==0&&u.push(b),p.push(b)):(i.roundLengths&&(b=Math.floor(b)),(w-Math.min(t.params.slidesPerGroupSkip,w))%t.params.slidesPerGroup==0&&u.push(b),p.push(b),b=b+S+y),t.virtualSize+=S+y,E=S,w+=1}}if(t.virtualSize=Math.max(t.virtualSize,r)+v,o&&a&&("slide"===i.effect||"coverflow"===i.effect)&&(s.style.width=`${t.virtualSize+y}px`),i.setWrapperSize&&(s.style[t.getDirectionLabel("width")]=`${t.virtualSize+y}px`),T&&t.grid.updateWrapperSize(S,u),!i.centeredSlides){const e=[];for(let s=0;s<u.length;s+=1){let n=u[s];i.roundLengths&&(n=Math.floor(n)),u[s]<=t.virtualSize-r&&e.push(n)}u=e,Math.floor(t.virtualSize-r)-Math.floor(u[u.length-1])>1&&u.push(t.virtualSize-r)}if(l&&i.loop){const e=m[0]+y;if(i.slidesPerGroup>1){const s=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/i.slidesPerGroup),n=e*i.slidesPerGroup;for(let t=0;t<s;t+=1)u.push(u[u.length-1]+n)}for(let s=0;s<t.virtual.slidesBefore+t.virtual.slidesAfter;s+=1)1===i.slidesPerGroup&&u.push(u[u.length-1]+e),p.push(p[p.length-1]+e),t.virtualSize+=e}if(0===u.length&&(u=[0]),0!==y){const e=t.isHorizontal()&&o?"marginLeft":t.getDirectionLabel("marginRight");h.filter(((t,e)=>!(i.cssMode&&!i.loop)||e!==h.length-1)).forEach((t=>{t.style[e]=`${y}px`}))}if(i.centeredSlides&&i.centeredSlidesBounds){let t=0;m.forEach((e=>{t+=e+(y||0)})),t-=y;const e=t-r;u=u.map((t=>t<=0?-f:t>e?e+v:t))}if(i.centerInsufficientSlides){let t=0;if(m.forEach((e=>{t+=e+(y||0)})),t-=y,t<r){const e=(r-t)/2;u.forEach(((t,i)=>{u[i]=t-e})),p.forEach(((t,i)=>{p[i]=t+e}))}}if(Object.assign(t,{slides:h,snapGrid:u,slidesGrid:p,slidesSizesGrid:m}),i.centeredSlides&&i.cssMode&&!i.centeredSlidesBounds){vo(s,"--swiper-centered-offset-before",-u[0]+"px"),vo(s,"--swiper-centered-offset-after",t.size/2-m[m.length-1]/2+"px");const e=-t.snapGrid[0],i=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map((t=>t+e)),t.slidesGrid=t.slidesGrid.map((t=>t+i))}if(c!==d&&t.emit("slidesLengthChange"),u.length!==g&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),p.length!==_&&t.emit("slidesGridLengthChange"),i.watchSlidesProgress&&t.updateSlidesOffset(),t.emit("slidesUpdated"),!(l||i.cssMode||"slide"!==i.effect&&"fade"!==i.effect)){const e=`${i.containerModifierClass}backface-hidden`,s=t.el.classList.contains(e);c<=i.maxBackfaceHiddenSlides?s||t.el.classList.add(e):s&&t.el.classList.remove(e)}},updateAutoHeight:function(t){const e=this,i=[],s=e.virtual&&e.params.virtual.enabled;let n,r=0;"number"==typeof t?e.setTransition(t):!0===t&&e.setTransition(e.params.speed);const o=t=>s?e.slides[e.getSlideIndexByData(t)]:e.slides[t];if("auto"!==e.params.slidesPerView&&e.params.slidesPerView>1)if(e.params.centeredSlides)(e.visibleSlides||[]).forEach((t=>{i.push(t)}));else for(n=0;n<Math.ceil(e.params.slidesPerView);n+=1){const t=e.activeIndex+n;if(t>e.slides.length&&!s)break;i.push(o(t))}else i.push(o(e.activeIndex));for(n=0;n<i.length;n+=1)if(void 0!==i[n]){const t=i[n].offsetHeight;r=t>r?t:r}(r||0===r)&&(e.wrapperEl.style.height=`${r}px`)},updateSlidesOffset:function(){const t=this,e=t.slides,i=t.isElement?t.isHorizontal()?t.wrapperEl.offsetLeft:t.wrapperEl.offsetTop:0;for(let s=0;s<e.length;s+=1)e[s].swiperSlideOffset=(t.isHorizontal()?e[s].offsetLeft:e[s].offsetTop)-i-t.cssOverflowAdjustment()},updateSlidesProgress:function(t){void 0===t&&(t=this&&this.translate||0);const e=this,i=e.params,{slides:s,rtlTranslate:n,snapGrid:r}=e;if(0===s.length)return;void 0===s[0].swiperSlideOffset&&e.updateSlidesOffset();let o=-t;n&&(o=t),s.forEach((t=>{t.classList.remove(i.slideVisibleClass,i.slideFullyVisibleClass)})),e.visibleSlidesIndexes=[],e.visibleSlides=[];let a=i.spaceBetween;"string"==typeof a&&a.indexOf("%")>=0?a=parseFloat(a.replace("%",""))/100*e.size:"string"==typeof a&&(a=parseFloat(a));for(let t=0;t<s.length;t+=1){const l=s[t];let d=l.swiperSlideOffset;i.cssMode&&i.centeredSlides&&(d-=s[0].swiperSlideOffset);const h=(o+(i.centeredSlides?e.minTranslate():0)-d)/(l.swiperSlideSize+a),c=(o-r[0]+(i.centeredSlides?e.minTranslate():0)-d)/(l.swiperSlideSize+a),u=-(o-d),p=u+e.slidesSizesGrid[t],m=u>=0&&u<=e.size-e.slidesSizesGrid[t];(u>=0&&u<e.size-1||p>1&&p<=e.size||u<=0&&p>=e.size)&&(e.visibleSlides.push(l),e.visibleSlidesIndexes.push(t),s[t].classList.add(i.slideVisibleClass)),m&&s[t].classList.add(i.slideFullyVisibleClass),l.progress=n?-h:h,l.originalProgress=n?-c:c}},updateProgress:function(t){const e=this;if(void 0===t){const i=e.rtlTranslate?-1:1;t=e&&e.translate&&e.translate*i||0}const i=e.params,s=e.maxTranslate()-e.minTranslate();let{progress:n,isBeginning:r,isEnd:o,progressLoop:a}=e;const l=r,d=o;if(0===s)n=0,r=!0,o=!0;else{n=(t-e.minTranslate())/s;const i=Math.abs(t-e.minTranslate())<1,a=Math.abs(t-e.maxTranslate())<1;r=i||n<=0,o=a||n>=1,i&&(n=0),a&&(n=1)}if(i.loop){const i=e.getSlideIndexByData(0),s=e.getSlideIndexByData(e.slides.length-1),n=e.slidesGrid[i],r=e.slidesGrid[s],o=e.slidesGrid[e.slidesGrid.length-1],l=Math.abs(t);a=l>=n?(l-n)/o:(l+o-r)/o,a>1&&(a-=1)}Object.assign(e,{progress:n,progressLoop:a,isBeginning:r,isEnd:o}),(i.watchSlidesProgress||i.centeredSlides&&i.autoHeight)&&e.updateSlidesProgress(t),r&&!l&&e.emit("reachBeginning toEdge"),o&&!d&&e.emit("reachEnd toEdge"),(l&&!r||d&&!o)&&e.emit("fromEdge"),e.emit("progress",n)},updateSlidesClasses:function(){const t=this,{slides:e,params:i,slidesEl:s,activeIndex:n}=t,r=t.virtual&&i.virtual.enabled,o=t.grid&&i.grid&&i.grid.rows>1,a=t=>_o(s,`.${i.slideClass}${t}, swiper-slide${t}`)[0];let l,d,h;if(e.forEach((t=>{t.classList.remove(i.slideActiveClass,i.slideNextClass,i.slidePrevClass)})),r)if(i.loop){let e=n-t.virtual.slidesBefore;e<0&&(e=t.virtual.slides.length+e),e>=t.virtual.slides.length&&(e-=t.virtual.slides.length),l=a(`[data-swiper-slide-index="${e}"]`)}else l=a(`[data-swiper-slide-index="${n}"]`);else o?(l=e.filter((t=>t.column===n))[0],h=e.filter((t=>t.column===n+1))[0],d=e.filter((t=>t.column===n-1))[0]):l=e[n];l&&(l.classList.add(i.slideActiveClass),o?(h&&h.classList.add(i.slideNextClass),d&&d.classList.add(i.slidePrevClass)):(h=function(t,e){const i=[];for(;t.nextElementSibling;){const s=t.nextElementSibling;e?s.matches(e)&&i.push(s):i.push(s),t=s}return i}(l,`.${i.slideClass}, swiper-slide`)[0],i.loop&&!h&&(h=e[0]),h&&h.classList.add(i.slideNextClass),d=function(t,e){const i=[];for(;t.previousElementSibling;){const s=t.previousElementSibling;e?s.matches(e)&&i.push(s):i.push(s),t=s}return i}(l,`.${i.slideClass}, swiper-slide`)[0],i.loop&&0===!d&&(d=e[e.length-1]),d&&d.classList.add(i.slidePrevClass))),t.emitSlidesClasses()},updateActiveIndex:function(t){const e=this,i=e.rtlTranslate?e.translate:-e.translate,{snapGrid:s,params:n,activeIndex:r,realIndex:o,snapIndex:a}=e;let l,d=t;const h=t=>{let i=t-e.virtual.slidesBefore;return i<0&&(i=e.virtual.slides.length+i),i>=e.virtual.slides.length&&(i-=e.virtual.slides.length),i};if(void 0===d&&(d=function(t){const{slidesGrid:e,params:i}=t,s=t.rtlTranslate?t.translate:-t.translate;let n;for(let t=0;t<e.length;t+=1)void 0!==e[t+1]?s>=e[t]&&s<e[t+1]-(e[t+1]-e[t])/2?n=t:s>=e[t]&&s<e[t+1]&&(n=t+1):s>=e[t]&&(n=t);return i.normalizeSlideIndex&&(n<0||void 0===n)&&(n=0),n}(e)),s.indexOf(i)>=0)l=s.indexOf(i);else{const t=Math.min(n.slidesPerGroupSkip,d);l=t+Math.floor((d-t)/n.slidesPerGroup)}if(l>=s.length&&(l=s.length-1),d===r&&!e.params.loop)return void(l!==a&&(e.snapIndex=l,e.emit("snapIndexChange")));if(d===r&&e.params.loop&&e.virtual&&e.params.virtual.enabled)return void(e.realIndex=h(d));const c=e.grid&&n.grid&&n.grid.rows>1;let u;if(e.virtual&&n.virtual.enabled&&n.loop)u=h(d);else if(c){const t=e.slides.filter((t=>t.column===d))[0];let i=parseInt(t.getAttribute("data-swiper-slide-index"),10);Number.isNaN(i)&&(i=Math.max(e.slides.indexOf(t),0)),u=Math.floor(i/n.grid.rows)}else if(e.slides[d]){const t=e.slides[d].getAttribute("data-swiper-slide-index");u=t?parseInt(t,10):d}else u=d;Object.assign(e,{previousSnapIndex:a,snapIndex:l,previousRealIndex:o,realIndex:u,previousIndex:r,activeIndex:d}),e.initialized&&Mo(e),e.emit("activeIndexChange"),e.emit("snapIndexChange"),(e.initialized||e.params.runCallbacksOnInit)&&(o!==u&&e.emit("realIndexChange"),e.emit("slideChange"))},updateClickedSlide:function(t,e){const i=this,s=i.params;let n=t.closest(`.${s.slideClass}, swiper-slide`);!n&&i.isElement&&e&&e.length>1&&e.includes(t)&&[...e.slice(e.indexOf(t)+1,e.length)].forEach((t=>{!n&&t.matches&&t.matches(`.${s.slideClass}, swiper-slide`)&&(n=t)}));let r,o=!1;if(n)for(let t=0;t<i.slides.length;t+=1)if(i.slides[t]===n){o=!0,r=t;break}if(!n||!o)return i.clickedSlide=void 0,void(i.clickedIndex=void 0);i.clickedSlide=n,i.virtual&&i.params.virtual.enabled?i.clickedIndex=parseInt(n.getAttribute("data-swiper-slide-index"),10):i.clickedIndex=r,s.slideToClickedSlide&&void 0!==i.clickedIndex&&i.clickedIndex!==i.activeIndex&&i.slideToClickedSlide()}};var Do={getTranslate:function(t){void 0===t&&(t=this.isHorizontal()?"x":"y");const{params:e,rtlTranslate:i,translate:s,wrapperEl:n}=this;if(e.virtualTranslate)return i?-s:s;if(e.cssMode)return s;let r=uo(n,t);return r+=this.cssOverflowAdjustment(),i&&(r=-r),r||0},setTranslate:function(t,e){const i=this,{rtlTranslate:s,params:n,wrapperEl:r,progress:o}=i;let a,l=0,d=0;i.isHorizontal()?l=s?-t:t:d=t,n.roundLengths&&(l=Math.floor(l),d=Math.floor(d)),i.previousTranslate=i.translate,i.translate=i.isHorizontal()?l:d,n.cssMode?r[i.isHorizontal()?"scrollLeft":"scrollTop"]=i.isHorizontal()?-l:-d:n.virtualTranslate||(i.isHorizontal()?l-=i.cssOverflowAdjustment():d-=i.cssOverflowAdjustment(),r.style.transform=`translate3d(${l}px, ${d}px, 0px)`);const h=i.maxTranslate()-i.minTranslate();a=0===h?0:(t-i.minTranslate())/h,a!==o&&i.updateProgress(t),i.emit("setTranslate",i.translate,e)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(t,e,i,s,n){void 0===t&&(t=0),void 0===e&&(e=this.params.speed),void 0===i&&(i=!0),void 0===s&&(s=!0);const r=this,{params:o,wrapperEl:a}=r;if(r.animating&&o.preventInteractionOnTransition)return!1;const l=r.minTranslate(),d=r.maxTranslate();let h;if(h=s&&t>l?l:s&&t<d?d:t,r.updateProgress(h),o.cssMode){const t=r.isHorizontal();if(0===e)a[t?"scrollLeft":"scrollTop"]=-h;else{if(!r.support.smoothScroll)return go({swiper:r,targetPosition:-h,side:t?"left":"top"}),!0;a.scrollTo({[t?"left":"top"]:-h,behavior:"smooth"})}return!0}return 0===e?(r.setTransition(0),r.setTranslate(h),i&&(r.emit("beforeTransitionStart",e,n),r.emit("transitionEnd"))):(r.setTransition(e),r.setTranslate(h),i&&(r.emit("beforeTransitionStart",e,n),r.emit("transitionStart")),r.animating||(r.animating=!0,r.onTranslateToWrapperTransitionEnd||(r.onTranslateToWrapperTransitionEnd=function(t){r&&!r.destroyed&&t.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.onTranslateToWrapperTransitionEnd=null,delete r.onTranslateToWrapperTransitionEnd,i&&r.emit("transitionEnd"))}),r.wrapperEl.addEventListener("transitionend",r.onTranslateToWrapperTransitionEnd))),!0}};function Ro(t){let{swiper:e,runCallbacks:i,direction:s,step:n}=t;const{activeIndex:r,previousIndex:o}=e;let a=s;if(a||(a=r>o?"next":r<o?"prev":"reset"),e.emit(`transition${n}`),i&&r!==o){if("reset"===a)return void e.emit(`slideResetTransition${n}`);e.emit(`slideChangeTransition${n}`),"next"===a?e.emit(`slideNextTransition${n}`):e.emit(`slidePrevTransition${n}`)}}var $o={slideTo:function(t,e,i,s,n){void 0===t&&(t=0),void 0===e&&(e=this.params.speed),void 0===i&&(i=!0),"string"==typeof t&&(t=parseInt(t,10));const r=this;let o=t;o<0&&(o=0);const{params:a,snapGrid:l,slidesGrid:d,previousIndex:h,activeIndex:c,rtlTranslate:u,wrapperEl:p,enabled:m}=r;if(r.animating&&a.preventInteractionOnTransition||!m&&!s&&!n)return!1;const f=Math.min(r.params.slidesPerGroupSkip,o);let v=f+Math.floor((o-f)/r.params.slidesPerGroup);v>=l.length&&(v=l.length-1);const g=-l[v];if(a.normalizeSlideIndex)for(let t=0;t<d.length;t+=1){const e=-Math.floor(100*g),i=Math.floor(100*d[t]),s=Math.floor(100*d[t+1]);void 0!==d[t+1]?e>=i&&e<s-(s-i)/2?o=t:e>=i&&e<s&&(o=t+1):e>=i&&(o=t)}if(r.initialized&&o!==c){if(!r.allowSlideNext&&(u?g>r.translate&&g>r.minTranslate():g<r.translate&&g<r.minTranslate()))return!1;if(!r.allowSlidePrev&&g>r.translate&&g>r.maxTranslate()&&(c||0)!==o)return!1}let _;if(o!==(h||0)&&i&&r.emit("beforeSlideChangeStart"),r.updateProgress(g),_=o>c?"next":o<c?"prev":"reset",u&&-g===r.translate||!u&&g===r.translate)return r.updateActiveIndex(o),a.autoHeight&&r.updateAutoHeight(),r.updateSlidesClasses(),"slide"!==a.effect&&r.setTranslate(g),"reset"!==_&&(r.transitionStart(i,_),r.transitionEnd(i,_)),!1;if(a.cssMode){const t=r.isHorizontal(),i=u?g:-g;if(0===e){const e=r.virtual&&r.params.virtual.enabled;e&&(r.wrapperEl.style.scrollSnapType="none",r._immediateVirtual=!0),e&&!r._cssModeVirtualInitialSet&&r.params.initialSlide>0?(r._cssModeVirtualInitialSet=!0,requestAnimationFrame((()=>{p[t?"scrollLeft":"scrollTop"]=i}))):p[t?"scrollLeft":"scrollTop"]=i,e&&requestAnimationFrame((()=>{r.wrapperEl.style.scrollSnapType="",r._immediateVirtual=!1}))}else{if(!r.support.smoothScroll)return go({swiper:r,targetPosition:i,side:t?"left":"top"}),!0;p.scrollTo({[t?"left":"top"]:i,behavior:"smooth"})}return!0}return r.setTransition(e),r.setTranslate(g),r.updateActiveIndex(o),r.updateSlidesClasses(),r.emit("beforeTransitionStart",e,s),r.transitionStart(i,_),0===e?r.transitionEnd(i,_):r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(t){r&&!r.destroyed&&t.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(i,_))}),r.wrapperEl.addEventListener("transitionend",r.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(t,e,i,s){if(void 0===t&&(t=0),void 0===e&&(e=this.params.speed),void 0===i&&(i=!0),"string"==typeof t){t=parseInt(t,10)}const n=this,r=n.grid&&n.params.grid&&n.params.grid.rows>1;let o=t;if(n.params.loop)if(n.virtual&&n.params.virtual.enabled)o+=n.virtual.slidesBefore;else{let t;if(r){const e=o*n.params.grid.rows;t=n.slides.filter((t=>1*t.getAttribute("data-swiper-slide-index")===e))[0].column}else t=n.getSlideIndexByData(o);const e=r?Math.ceil(n.slides.length/n.params.grid.rows):n.slides.length,{centeredSlides:i}=n.params;let s=n.params.slidesPerView;"auto"===s?s=n.slidesPerViewDynamic():(s=Math.ceil(parseFloat(n.params.slidesPerView,10)),i&&s%2==0&&(s+=1));let a=e-t<s;if(i&&(a=a||t<Math.ceil(s/2)),a){const s=i?t<n.activeIndex?"prev":"next":t-n.activeIndex-1<n.params.slidesPerView?"next":"prev";n.loopFix({direction:s,slideTo:!0,activeSlideIndex:"next"===s?t+1:t-e+1,slideRealIndex:"next"===s?n.realIndex:void 0})}if(r){const t=o*n.params.grid.rows;o=n.slides.filter((e=>1*e.getAttribute("data-swiper-slide-index")===t))[0].column}else o=n.getSlideIndexByData(o)}return requestAnimationFrame((()=>{n.slideTo(o,e,i,s)})),n},slideNext:function(t,e,i){void 0===t&&(t=this.params.speed),void 0===e&&(e=!0);const s=this,{enabled:n,params:r,animating:o}=s;if(!n)return s;let a=r.slidesPerGroup;"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(a=Math.max(s.slidesPerViewDynamic("current",!0),1));const l=s.activeIndex<r.slidesPerGroupSkip?1:a,d=s.virtual&&r.virtual.enabled;if(r.loop){if(o&&!d&&r.loopPreventsSliding)return!1;if(s.loopFix({direction:"next"}),s._clientLeft=s.wrapperEl.clientLeft,s.activeIndex===s.slides.length-1&&r.cssMode)return requestAnimationFrame((()=>{s.slideTo(s.activeIndex+l,t,e,i)})),!0}return r.rewind&&s.isEnd?s.slideTo(0,t,e,i):s.slideTo(s.activeIndex+l,t,e,i)},slidePrev:function(t,e,i){void 0===t&&(t=this.params.speed),void 0===e&&(e=!0);const s=this,{params:n,snapGrid:r,slidesGrid:o,rtlTranslate:a,enabled:l,animating:d}=s;if(!l)return s;const h=s.virtual&&n.virtual.enabled;if(n.loop){if(d&&!h&&n.loopPreventsSliding)return!1;s.loopFix({direction:"prev"}),s._clientLeft=s.wrapperEl.clientLeft}function c(t){return t<0?-Math.floor(Math.abs(t)):Math.floor(t)}const u=c(a?s.translate:-s.translate),p=r.map((t=>c(t)));let m=r[p.indexOf(u)-1];if(void 0===m&&n.cssMode){let t;r.forEach(((e,i)=>{u>=e&&(t=i)})),void 0!==t&&(m=r[t>0?t-1:t])}let f=0;if(void 0!==m&&(f=o.indexOf(m),f<0&&(f=s.activeIndex-1),"auto"===n.slidesPerView&&1===n.slidesPerGroup&&n.slidesPerGroupAuto&&(f=f-s.slidesPerViewDynamic("previous",!0)+1,f=Math.max(f,0))),n.rewind&&s.isBeginning){const n=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(n,t,e,i)}return n.loop&&0===s.activeIndex&&n.cssMode?(requestAnimationFrame((()=>{s.slideTo(f,t,e,i)})),!0):s.slideTo(f,t,e,i)},slideReset:function(t,e,i){return void 0===t&&(t=this.params.speed),void 0===e&&(e=!0),this.slideTo(this.activeIndex,t,e,i)},slideToClosest:function(t,e,i,s){void 0===t&&(t=this.params.speed),void 0===e&&(e=!0),void 0===s&&(s=.5);const n=this;let r=n.activeIndex;const o=Math.min(n.params.slidesPerGroupSkip,r),a=o+Math.floor((r-o)/n.params.slidesPerGroup),l=n.rtlTranslate?n.translate:-n.translate;if(l>=n.snapGrid[a]){const t=n.snapGrid[a];l-t>(n.snapGrid[a+1]-t)*s&&(r+=n.params.slidesPerGroup)}else{const t=n.snapGrid[a-1];l-t<=(n.snapGrid[a]-t)*s&&(r-=n.params.slidesPerGroup)}return r=Math.max(r,0),r=Math.min(r,n.slidesGrid.length-1),n.slideTo(r,t,e,i)},slideToClickedSlide:function(){const t=this,{params:e,slidesEl:i}=t,s="auto"===e.slidesPerView?t.slidesPerViewDynamic():e.slidesPerView;let n,r=t.clickedIndex;const o=t.isElement?"swiper-slide":`.${e.slideClass}`;if(e.loop){if(t.animating)return;n=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),e.centeredSlides?r<t.loopedSlides-s/2||r>t.slides.length-t.loopedSlides+s/2?(t.loopFix(),r=t.getSlideIndex(_o(i,`${o}[data-swiper-slide-index="${n}"]`)[0]),ho((()=>{t.slideTo(r)}))):t.slideTo(r):r>t.slides.length-s?(t.loopFix(),r=t.getSlideIndex(_o(i,`${o}[data-swiper-slide-index="${n}"]`)[0]),ho((()=>{t.slideTo(r)}))):t.slideTo(r)}else t.slideTo(r)}};var Fo={loopCreate:function(t){const e=this,{params:i,slidesEl:s}=e;if(!i.loop||e.virtual&&e.params.virtual.enabled)return;const n=()=>{_o(s,`.${i.slideClass}, swiper-slide`).forEach(((t,e)=>{t.setAttribute("data-swiper-slide-index",e)}))},r=e.grid&&i.grid&&i.grid.rows>1,o=i.slidesPerGroup*(r?i.grid.rows:1),a=e.slides.length%o!=0,l=r&&e.slides.length%i.grid.rows!=0,d=t=>{for(let s=0;s<t;s+=1){const t=e.isElement?bo("swiper-slide",[i.slideBlankClass]):bo("div",[i.slideClass,i.slideBlankClass]);e.slidesEl.append(t)}};if(a){if(i.loopAddBlankSlides){d(o-e.slides.length%o),e.recalcSlides(),e.updateSlides()}else yo();n()}else if(l){if(i.loopAddBlankSlides){d(i.grid.rows-e.slides.length%i.grid.rows),e.recalcSlides(),e.updateSlides()}else yo();n()}else n();e.loopFix({slideRealIndex:t,direction:i.centeredSlides?void 0:"next"})},loopFix:function(t){let{slideRealIndex:e,slideTo:i=!0,direction:s,setTranslate:n,activeSlideIndex:r,byController:o,byMousewheel:a}=void 0===t?{}:t;const l=this;if(!l.params.loop)return;l.emit("beforeLoopFix");const{slides:d,allowSlidePrev:h,allowSlideNext:c,slidesEl:u,params:p}=l,{centeredSlides:m}=p;if(l.allowSlidePrev=!0,l.allowSlideNext=!0,l.virtual&&p.virtual.enabled)return i&&(p.centeredSlides||0!==l.snapIndex?p.centeredSlides&&l.snapIndex<p.slidesPerView?l.slideTo(l.virtual.slides.length+l.snapIndex,0,!1,!0):l.snapIndex===l.snapGrid.length-1&&l.slideTo(l.virtual.slidesBefore,0,!1,!0):l.slideTo(l.virtual.slides.length,0,!1,!0)),l.allowSlidePrev=h,l.allowSlideNext=c,void l.emit("loopFix");let f=p.slidesPerView;"auto"===f?f=l.slidesPerViewDynamic():(f=Math.ceil(parseFloat(p.slidesPerView,10)),m&&f%2==0&&(f+=1));const v=p.slidesPerGroupAuto?f:p.slidesPerGroup;let g=v;g%v!=0&&(g+=v-g%v),g+=p.loopAdditionalSlides,l.loopedSlides=g;const _=l.grid&&p.grid&&p.grid.rows>1;(d.length<f+g||_&&"row"===p.grid.fill)&&yo();const y=[],b=[];let E=l.activeIndex;void 0===r?r=l.getSlideIndex(d.filter((t=>t.classList.contains(p.slideActiveClass)))[0]):E=r;const w="next"===s||!s,T="prev"===s||!s;let S=0,A=0;const L=_?Math.ceil(d.length/p.grid.rows):d.length,O=(_?d[r].column:r)+(m&&void 0===n?-f/2+.5:0);if(O<g){S=Math.max(g-O,v);for(let t=0;t<g-O;t+=1){const e=t-Math.floor(t/L)*L;if(_){const t=L-e-1;for(let e=d.length-1;e>=0;e-=1)d[e].column===t&&y.push(e)}else y.push(L-e-1)}}else if(O+f>L-g){A=Math.max(O-(L-2*g),v);for(let t=0;t<A;t+=1){const e=t-Math.floor(t/L)*L;_?d.forEach(((t,i)=>{t.column===e&&b.push(i)})):b.push(e)}}if(l.__preventObserver__=!0,requestAnimationFrame((()=>{l.__preventObserver__=!1})),T&&y.forEach((t=>{d[t].swiperLoopMoveDOM=!0,u.prepend(d[t]),d[t].swiperLoopMoveDOM=!1})),w&&b.forEach((t=>{d[t].swiperLoopMoveDOM=!0,u.append(d[t]),d[t].swiperLoopMoveDOM=!1})),l.recalcSlides(),"auto"===p.slidesPerView?l.updateSlides():_&&(y.length>0&&T||b.length>0&&w)&&l.slides.forEach(((t,e)=>{l.grid.updateSlide(e,t,l.slides)})),p.watchSlidesProgress&&l.updateSlidesOffset(),i)if(y.length>0&&T){if(void 0===e){const t=l.slidesGrid[E],e=l.slidesGrid[E+S]-t;a?l.setTranslate(l.translate-e):(l.slideTo(E+S,0,!1,!0),n&&(l.touchEventsData.startTranslate=l.touchEventsData.startTranslate-e,l.touchEventsData.currentTranslate=l.touchEventsData.currentTranslate-e))}else if(n){const t=_?y.length/p.grid.rows:y.length;l.slideTo(l.activeIndex+t,0,!1,!0),l.touchEventsData.currentTranslate=l.translate}}else if(b.length>0&&w)if(void 0===e){const t=l.slidesGrid[E],e=l.slidesGrid[E-A]-t;a?l.setTranslate(l.translate-e):(l.slideTo(E-A,0,!1,!0),n&&(l.touchEventsData.startTranslate=l.touchEventsData.startTranslate-e,l.touchEventsData.currentTranslate=l.touchEventsData.currentTranslate-e))}else{const t=_?b.length/p.grid.rows:b.length;l.slideTo(l.activeIndex-t,0,!1,!0)}if(l.allowSlidePrev=h,l.allowSlideNext=c,l.controller&&l.controller.control&&!o){const t={slideRealIndex:e,direction:s,setTranslate:n,activeSlideIndex:r,byController:!0};Array.isArray(l.controller.control)?l.controller.control.forEach((e=>{!e.destroyed&&e.params.loop&&e.loopFix({...t,slideTo:e.params.slidesPerView===p.slidesPerView&&i})})):l.controller.control instanceof l.constructor&&l.controller.control.params.loop&&l.controller.control.loopFix({...t,slideTo:l.controller.control.params.slidesPerView===p.slidesPerView&&i})}l.emit("loopFix")},loopDestroy:function(){const t=this,{params:e,slidesEl:i}=t;if(!e.loop||t.virtual&&t.params.virtual.enabled)return;t.recalcSlides();const s=[];t.slides.forEach((t=>{const e=void 0===t.swiperSlideIndex?1*t.getAttribute("data-swiper-slide-index"):t.swiperSlideIndex;s[e]=t})),t.slides.forEach((t=>{t.removeAttribute("data-swiper-slide-index")})),s.forEach((t=>{i.append(t)})),t.recalcSlides(),t.slideTo(t.realIndex,0)}};function Ho(t,e,i){const s=lo(),{params:n}=t,r=n.edgeSwipeDetection,o=n.edgeSwipeThreshold;return!r||!(i<=o||i>=s.innerWidth-o)||"prevent"===r&&(e.preventDefault(),!0)}function Uo(t){const e=this,i=oo();let s=t;s.originalEvent&&(s=s.originalEvent);const n=e.touchEventsData;if("pointerdown"===s.type){if(null!==n.pointerId&&n.pointerId!==s.pointerId)return;n.pointerId=s.pointerId}else"touchstart"===s.type&&1===s.targetTouches.length&&(n.touchId=s.targetTouches[0].identifier);if("touchstart"===s.type)return void Ho(e,s,s.targetTouches[0].pageX);const{params:r,touches:o,enabled:a}=e;if(!a)return;if(!r.simulateTouch&&"mouse"===s.pointerType)return;if(e.animating&&r.preventInteractionOnTransition)return;!e.animating&&r.cssMode&&r.loop&&e.loopFix();let l=s.target;if("wrapper"===r.touchEventsTarget&&!e.wrapperEl.contains(l))return;if("which"in s&&3===s.which)return;if("button"in s&&s.button>0)return;if(n.isTouched&&n.isMoved)return;const d=!!r.noSwipingClass&&""!==r.noSwipingClass,h=s.composedPath?s.composedPath():s.path;d&&s.target&&s.target.shadowRoot&&h&&(l=h[0]);const c=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,u=!(!s.target||!s.target.shadowRoot);if(r.noSwiping&&(u?function(t,e){return void 0===e&&(e=this),function e(i){if(!i||i===oo()||i===lo())return null;i.assignedSlot&&(i=i.assignedSlot);const s=i.closest(t);return s||i.getRootNode?s||e(i.getRootNode().host):null}(e)}(c,l):l.closest(c)))return void(e.allowClick=!0);if(r.swipeHandler&&!l.closest(r.swipeHandler))return;o.currentX=s.pageX,o.currentY=s.pageY;const p=o.currentX,m=o.currentY;if(!Ho(e,s,p))return;Object.assign(n,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),o.startX=p,o.startY=m,n.touchStartTime=co(),e.allowClick=!0,e.updateSize(),e.swipeDirection=void 0,r.threshold>0&&(n.allowThresholdMove=!1);let f=!0;l.matches(n.focusableElements)&&(f=!1,"SELECT"===l.nodeName&&(n.isTouched=!1)),i.activeElement&&i.activeElement.matches(n.focusableElements)&&i.activeElement!==l&&i.activeElement.blur();const v=f&&e.allowTouchMove&&r.touchStartPreventDefault;!r.touchStartForcePreventDefault&&!v||l.isContentEditable||s.preventDefault(),r.freeMode&&r.freeMode.enabled&&e.freeMode&&e.animating&&!r.cssMode&&e.freeMode.onTouchStart(),e.emit("touchStart",s)}function zo(t){const e=oo(),i=this,s=i.touchEventsData,{params:n,touches:r,rtlTranslate:o,enabled:a}=i;if(!a)return;if(!n.simulateTouch&&"mouse"===t.pointerType)return;let l,d=t;if(d.originalEvent&&(d=d.originalEvent),"pointermove"===d.type){if(null!==s.touchId)return;if(d.pointerId!==s.pointerId)return}if("touchmove"===d.type){if(l=[...d.changedTouches].filter((t=>t.identifier===s.touchId))[0],!l||l.identifier!==s.touchId)return}else l=d;if(!s.isTouched)return void(s.startMoving&&s.isScrolling&&i.emit("touchMoveOpposite",d));const h=l.pageX,c=l.pageY;if(d.preventedByNestedSwiper)return r.startX=h,void(r.startY=c);if(!i.allowTouchMove)return d.target.matches(s.focusableElements)||(i.allowClick=!1),void(s.isTouched&&(Object.assign(r,{startX:h,startY:c,currentX:h,currentY:c}),s.touchStartTime=co()));if(n.touchReleaseOnEdges&&!n.loop)if(i.isVertical()){if(c<r.startY&&i.translate<=i.maxTranslate()||c>r.startY&&i.translate>=i.minTranslate())return s.isTouched=!1,void(s.isMoved=!1)}else if(h<r.startX&&i.translate<=i.maxTranslate()||h>r.startX&&i.translate>=i.minTranslate())return;if(e.activeElement&&d.target===e.activeElement&&d.target.matches(s.focusableElements))return s.isMoved=!0,void(i.allowClick=!1);s.allowTouchCallbacks&&i.emit("touchMove",d),r.previousX=r.currentX,r.previousY=r.currentY,r.currentX=h,r.currentY=c;const u=r.currentX-r.startX,p=r.currentY-r.startY;if(i.params.threshold&&Math.sqrt(u**2+p**2)<i.params.threshold)return;if(void 0===s.isScrolling){let t;i.isHorizontal()&&r.currentY===r.startY||i.isVertical()&&r.currentX===r.startX?s.isScrolling=!1:u*u+p*p>=25&&(t=180*Math.atan2(Math.abs(p),Math.abs(u))/Math.PI,s.isScrolling=i.isHorizontal()?t>n.touchAngle:90-t>n.touchAngle)}if(s.isScrolling&&i.emit("touchMoveOpposite",d),void 0===s.startMoving&&(r.currentX===r.startX&&r.currentY===r.startY||(s.startMoving=!0)),s.isScrolling)return void(s.isTouched=!1);if(!s.startMoving)return;i.allowClick=!1,!n.cssMode&&d.cancelable&&d.preventDefault(),n.touchMoveStopPropagation&&!n.nested&&d.stopPropagation();let m=i.isHorizontal()?u:p,f=i.isHorizontal()?r.currentX-r.previousX:r.currentY-r.previousY;n.oneWayMovement&&(m=Math.abs(m)*(o?1:-1),f=Math.abs(f)*(o?1:-1)),r.diff=m,m*=n.touchRatio,o&&(m=-m,f=-f);const v=i.touchesDirection;i.swipeDirection=m>0?"prev":"next",i.touchesDirection=f>0?"prev":"next";const g=i.params.loop&&!n.cssMode,_="next"===i.touchesDirection&&i.allowSlideNext||"prev"===i.touchesDirection&&i.allowSlidePrev;if(!s.isMoved){if(g&&_&&i.loopFix({direction:i.swipeDirection}),s.startTranslate=i.getTranslate(),i.setTransition(0),i.animating){const t=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});i.wrapperEl.dispatchEvent(t)}s.allowMomentumBounce=!1,!n.grabCursor||!0!==i.allowSlideNext&&!0!==i.allowSlidePrev||i.setGrabCursor(!0),i.emit("sliderFirstMove",d)}if((new Date).getTime(),s.isMoved&&s.allowThresholdMove&&v!==i.touchesDirection&&g&&_&&Math.abs(m)>=1)return Object.assign(r,{startX:h,startY:c,currentX:h,currentY:c,startTranslate:s.currentTranslate}),s.loopSwapReset=!0,void(s.startTranslate=s.currentTranslate);i.emit("sliderMove",d),s.isMoved=!0,s.currentTranslate=m+s.startTranslate;let y=!0,b=n.resistanceRatio;if(n.touchReleaseOnEdges&&(b=0),m>0?(g&&_&&s.allowThresholdMove&&s.currentTranslate>(n.centeredSlides?i.minTranslate()-i.slidesSizesGrid[i.activeIndex+1]:i.minTranslate())&&i.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),s.currentTranslate>i.minTranslate()&&(y=!1,n.resistance&&(s.currentTranslate=i.minTranslate()-1+(-i.minTranslate()+s.startTranslate+m)**b))):m<0&&(g&&_&&s.allowThresholdMove&&s.currentTranslate<(n.centeredSlides?i.maxTranslate()+i.slidesSizesGrid[i.slidesSizesGrid.length-1]:i.maxTranslate())&&i.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:i.slides.length-("auto"===n.slidesPerView?i.slidesPerViewDynamic():Math.ceil(parseFloat(n.slidesPerView,10)))}),s.currentTranslate<i.maxTranslate()&&(y=!1,n.resistance&&(s.currentTranslate=i.maxTranslate()+1-(i.maxTranslate()-s.startTranslate-m)**b))),y&&(d.preventedByNestedSwiper=!0),!i.allowSlideNext&&"next"===i.swipeDirection&&s.currentTranslate<s.startTranslate&&(s.currentTranslate=s.startTranslate),!i.allowSlidePrev&&"prev"===i.swipeDirection&&s.currentTranslate>s.startTranslate&&(s.currentTranslate=s.startTranslate),i.allowSlidePrev||i.allowSlideNext||(s.currentTranslate=s.startTranslate),n.threshold>0){if(!(Math.abs(m)>n.threshold||s.allowThresholdMove))return void(s.currentTranslate=s.startTranslate);if(!s.allowThresholdMove)return s.allowThresholdMove=!0,r.startX=r.currentX,r.startY=r.currentY,s.currentTranslate=s.startTranslate,void(r.diff=i.isHorizontal()?r.currentX-r.startX:r.currentY-r.startY)}n.followFinger&&!n.cssMode&&((n.freeMode&&n.freeMode.enabled&&i.freeMode||n.watchSlidesProgress)&&(i.updateActiveIndex(),i.updateSlidesClasses()),n.freeMode&&n.freeMode.enabled&&i.freeMode&&i.freeMode.onTouchMove(),i.updateProgress(s.currentTranslate),i.setTranslate(s.currentTranslate))}function Bo(t){const e=this,i=e.touchEventsData;let s,n=t;n.originalEvent&&(n=n.originalEvent);if("touchend"===n.type||"touchcancel"===n.type){if(s=[...n.changedTouches].filter((t=>t.identifier===i.touchId))[0],!s||s.identifier!==i.touchId)return}else{if(null!==i.touchId)return;if(n.pointerId!==i.pointerId)return;s=n}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(n.type)){if(!(["pointercancel","contextmenu"].includes(n.type)&&(e.browser.isSafari||e.browser.isWebView)))return}i.pointerId=null,i.touchId=null;const{params:r,touches:o,rtlTranslate:a,slidesGrid:l,enabled:d}=e;if(!d)return;if(!r.simulateTouch&&"mouse"===n.pointerType)return;if(i.allowTouchCallbacks&&e.emit("touchEnd",n),i.allowTouchCallbacks=!1,!i.isTouched)return i.isMoved&&r.grabCursor&&e.setGrabCursor(!1),i.isMoved=!1,void(i.startMoving=!1);r.grabCursor&&i.isMoved&&i.isTouched&&(!0===e.allowSlideNext||!0===e.allowSlidePrev)&&e.setGrabCursor(!1);const h=co(),c=h-i.touchStartTime;if(e.allowClick){const t=n.path||n.composedPath&&n.composedPath();e.updateClickedSlide(t&&t[0]||n.target,t),e.emit("tap click",n),c<300&&h-i.lastClickTime<300&&e.emit("doubleTap doubleClick",n)}if(i.lastClickTime=co(),ho((()=>{e.destroyed||(e.allowClick=!0)})),!i.isTouched||!i.isMoved||!e.swipeDirection||0===o.diff&&!i.loopSwapReset||i.currentTranslate===i.startTranslate&&!i.loopSwapReset)return i.isTouched=!1,i.isMoved=!1,void(i.startMoving=!1);let u;if(i.isTouched=!1,i.isMoved=!1,i.startMoving=!1,u=r.followFinger?a?e.translate:-e.translate:-i.currentTranslate,r.cssMode)return;if(r.freeMode&&r.freeMode.enabled)return void e.freeMode.onTouchEnd({currentPos:u});const p=u>=-e.maxTranslate()&&!e.params.loop;let m=0,f=e.slidesSizesGrid[0];for(let t=0;t<l.length;t+=t<r.slidesPerGroupSkip?1:r.slidesPerGroup){const e=t<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;void 0!==l[t+e]?(p||u>=l[t]&&u<l[t+e])&&(m=t,f=l[t+e]-l[t]):(p||u>=l[t])&&(m=t,f=l[l.length-1]-l[l.length-2])}let v=null,g=null;r.rewind&&(e.isBeginning?g=r.virtual&&r.virtual.enabled&&e.virtual?e.virtual.slides.length-1:e.slides.length-1:e.isEnd&&(v=0));const _=(u-l[m])/f,y=m<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;if(c>r.longSwipesMs){if(!r.longSwipes)return void e.slideTo(e.activeIndex);"next"===e.swipeDirection&&(_>=r.longSwipesRatio?e.slideTo(r.rewind&&e.isEnd?v:m+y):e.slideTo(m)),"prev"===e.swipeDirection&&(_>1-r.longSwipesRatio?e.slideTo(m+y):null!==g&&_<0&&Math.abs(_)>r.longSwipesRatio?e.slideTo(g):e.slideTo(m))}else{if(!r.shortSwipes)return void e.slideTo(e.activeIndex);e.navigation&&(n.target===e.navigation.nextEl||n.target===e.navigation.prevEl)?n.target===e.navigation.nextEl?e.slideTo(m+y):e.slideTo(m):("next"===e.swipeDirection&&e.slideTo(null!==v?v:m+y),"prev"===e.swipeDirection&&e.slideTo(null!==g?g:m))}}function Vo(){const t=this,{params:e,el:i}=t;if(i&&0===i.offsetWidth)return;e.breakpoints&&t.setBreakpoint();const{allowSlideNext:s,allowSlidePrev:n,snapGrid:r}=t,o=t.virtual&&t.params.virtual.enabled;t.allowSlideNext=!0,t.allowSlidePrev=!0,t.updateSize(),t.updateSlides(),t.updateSlidesClasses();const a=o&&e.loop;!("auto"===e.slidesPerView||e.slidesPerView>1)||!t.isEnd||t.isBeginning||t.params.centeredSlides||a?t.params.loop&&!o?t.slideToLoop(t.realIndex,0,!1,!0):t.slideTo(t.activeIndex,0,!1,!0):t.slideTo(t.slides.length-1,0,!1,!0),t.autoplay&&t.autoplay.running&&t.autoplay.paused&&(clearTimeout(t.autoplay.resizeTimeout),t.autoplay.resizeTimeout=setTimeout((()=>{t.autoplay&&t.autoplay.running&&t.autoplay.paused&&t.autoplay.resume()}),500)),t.allowSlidePrev=n,t.allowSlideNext=s,t.params.watchOverflow&&r!==t.snapGrid&&t.checkOverflow()}function qo(t){const e=this;e.enabled&&(e.allowClick||(e.params.preventClicks&&t.preventDefault(),e.params.preventClicksPropagation&&e.animating&&(t.stopPropagation(),t.stopImmediatePropagation())))}function Go(){const t=this,{wrapperEl:e,rtlTranslate:i,enabled:s}=t;if(!s)return;let n;t.previousTranslate=t.translate,t.isHorizontal()?t.translate=-e.scrollLeft:t.translate=-e.scrollTop,0===t.translate&&(t.translate=0),t.updateActiveIndex(),t.updateSlidesClasses();const r=t.maxTranslate()-t.minTranslate();n=0===r?0:(t.translate-t.minTranslate())/r,n!==t.progress&&t.updateProgress(i?-t.translate:t.translate),t.emit("setTranslate",t.translate,!1)}function Wo(t){const e=this;No(e,t.target),e.params.cssMode||"auto"!==e.params.slidesPerView&&!e.params.autoHeight||e.update()}function Ko(){const t=this;t.documentTouchHandlerProceeded||(t.documentTouchHandlerProceeded=!0,t.params.touchReleaseOnEdges&&(t.el.style.touchAction="auto"))}const jo=(t,e)=>{const i=oo(),{params:s,el:n,wrapperEl:r,device:o}=t,a=!!s.nested,l="on"===e?"addEventListener":"removeEventListener",d=e;i[l]("touchstart",t.onDocumentTouchStart,{passive:!1,capture:a}),n[l]("touchstart",t.onTouchStart,{passive:!1}),n[l]("pointerdown",t.onTouchStart,{passive:!1}),i[l]("touchmove",t.onTouchMove,{passive:!1,capture:a}),i[l]("pointermove",t.onTouchMove,{passive:!1,capture:a}),i[l]("touchend",t.onTouchEnd,{passive:!0}),i[l]("pointerup",t.onTouchEnd,{passive:!0}),i[l]("pointercancel",t.onTouchEnd,{passive:!0}),i[l]("touchcancel",t.onTouchEnd,{passive:!0}),i[l]("pointerout",t.onTouchEnd,{passive:!0}),i[l]("pointerleave",t.onTouchEnd,{passive:!0}),i[l]("contextmenu",t.onTouchEnd,{passive:!0}),(s.preventClicks||s.preventClicksPropagation)&&n[l]("click",t.onClick,!0),s.cssMode&&r[l]("scroll",t.onScroll),s.updateOnWindowResize?t[d](o.ios||o.android?"resize orientationchange observerUpdate":"resize observerUpdate",Vo,!0):t[d]("observerUpdate",Vo,!0),n[l]("load",t.onLoad,{capture:!0})};const Yo=(t,e)=>t.grid&&e.grid&&e.grid.rows>1;var Zo={setBreakpoint:function(){const t=this,{realIndex:e,initialized:i,params:s,el:n}=t,r=s.breakpoints;if(!r||r&&0===Object.keys(r).length)return;const o=t.getBreakpoint(r,t.params.breakpointsBase,t.el);if(!o||t.currentBreakpoint===o)return;const a=(o in r?r[o]:void 0)||t.originalParams,l=Yo(t,s),d=Yo(t,a),h=s.enabled;l&&!d?(n.classList.remove(`${s.containerModifierClass}grid`,`${s.containerModifierClass}grid-column`),t.emitContainerClasses()):!l&&d&&(n.classList.add(`${s.containerModifierClass}grid`),(a.grid.fill&&"column"===a.grid.fill||!a.grid.fill&&"column"===s.grid.fill)&&n.classList.add(`${s.containerModifierClass}grid-column`),t.emitContainerClasses()),["navigation","pagination","scrollbar"].forEach((e=>{if(void 0===a[e])return;const i=s[e]&&s[e].enabled,n=a[e]&&a[e].enabled;i&&!n&&t[e].disable(),!i&&n&&t[e].enable()}));const c=a.direction&&a.direction!==s.direction,u=s.loop&&(a.slidesPerView!==s.slidesPerView||c),p=s.loop;c&&i&&t.changeDirection(),fo(t.params,a);const m=t.params.enabled,f=t.params.loop;Object.assign(t,{allowTouchMove:t.params.allowTouchMove,allowSlideNext:t.params.allowSlideNext,allowSlidePrev:t.params.allowSlidePrev}),h&&!m?t.disable():!h&&m&&t.enable(),t.currentBreakpoint=o,t.emit("_beforeBreakpoint",a),i&&(u?(t.loopDestroy(),t.loopCreate(e),t.updateSlides()):!p&&f?(t.loopCreate(e),t.updateSlides()):p&&!f&&t.loopDestroy()),t.emit("breakpoint",a)},getBreakpoint:function(t,e,i){if(void 0===e&&(e="window"),!t||"container"===e&&!i)return;let s=!1;const n=lo(),r="window"===e?n.innerHeight:i.clientHeight,o=Object.keys(t).map((t=>{if("string"==typeof t&&0===t.indexOf("@")){const e=parseFloat(t.substr(1));return{value:r*e,point:t}}return{value:t,point:t}}));o.sort(((t,e)=>parseInt(t.value,10)-parseInt(e.value,10)));for(let t=0;t<o.length;t+=1){const{point:r,value:a}=o[t];"window"===e?n.matchMedia(`(min-width: ${a}px)`).matches&&(s=r):a<=i.clientWidth&&(s=r)}return s||"max"}};var Xo={addClasses:function(){const t=this,{classNames:e,params:i,rtl:s,el:n,device:r}=t,o=function(t,e){const i=[];return t.forEach((t=>{"object"==typeof t?Object.keys(t).forEach((s=>{t[s]&&i.push(e+s)})):"string"==typeof t&&i.push(e+t)})),i}(["initialized",i.direction,{"free-mode":t.params.freeMode&&i.freeMode.enabled},{autoheight:i.autoHeight},{rtl:s},{grid:i.grid&&i.grid.rows>1},{"grid-column":i.grid&&i.grid.rows>1&&"column"===i.grid.fill},{android:r.android},{ios:r.ios},{"css-mode":i.cssMode},{centered:i.cssMode&&i.centeredSlides},{"watch-progress":i.watchSlidesProgress}],i.containerModifierClass);e.push(...o),n.classList.add(...e),t.emitContainerClasses()},removeClasses:function(){const{el:t,classNames:e}=this;t.classList.remove(...e),this.emitContainerClasses()}};var Jo={init:!0,direction:"horizontal",oneWayMovement:!1,touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function Qo(t,e){return function(i){void 0===i&&(i={});const s=Object.keys(i)[0],n=i[s];"object"==typeof n&&null!==n?(!0===t[s]&&(t[s]={enabled:!0}),"navigation"===s&&t[s]&&t[s].enabled&&!t[s].prevEl&&!t[s].nextEl&&(t[s].auto=!0),["pagination","scrollbar"].indexOf(s)>=0&&t[s]&&t[s].enabled&&!t[s].el&&(t[s].auto=!0),s in t&&"enabled"in n?("object"!=typeof t[s]||"enabled"in t[s]||(t[s].enabled=!0),t[s]||(t[s]={enabled:!1}),fo(e,i)):fo(e,i)):fo(e,i)}}const ta={eventsEmitter:Po,update:ko,translate:Do,transition:{setTransition:function(t,e){const i=this;i.params.cssMode||(i.wrapperEl.style.transitionDuration=`${t}ms`,i.wrapperEl.style.transitionDelay=0===t?"0ms":""),i.emit("setTransition",t,e)},transitionStart:function(t,e){void 0===t&&(t=!0);const i=this,{params:s}=i;s.cssMode||(s.autoHeight&&i.updateAutoHeight(),Ro({swiper:i,runCallbacks:t,direction:e,step:"Start"}))},transitionEnd:function(t,e){void 0===t&&(t=!0);const i=this,{params:s}=i;i.animating=!1,s.cssMode||(i.setTransition(0),Ro({swiper:i,runCallbacks:t,direction:e,step:"End"}))}},slide:$o,loop:Fo,grabCursor:{setGrabCursor:function(t){const e=this;if(!e.params.simulateTouch||e.params.watchOverflow&&e.isLocked||e.params.cssMode)return;const i="container"===e.params.touchEventsTarget?e.el:e.wrapperEl;e.isElement&&(e.__preventObserver__=!0),i.style.cursor="move",i.style.cursor=t?"grabbing":"grab",e.isElement&&requestAnimationFrame((()=>{e.__preventObserver__=!1}))},unsetGrabCursor:function(){const t=this;t.params.watchOverflow&&t.isLocked||t.params.cssMode||(t.isElement&&(t.__preventObserver__=!0),t["container"===t.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",t.isElement&&requestAnimationFrame((()=>{t.__preventObserver__=!1})))}},events:{attachEvents:function(){const t=this,{params:e}=t;t.onTouchStart=Uo.bind(t),t.onTouchMove=zo.bind(t),t.onTouchEnd=Bo.bind(t),t.onDocumentTouchStart=Ko.bind(t),e.cssMode&&(t.onScroll=Go.bind(t)),t.onClick=qo.bind(t),t.onLoad=Wo.bind(t),jo(t,"on")},detachEvents:function(){jo(this,"off")}},breakpoints:Zo,checkOverflow:{checkOverflow:function(){const t=this,{isLocked:e,params:i}=t,{slidesOffsetBefore:s}=i;if(s){const e=t.slides.length-1,i=t.slidesGrid[e]+t.slidesSizesGrid[e]+2*s;t.isLocked=t.size>i}else t.isLocked=1===t.snapGrid.length;!0===i.allowSlideNext&&(t.allowSlideNext=!t.isLocked),!0===i.allowSlidePrev&&(t.allowSlidePrev=!t.isLocked),e&&e!==t.isLocked&&(t.isEnd=!1),e!==t.isLocked&&t.emit(t.isLocked?"lock":"unlock")}},classes:Xo},ea={};class ia{constructor(){let t,e;for(var i=arguments.length,s=new Array(i),n=0;n<i;n++)s[n]=arguments[n];1===s.length&&s[0].constructor&&"Object"===Object.prototype.toString.call(s[0]).slice(8,-1)?e=s[0]:[t,e]=s,e||(e={}),e=fo({},e),t&&!e.el&&(e.el=t);const r=oo();if(e.el&&"string"==typeof e.el&&r.querySelectorAll(e.el).length>1){const t=[];return r.querySelectorAll(e.el).forEach((i=>{const s=fo({},e,{el:i});t.push(new ia(s))})),t}const o=this;o.__swiper__=!0,o.support=Oo(),o.device=Co({userAgent:e.userAgent}),o.browser=Io(),o.eventsListeners={},o.eventsAnyListeners=[],o.modules=[...o.__modules__],e.modules&&Array.isArray(e.modules)&&o.modules.push(...e.modules);const a={};o.modules.forEach((t=>{t({params:e,swiper:o,extendParams:Qo(e,a),on:o.on.bind(o),once:o.once.bind(o),off:o.off.bind(o),emit:o.emit.bind(o)})}));const l=fo({},Jo,a);return o.params=fo({},l,ea,e),o.originalParams=fo({},o.params),o.passedParams=fo({},e),o.params&&o.params.on&&Object.keys(o.params.on).forEach((t=>{o.on(t,o.params.on[t])})),o.params&&o.params.onAny&&o.onAny(o.params.onAny),Object.assign(o,{enabled:o.params.enabled,el:t,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return"horizontal"===o.params.direction},isVertical(){return"vertical"===o.params.direction},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:o.params.allowSlideNext,allowSlidePrev:o.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:o.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:o.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),o.emit("_swiper"),o.params.init&&o.init(),o}getDirectionLabel(t){return this.isHorizontal()?t:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[t]}getSlideIndex(t){const{slidesEl:e,params:i}=this,s=wo(_o(e,`.${i.slideClass}, swiper-slide`)[0]);return wo(t)-s}getSlideIndexByData(t){return this.getSlideIndex(this.slides.filter((e=>1*e.getAttribute("data-swiper-slide-index")===t))[0])}recalcSlides(){const{slidesEl:t,params:e}=this;this.slides=_o(t,`.${e.slideClass}, swiper-slide`)}enable(){const t=this;t.enabled||(t.enabled=!0,t.params.grabCursor&&t.setGrabCursor(),t.emit("enable"))}disable(){const t=this;t.enabled&&(t.enabled=!1,t.params.grabCursor&&t.unsetGrabCursor(),t.emit("disable"))}setProgress(t,e){const i=this;t=Math.min(Math.max(t,0),1);const s=i.minTranslate(),n=(i.maxTranslate()-s)*t+s;i.translateTo(n,void 0===e?0:e),i.updateActiveIndex(),i.updateSlidesClasses()}emitContainerClasses(){const t=this;if(!t.params._emitClasses||!t.el)return;const e=t.el.className.split(" ").filter((e=>0===e.indexOf("swiper")||0===e.indexOf(t.params.containerModifierClass)));t.emit("_containerClasses",e.join(" "))}getSlideClasses(t){const e=this;return e.destroyed?"":t.className.split(" ").filter((t=>0===t.indexOf("swiper-slide")||0===t.indexOf(e.params.slideClass))).join(" ")}emitSlidesClasses(){const t=this;if(!t.params._emitClasses||!t.el)return;const e=[];t.slides.forEach((i=>{const s=t.getSlideClasses(i);e.push({slideEl:i,classNames:s}),t.emit("_slideClass",i,s)})),t.emit("_slideClasses",e)}slidesPerViewDynamic(t,e){void 0===t&&(t="current"),void 0===e&&(e=!1);const{params:i,slides:s,slidesGrid:n,slidesSizesGrid:r,size:o,activeIndex:a}=this;let l=1;if("number"==typeof i.slidesPerView)return i.slidesPerView;if(i.centeredSlides){let t,e=s[a]?s[a].swiperSlideSize:0;for(let i=a+1;i<s.length;i+=1)s[i]&&!t&&(e+=s[i].swiperSlideSize,l+=1,e>o&&(t=!0));for(let i=a-1;i>=0;i-=1)s[i]&&!t&&(e+=s[i].swiperSlideSize,l+=1,e>o&&(t=!0))}else if("current"===t)for(let t=a+1;t<s.length;t+=1){(e?n[t]+r[t]-n[a]<o:n[t]-n[a]<o)&&(l+=1)}else for(let t=a-1;t>=0;t-=1){n[a]-n[t]<o&&(l+=1)}return l}update(){const t=this;if(!t||t.destroyed)return;const{snapGrid:e,params:i}=t;function s(){const e=t.rtlTranslate?-1*t.translate:t.translate,i=Math.min(Math.max(e,t.maxTranslate()),t.minTranslate());t.setTranslate(i),t.updateActiveIndex(),t.updateSlidesClasses()}let n;if(i.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach((e=>{e.complete&&No(t,e)})),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),i.freeMode&&i.freeMode.enabled&&!i.cssMode)s(),i.autoHeight&&t.updateAutoHeight();else{if(("auto"===i.slidesPerView||i.slidesPerView>1)&&t.isEnd&&!i.centeredSlides){const e=t.virtual&&i.virtual.enabled?t.virtual.slides:t.slides;n=t.slideTo(e.length-1,0,!1,!0)}else n=t.slideTo(t.activeIndex,0,!1,!0);n||s()}i.watchOverflow&&e!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(t,e){void 0===e&&(e=!0);const i=this,s=i.params.direction;return t||(t="horizontal"===s?"vertical":"horizontal"),t===s||"horizontal"!==t&&"vertical"!==t||(i.el.classList.remove(`${i.params.containerModifierClass}${s}`),i.el.classList.add(`${i.params.containerModifierClass}${t}`),i.emitContainerClasses(),i.params.direction=t,i.slides.forEach((e=>{"vertical"===t?e.style.width="":e.style.height=""})),i.emit("changeDirection"),e&&i.update()),i}changeLanguageDirection(t){const e=this;e.rtl&&"rtl"===t||!e.rtl&&"ltr"===t||(e.rtl="rtl"===t,e.rtlTranslate="horizontal"===e.params.direction&&e.rtl,e.rtl?(e.el.classList.add(`${e.params.containerModifierClass}rtl`),e.el.dir="rtl"):(e.el.classList.remove(`${e.params.containerModifierClass}rtl`),e.el.dir="ltr"),e.update())}mount(t){const e=this;if(e.mounted)return!0;let i=t||e.params.el;if("string"==typeof i&&(i=document.querySelector(i)),!i)return!1;i.swiper=e,i.parentNode&&i.parentNode.host&&"SWIPER-CONTAINER"===i.parentNode.host.nodeName&&(e.isElement=!0);const s=()=>`.${(e.params.wrapperClass||"").trim().split(" ").join(".")}`;let n=(()=>{if(i&&i.shadowRoot&&i.shadowRoot.querySelector){return i.shadowRoot.querySelector(s())}return _o(i,s())[0]})();return!n&&e.params.createElements&&(n=bo("div",e.params.wrapperClass),i.append(n),_o(i,`.${e.params.slideClass}`).forEach((t=>{n.append(t)}))),Object.assign(e,{el:i,wrapperEl:n,slidesEl:e.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:n,hostEl:e.isElement?i.parentNode.host:i,mounted:!0,rtl:"rtl"===i.dir.toLowerCase()||"rtl"===Eo(i,"direction"),rtlTranslate:"horizontal"===e.params.direction&&("rtl"===i.dir.toLowerCase()||"rtl"===Eo(i,"direction")),wrongRTL:"-webkit-box"===Eo(n,"display")}),!0}init(t){const e=this;if(e.initialized)return e;if(!1===e.mount(t))return e;e.emit("beforeInit"),e.params.breakpoints&&e.setBreakpoint(),e.addClasses(),e.updateSize(),e.updateSlides(),e.params.watchOverflow&&e.checkOverflow(),e.params.grabCursor&&e.enabled&&e.setGrabCursor(),e.params.loop&&e.virtual&&e.params.virtual.enabled?e.slideTo(e.params.initialSlide+e.virtual.slidesBefore,0,e.params.runCallbacksOnInit,!1,!0):e.slideTo(e.params.initialSlide,0,e.params.runCallbacksOnInit,!1,!0),e.params.loop&&e.loopCreate(),e.attachEvents();const i=[...e.el.querySelectorAll('[loading="lazy"]')];return e.isElement&&i.push(...e.hostEl.querySelectorAll('[loading="lazy"]')),i.forEach((t=>{t.complete?No(e,t):t.addEventListener("load",(t=>{No(e,t.target)}))})),Mo(e),e.initialized=!0,Mo(e),e.emit("init"),e.emit("afterInit"),e}destroy(t,e){void 0===t&&(t=!0),void 0===e&&(e=!0);const i=this,{params:s,el:n,wrapperEl:r,slides:o}=i;return void 0===i.params||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),s.loop&&i.loopDestroy(),e&&(i.removeClasses(),n.removeAttribute("style"),r.removeAttribute("style"),o&&o.length&&o.forEach((t=>{t.classList.remove(s.slideVisibleClass,s.slideFullyVisibleClass,s.slideActiveClass,s.slideNextClass,s.slidePrevClass),t.removeAttribute("style"),t.removeAttribute("data-swiper-slide-index")}))),i.emit("destroy"),Object.keys(i.eventsListeners).forEach((t=>{i.off(t)})),!1!==t&&(i.el.swiper=null,function(t){const e=t;Object.keys(e).forEach((t=>{try{e[t]=null}catch(t){}try{delete e[t]}catch(t){}}))}(i)),i.destroyed=!0),null}static extendDefaults(t){fo(ea,t)}static get extendedDefaults(){return ea}static get defaults(){return Jo}static installModule(t){ia.prototype.__modules__||(ia.prototype.__modules__=[]);const e=ia.prototype.__modules__;"function"==typeof t&&e.indexOf(t)<0&&e.push(t)}static use(t){return Array.isArray(t)?(t.forEach((t=>ia.installModule(t))),ia):(ia.installModule(t),ia)}}function sa(t){let{swiper:e,extendParams:i,on:s,emit:n}=t;i({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),e.navigation={nextEl:null,prevEl:null};const r=t=>(Array.isArray(t)?t:[t]).filter((t=>!!t));function o(t){let i;return t&&"string"==typeof t&&e.isElement&&(i=e.el.querySelector(t),i)?i:(t&&("string"==typeof t&&(i=[...document.querySelectorAll(t)]),e.params.uniqueNavElements&&"string"==typeof t&&i.length>1&&1===e.el.querySelectorAll(t).length&&(i=e.el.querySelector(t))),t&&!i?t:i)}function a(t,i){const s=e.params.navigation;(t=r(t)).forEach((t=>{t&&(t.classList[i?"add":"remove"](...s.disabledClass.split(" ")),"BUTTON"===t.tagName&&(t.disabled=i),e.params.watchOverflow&&e.enabled&&t.classList[e.isLocked?"add":"remove"](s.lockClass))}))}function l(){const{nextEl:t,prevEl:i}=e.navigation;if(e.params.loop)return a(i,!1),void a(t,!1);a(i,e.isBeginning&&!e.params.rewind),a(t,e.isEnd&&!e.params.rewind)}function d(t){t.preventDefault(),(!e.isBeginning||e.params.loop||e.params.rewind)&&(e.slidePrev(),n("navigationPrev"))}function h(t){t.preventDefault(),(!e.isEnd||e.params.loop||e.params.rewind)&&(e.slideNext(),n("navigationNext"))}function c(){const t=e.params.navigation;if(e.params.navigation=function(t,e,i,s){return t.params.createElements&&Object.keys(s).forEach((n=>{if(!i[n]&&!0===i.auto){let r=_o(t.el,`.${s[n]}`)[0];r||(r=bo("div",s[n]),r.className=s[n],t.el.append(r)),i[n]=r,e[n]=r}})),i}(e,e.originalParams.navigation,e.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!t.nextEl&&!t.prevEl)return;let i=o(t.nextEl),s=o(t.prevEl);Object.assign(e.navigation,{nextEl:i,prevEl:s}),i=r(i),s=r(s);const n=(i,s)=>{i&&i.addEventListener("click","next"===s?h:d),!e.enabled&&i&&i.classList.add(...t.lockClass.split(" "))};i.forEach((t=>n(t,"next"))),s.forEach((t=>n(t,"prev")))}function u(){let{nextEl:t,prevEl:i}=e.navigation;t=r(t),i=r(i);const s=(t,i)=>{t.removeEventListener("click","next"===i?h:d),t.classList.remove(...e.params.navigation.disabledClass.split(" "))};t.forEach((t=>s(t,"next"))),i.forEach((t=>s(t,"prev")))}s("init",(()=>{!1===e.params.navigation.enabled?p():(c(),l())})),s("toEdge fromEdge lock unlock",(()=>{l()})),s("destroy",(()=>{u()})),s("enable disable",(()=>{let{nextEl:t,prevEl:i}=e.navigation;t=r(t),i=r(i),e.enabled?l():[...t,...i].filter((t=>!!t)).forEach((t=>t.classList.add(e.params.navigation.lockClass)))})),s("click",((t,i)=>{let{nextEl:s,prevEl:o}=e.navigation;s=r(s),o=r(o);const a=i.target;if(e.params.navigation.hideOnClick&&!o.includes(a)&&!s.includes(a)){if(e.pagination&&e.params.pagination&&e.params.pagination.clickable&&(e.pagination.el===a||e.pagination.el.contains(a)))return;let t;s.length?t=s[0].classList.contains(e.params.navigation.hiddenClass):o.length&&(t=o[0].classList.contains(e.params.navigation.hiddenClass)),n(!0===t?"navigationShow":"navigationHide"),[...s,...o].filter((t=>!!t)).forEach((t=>t.classList.toggle(e.params.navigation.hiddenClass)))}}));const p=()=>{e.el.classList.add(...e.params.navigation.navigationDisabledClass.split(" ")),u()};Object.assign(e.navigation,{enable:()=>{e.el.classList.remove(...e.params.navigation.navigationDisabledClass.split(" ")),c(),l()},disable:p,update:l,init:c,destroy:u})}Object.keys(ta).forEach((t=>{Object.keys(ta[t]).forEach((e=>{ia.prototype[e]=ta[t][e]}))})),ia.use([function(t){let{swiper:e,on:i,emit:s}=t;const n=lo();let r=null,o=null;const a=()=>{e&&!e.destroyed&&e.initialized&&(s("beforeResize"),s("resize"))},l=()=>{e&&!e.destroyed&&e.initialized&&s("orientationchange")};i("init",(()=>{e.params.resizeObserver&&void 0!==n.ResizeObserver?e&&!e.destroyed&&e.initialized&&(r=new ResizeObserver((t=>{o=n.requestAnimationFrame((()=>{const{width:i,height:s}=e;let n=i,r=s;t.forEach((t=>{let{contentBoxSize:i,contentRect:s,target:o}=t;o&&o!==e.el||(n=s?s.width:(i[0]||i).inlineSize,r=s?s.height:(i[0]||i).blockSize)})),n===i&&r===s||a()}))})),r.observe(e.el)):(n.addEventListener("resize",a),n.addEventListener("orientationchange",l))})),i("destroy",(()=>{o&&n.cancelAnimationFrame(o),r&&r.unobserve&&e.el&&(r.unobserve(e.el),r=null),n.removeEventListener("resize",a),n.removeEventListener("orientationchange",l)}))},function(t){let{swiper:e,extendParams:i,on:s,emit:n}=t;const r=[],o=lo(),a=function(t,i){void 0===i&&(i={});const s=new(o.MutationObserver||o.WebkitMutationObserver)((t=>{if(e.__preventObserver__)return;if(1===t.length)return void n("observerUpdate",t[0]);const i=function(){n("observerUpdate",t[0])};o.requestAnimationFrame?o.requestAnimationFrame(i):o.setTimeout(i,0)}));s.observe(t,{attributes:void 0===i.attributes||i.attributes,childList:void 0===i.childList||i.childList,characterData:void 0===i.characterData||i.characterData}),r.push(s)};i({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",(()=>{if(e.params.observer){if(e.params.observeParents){const t=function(t,e){const i=[];let s=t.parentElement;for(;s;)e?s.matches(e)&&i.push(s):i.push(s),s=s.parentElement;return i}(e.hostEl);for(let e=0;e<t.length;e+=1)a(t[e])}a(e.hostEl,{childList:e.params.observeSlideChildren}),a(e.wrapperEl,{attributes:!1})}})),s("destroy",(()=>{r.forEach((t=>{t.disconnect()})),r.splice(0,r.length)}))}]);var na={Accordion:Fe,ActionNetworkEmbed:class extends Ie{constructor(t){super(),this.element=t,this.init()}init(){return this.cacheDomReferences().setupHandlers().enable(),this}cacheDomReferences(){return this.formUrl=this.element.dataset.formUrl,this.mobileDisclaimerLanguage=this.element.querySelector(".actionnetworkform__mobile-disclaimer"),this}setupHandlers(){this.updateMobileDisclaimer=this.updateMobileDisclaimer.bind(this);const t=document.createElement("script");return t.src=this.formUrl,t.async=!0,document.body.appendChild(t),this}enable(){return document.addEventListener("can_embed_loaded",this.updateMobileDisclaimer,!0),this}updateMobileDisclaimer(){const t=this.element.querySelector("#mobile_opt_in_default");return t&&(t.innerHTML=this.mobileDisclaimerLanguage?this.mobileDisclaimerLanguage.innerHTML:'By checking this box you consent to receive recurring text message updates from Vote Save America, including by automated text messages. Txt HELP for help, STOP to end. Msg & Data rates may apply. Privacy policy and terms of service: <a target="_blank" href="https://votesaveamerica.com/privacy-policy">https://votesaveamerica.com/privacy-policy</a>'),this}},CongressionalLookup:He,Countdown:class extends Ie{constructor(t){super(),this.element=t,this.init()}init(){return this.cacheDomReferences().setupHandlers().enable(),this}cacheDomReferences(){return this.dateSpan=this.element.querySelector(".featuredcountdown__date"),this.todaySpan=this.element.querySelector(".featuredcountdown__today"),this.beforeSpan=this.element.querySelector(".featuredcountdown__before"),this.interval=1e3,this.dateSpan&&(this.date=this.dateSpan.dataset.countdownDate,this.precision=this.dateSpan.dataset.countdownPrecision,this.year=this.date.substring(0,4),this.month=this.date.substring(4,6),this.day=this.date.substring(6,8),this.countdownDate=new Date(this.year,this.month-1,this.day)),this.todaySpan&&this.todaySpan.classList.add("hidden"),"minute"===this.precision&&(this.interval=6e4),"hour"===this.precision&&(this.interval=36e5),this}enable(){return this.date&&(this.countdownTimer=setInterval(this.countdownUpdater,this.interval)),this}setupHandlers(){return this.countdownUpdater=this.countdownUpdater.bind(this),this}countdownUpdater(){const t=(new Date).getTime(),e=this.countdownDate-t,i=Math.floor(e/864e5),s=Math.floor(e%864e5/36e5),n=Math.floor(e%36e5/6e4),r=Math.floor(e%6e4/1e3);this.dateSpan.innerHTML=`${i} ${1===i?"day":"days"}${"second"===this.precision||"minute"===this.precision||"hour"===this.precision?`, ${s} ${1===s?"hour":"hours"}`:""}${"second"===this.precision||"minute"===this.precision?`, ${n} ${1===n?"minute":"minutes"}`:""}${"second"===this.precision?`, ${r} ${1===r?"second":"seconds"}`:""}`,e<0?(this.dateSpan.classList.add("hidden"),this.beforeSpan.classList.add("hidden"),e>-864e5?(this.element.classList.remove("hidden"),this.todaySpan.classList.remove("hidden")):(clearInterval(this.countdownTimer),this.todaySpan.classList.add("hidden"),this.element.classList.add("hidden"))):this.element.classList.remove("hidden")}},DonateGoalTracker:class extends Ie{constructor(t){super(),this.element=t,this.init()}init(){return this.enable(),this}enable(){if(this.timestamp=this.element.dataset.lastUpdated,this.timePlace=this.element.querySelector(".donategoaltracker__timeplace"),this.timePlace&&this.timestamp){const t=new Date(parseInt(this.timestamp,10));this.timePlace.innerHTML=t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}return this}},Dropdown:class extends Ie{constructor(t){super(),this.element=t,this.init()}init(){return this.cacheDomReferences().setupHandlers().enable(),this}cacheDomReferences(){return this.isDropDownOpen=!0,this.debounce_timeout_ms=100,this.currentListItemFocused=-1,this.bounce=void 0,this.states=[],this.statesKeys={},this.input=this.element.querySelector(".autocomplete__input"),this.resultsList=this.element.querySelector(".autocomplete__results"),this.dropdownArrow=this.element.querySelector(".autocomplete__dropdown-arrow"),this.comboBox=this.element.querySelector(".autocomplete__container"),this.statesDataField=this.element.dataset.stateField,this.statesData=window[this.statesDataField]||[],this.statesData.length>0&&this.statesData.forEach((t=>{this.statesKeys[t.name]=t.slug,this.states.push(t.name)})),this.filteredResults=[...this.states],this}setupHandlers(){return this.openDropdown=this.openDropdown.bind(this),this.closeDropdown=this.closeDropdown.bind(this),this.outsideClickListener=this.outsideClickListener.bind(this),this.filter=this.filter.bind(this),this.debounce=this.debounce.bind(this),this.setResults=this.setResults.bind(this),this.handleKeyboardEvents=this.handleKeyboardEvents.bind(this),this.focusListItem=this.focusListItem.bind(this),this.selectValue=this.selectValue.bind(this),this}enable(){return this.setResults(this.states),this.input.addEventListener("keydown",this.handleKeyboardEvents),this.resultsList.addEventListener("keydown",this.handleKeyboardEvents),this.input.addEventListener("click",this.openDropdown),document.addEventListener("click",this.outsideClickListener),this.input.addEventListener("input",(t=>{const{value:e}=t.target;this.debounce((()=>{this.filter(e),this.isDropDownOpen||this.openDropdown()}))})),this.resultsList.addEventListener("click",(t=>{([...this.resultsList.childNodes].includes(t.target)||[...this.resultsList.childNodes].includes(t.target.parentNode))&&this.selectValue(t.target)})),this.dropdownArrow.addEventListener("click",(t=>{t.preventDefault(),this.isDropDownOpen?this.closeDropdown():this.openDropdown()})),this}closeDropdownFromOutside(t){return bt(t.target,yt.DROPDOWN)!==this.element&&this.closeDropdown(),this}closeDropdown(){return this.isDropDownOpen=!1,this.resultsList.classList.remove("visible"),this.dropdownArrow.classList.remove("expanded"),this.comboBox.setAttribute("aria-expanded","false"),this.input.setAttribute("aria-activedescendant",""),this}openDropdown(){return this.isDropDownOpen=!0,this.resultsList.classList.add("visible"),this.dropdownArrow.classList.add("expanded"),this.comboBox.setAttribute("aria-expanded","true"),this}outsideClickListener(t){[this.input,this.dropdownArrow,...this.resultsList.childNodes].includes(t.target)||this.closeDropdown()}filter(t){if(t){const e=new RegExp(`${t}.*`,"gi");this.filteredResults=this.states.filter((t=>e.test(t)))}else this.filteredResults=[...this.states];this.setResults(this.filteredResults)}setResults(t){if(Array.isArray(t)&&t.length>0){const e=t.map(((t,e)=>`<li class="autocomplete-item" id="autocomplete-item-${e}" role="listitem" tabindex="0"><span>${t}</span></li>`)).join("");this.resultsList.innerHTML=e,this.currentListItemFocused=-1}}debounce(t){clearTimeout(this.bounce),this.bounce=setTimeout((()=>{t()}),[this.debounce_timeout_ms])}focusListItem(t){const{id:e}=t;this.input.setAttribute("aria-activedescendant",e),t.focus()}selectValue(t){const e=t.innerText,i=this.statesKeys[e],s=new URLSearchParams(window.location.search);this.input.value=e,this.input.removeAttribute("aria-activedescendant"),t.setAttribute("aria-selected","true"),this.input.focus(),this.closeDropdown(),e&&i&&(s.set("state",i.toUpperCase()),window.history.pushState(null,null,`?${s.toString()}`))}handleKeyboardEvents(t){const e=this.resultsList.childNodes;let i=null;switch(["ArrowUp","ArrowDown","Enter"].includes(t.key)&&t.preventDefault(),t.key){case"ArrowDown":this.currentListItemFocused<e.length-1&&(this.isDropDownOpen||this.openDropdown(),this.currentListItemFocused+=1,i=e.item(this.currentListItemFocused),this.focusListItem(i));break;case"ArrowUp":this.currentListItemFocused>0&&(this.currentListItemFocused-=1,i=e.item(this.currentListItemFocused),this.focusListItem(i));break;case"Home":this.currentListItemFocused>0&&(this.currentListItemFocused=0,i=e.item(this.currentListItemFocused),this.focusListItem(i));break;case"End":this.currentListItemFocused<e.length-1&&(this.currentListItemFocused=e.length-1,i=e.item(this.currentListItemFocused),this.focusListItem(i));break;case"Enter":this.isDropDownOpen?e[this.currentListItemFocused].innerText&&this.selectValue(e[this.currentListItemFocused]):this.openDropdown();break;case"Escape":this.isDropDownOpen&&this.closeDropdown();break;default:t.target!==this.input&&/([a-zA-Z0-9_]|ArrowLeft|ArrowRight)/.test(t.key)&&this.input.focus()}}},FindYourTeam:class extends Ie{constructor(t){super(),this.element=t,this.init()}init(){return this.cacheDomReferences().setupHandlers().enable(),this}cacheDomReferences(){return this.isDropDownOpen=!1,this.debounce_timeout_ms=100,this.currentListItemFocused=-1,this.bounce=void 0,this.states=[],this.statesKeys={},this.statesNames={},this.statesTeam={},this.statesType={},this.stateIcon="",this.part1=this.element.querySelector(yt.FIND_YOUR_TEAM_TITLE_PART_ONE),this.part2=this.element.querySelector(yt.FIND_YOUR_TEAM_TITLE_PART_TWO),this.statLists=document.querySelectorAll(".winstatlistsection"),this.teamTargets=document.querySelectorAll(".teamtargetlist"),this.teamOverviews=this.element.querySelectorAll(".findyourteam__teamoverview"),this.allFeaturedActions=this.element.querySelector(".allfeaturedactions"),this.limitedFeaturedActions=this.element.querySelector(".limitedfeaturedactions"),this.limitedFeaturedActionsStates=["wi","mi","az","nv","nc","ga","pa"],this.formSourceFields=document.querySelectorAll('input[name="default_source"]'),this.orginalFormSourceValues=[],this.formSourceFields.length>0&&this.formSourceFields.forEach((t=>{this.orginalFormSourceValues.push(t.value)})),this.teamIcons=this.element.querySelectorAll(".findyourteam__teamicon"),this.teamVs=this.element.querySelectorAll(".findyourteam__vs"),this.teamStars=this.element.querySelector(".findyourteam__stars--part2"),this.teamIconsList=this.element.querySelector(".findyourteam__teamicons"),this.headingState=this.element.querySelector(yt.FIND_YOUR_TEAM_HEADING_STATE),this.headingStateText=this.headingState.dataset.stateText,this.restart=this.element.querySelector(yt.FIND_YOUR_TEAM_RESTART_LINK),this.restartStateText=this.restart.dataset.stateText,this.restartLocationText=this.restart.dataset.locationText,this.dropdown=this.element.querySelector(yt.STATE_SELECTOR),this.select=this.element.querySelector(yt.STATE_SELECTOR_SELECT),this.input=this.element.querySelector(yt.AUTOCOMPLETE_INPUT),this.resultsList=this.element.querySelector(yt.AUTOCOMPLETE_RESULTS),this.dropdownArrow=this.element.querySelector(yt.AUTOCOMPLETE_DROPDOWN_ARROW),this.comboBox=this.element.querySelector(yt.AUTOCOMPLETE_CONTAINER),this.statesDataField=this.dropdown.dataset.stateField,this.queryString=window.location.search,this.queryParams=new URLSearchParams(this.queryString),this.state=(this.queryParams.get(gt.STATE_PARAM)||"").toLowerCase(),this.state||(this.state=(this.element.dataset.geoState||"").toLowerCase()),this.source=this.queryParams.get("source"),this.utmSource=this.queryParams.get("utm_source"),this.refcode=this.queryParams.get("refcode"),this.statesData=window[this.statesDataField]||[],this.validState="",this.validTeam="",this.statesData.length>0&&(this.statesData.forEach((t=>{this.statesKeys[t.name]=t.slug,this.statesNames[t.slug]=t.name,this.statesType[t.slug]=t.type,this.statesTeam[t.slug]={teamName:t.team_name,teamSlug:t.team_slug,teamColor:t.team_color},this.states.push(t.name),t.prefix&&(this.statesNames[t.slug]=`${t.prefix} ${t.name}`),this.state===t.slug&&(this.validState=this.state,this.validTeam=t.team_slug,this.validTeamColor=t.team_color)})),this.validTeam.length>0?(this.teamStars&&(this.teamStars.style=`--selected-team-color:${this.validTeamColor};`),this.formSourceFields.length>0&&this.formSourceFields.forEach(((t,e)=>{this.source?t.setAttribute("value",`${this.source}__${this.validTeam}`):this.refcode?t.setAttribute("value",`${this.refcode}__${this.validTeam}`):this.utmSource?t.setAttribute("value",`${this.utmSource}__${this.validTeam}`):t.setAttribute("value",`${this.orginalFormSourceValues[e]}__${this.validTeam}`)})),this.statLists.length>0&&this.statLists.forEach((t=>{t.dataset.team!==this.validTeam?(t.classList.add("hide"),t.classList.remove("show")):(t.classList.remove("hide"),t.classList.add("show"))})),this.teamTargets.length>0&&this.teamTargets.forEach((t=>{t.dataset.team!==this.validTeam?(t.classList.add("hide"),t.classList.remove("show")):(t.classList.remove("hide"),t.classList.add("show"))})),this.teamOverviews.length>0&&this.teamOverviews.forEach((t=>{if(t.dataset.team!==this.validTeam)t.classList.add("hide"),t.classList.remove("show");else if(t.classList.remove("hide"),t.classList.add("show"),null!==t.classList.contains("findyourteam__teamoverview--featuredactions"))if(this.limitedFeaturedActionsStates.includes(this.state)){const e=t.querySelector(".limitedfeaturedactions");e&&(e.classList.remove("hide"),e.classList.add("show"));const i=t.querySelector(".allfeaturedactions");i&&(i.classList.remove("show"),i.classList.add("hide"))}else{const e=t.querySelector(".limitedfeaturedactions");e&&(e.classList.remove("show"),e.classList.add("hide"));const i=t.querySelector(".allfeaturedactions");i&&(i.classList.remove("hide"),i.classList.add("show"))}})),this.teamIcons.length>0&&this.teamIcons.forEach((t=>{t.dataset.team!==this.validTeam?(t.classList.add("findyourteam__teamicon--notselected"),t.classList.remove("findyourteam__teamicon--selected")):(t.classList.remove("findyourteam__teamicon--notselected"),t.classList.add("findyourteam__teamicon--selected"))})),this.teamVs>0&&this.teamVs.forEach((t=>{t.classList.add("findyourteam__vs--selected")}))):this.resetToNoTeam()),this.filteredResults=[...this.states],this.loader=this.element.querySelector(yt.FIND_YOUR_TEAM_LOADER),this}setupHandlers(){return this.openDropdown=this.openDropdown.bind(this),this.closeDropdown=this.closeDropdown.bind(this),this.outsideClickListener=this.outsideClickListener.bind(this),this.filter=this.filter.bind(this),this.debounce=this.debounce.bind(this),this.setResults=this.setResults.bind(this),this.handleKeyboardEvents=this.handleKeyboardEvents.bind(this),this.focusListItem=this.focusListItem.bind(this),this.selectState=this.selectState.bind(this),this.selectChange=this.selectChange.bind(this),this.toggleButton=this.toggleButton.bind(this),this.filterOnInput=this.filterOnInput.bind(this),this.selectResult=this.selectResult.bind(this),this.resetToNoTeam=this.resetToNoTeam.bind(this),this.restartFlow=this.restartFlow.bind(this),this}enable(){return this.setResults(this.states),this.input.addEventListener(vt.KEY_DOWN,this.handleKeyboardEvents),this.resultsList.addEventListener(vt.KEY_DOWN,this.handleKeyboardEvents),this.input.addEventListener(vt.CLICK,this.openDropdown),this.select.addEventListener(vt.CHANGE,this.selectChange),document.addEventListener(vt.CLICK,this.outsideClickListener),this.input.addEventListener(vt.INPUT,this.filterOnInput),this.resultsList.addEventListener(vt.CLICK,this.selectResult),this.dropdownArrow.addEventListener(vt.CLICK,this.toggleButton),this.restart.addEventListener(vt.CLICK,this.restartFlow),window.addEventListener("load",(function(){if(window.location.hash){const t=document.querySelector(window.location.hash);t&&setTimeout((function(){t.scrollIntoView({block:"start",inline:"nearest"})}),1e3)}})),this}selectResult(t){return([...this.resultsList.childNodes].includes(t.target)||[...this.resultsList.childNodes].includes(t.target.parentNode))&&this.selectState(t.target),this}filterOnInput(t){const{value:e}=t.target;return this.debounce((()=>{this.filter(e),this.isDropDownOpen||this.openDropdown()})),this}toggleButton(t){return t.preventDefault(),this.isDropDownOpen?this.closeDropdown():this.openDropdown(),this}closeDropdown(){return this.isDropDownOpen=!1,this.resultsList.classList.remove(ft.VISIBLE),this.dropdownArrow.classList.remove(ft.EXPANDED),this.comboBox.setAttribute(mt.EXPANDED,gt.FALSE),this.input.setAttribute(mt.ACTIVE_DESCENDANT,""),this}openDropdown(){return this.isDropDownOpen=!0,this.resultsList.classList.add(ft.VISIBLE),this.dropdownArrow.classList.add(ft.EXPANDED),this.comboBox.setAttribute(mt.EXPANDED,gt.TRUE),this}resetToNoTeam(){this.formSourceFields.length>0&&this.formSourceFields.forEach(((t,e)=>{t.setAttribute("value",`${this.orginalFormSourceValues[e]}`)})),this.statLists.length>0&&this.statLists.forEach((t=>{t.dataset.team!==this.validTeam&&(t.classList.add("hide"),t.classList.remove("show"))})),this.teamTargets.length>0&&this.teamTargets.forEach((t=>{t.dataset.team!==this.validTeam&&(t.classList.add("hide"),t.classList.remove("show"))})),this.teamIcons.length>0&&this.teamIcons.forEach((t=>{t.classList.remove("findyourteam__teamicon--notselected"),t.classList.remove("findyourteam__teamicon--selected")})),this.teamOverviews.length>0&&this.teamOverviews.forEach((t=>{t.dataset.team!==this.validTeam&&(t.classList.add("hide"),t.classList.remove("show"))})),this.teamVs.length>0&&this.teamVs.forEach((t=>{t.classList.remove("findyourteam__vs--selected")}))}outsideClickListener(t){[this.input,this.dropdownArrow,...this.resultsList.childNodes].includes(t.target)||this.closeDropdown()}filter(t){this.filteredResults=t?this.states.filter((e=>e.toLowerCase().includes(t.toLowerCase()))):[...this.states],this.setResults(this.filteredResults)}setResults(t){if(Array.isArray(t)&&t.length>0){const e=t.map(((t,e)=>`<li class="autocomplete-item" id="autocomplete-item-${e}" role="listitem" tabindex="0"><span>${t}</span></li>`)).join("");this.resultsList.innerHTML=e,this.currentListItemFocused=-1}}debounce(t){clearTimeout(this.bounce),this.bounce=setTimeout((()=>{t()}),[this.debounce_timeout_ms])}focusListItem(t){const{id:e}=t;this.input.setAttribute(mt.ACTIVE_DESCENDANT,e),t.focus()}updateQueryParams(){return window.history.pushState(null,null,`?${this.queryParams.toString()}`),window.dispatchEvent(new CustomEvent(vt.PUSH_STATE,{detail:{vsa_state:this.state}})),this}selectChange(t){const{value:e}=t.target,i=this.statesNames[e];if(this.input.value=e,e&&i){this.state=e.toLowerCase(),this.validState=this.state;const t=this.statesTeam[this.state],s=this.statesType[this.state];this.validTeam=t.teamSlug,this.validTeamColor=t.teamColor,this.queryParams.set(gt.STATE_PARAM,this.state.toUpperCase()),this.updateQueryParams();const n=this.headingStateText.replace("[[state]]",`${i}`).replace("[[team_name]]",`${t?t.teamName:""}`);this.headingState.innerHTML=n;const r=("state"!==s&&""!==s&&s?this.restartLocationText:this.restartStateText).replace("[[state]]",`${i}`).replace("[[team_name]]",`${t?t.teamName:""}`);this.restart.innerHTML=r,this.element.dataset.geoState=this.state,this.element.classList.add(ft.FIND_YOUR_TEAM_STATE),this.part1.classList.remove(ft.SHOW),this.part2.classList.add(ft.SHOW),this.validTeam.length>0?(this.teamStars&&(this.teamStars.style=`--selected-team-color:${this.validTeamColor};`),this.formSourceFields.length>0&&this.formSourceFields.forEach(((t,e)=>{this.source?t.setAttribute("value",`${this.source}__${this.validTeam}`):this.refcode?t.setAttribute("value",`${this.refcode}__${this.validTeam}`):this.utmSource?t.setAttribute("value",`${this.utmSource}__${this.validTeam}`):t.setAttribute("value",`${this.orginalFormSourceValues[e]}__${this.validTeam}`)})),this.statLists.length>0&&this.statLists.forEach((t=>{t.dataset.team!==this.validTeam?(t.classList.add("hide"),t.classList.remove("show")):(t.classList.remove("hide"),t.classList.add("show"))})),this.teamTargets.length>0&&this.teamTargets.forEach((t=>{t.dataset.team!==this.validTeam?(t.classList.add("hide"),t.classList.remove("show")):(t.classList.remove("hide"),t.classList.add("show"))})),this.teamOverviews.length>0&&this.teamOverviews.forEach((t=>{t.dataset.team!==this.validTeam?(t.classList.add("hide"),t.classList.remove("show")):(t.classList.remove("hide"),t.classList.add("show"),null!==t.classList.contains("findyourteam__teamoverview--featuredactions")&&(this.limitedFeaturedActionsStates.includes(this.state)?(t.querySelector(".limitedfeaturedactions").classList.remove("hide"),t.querySelector(".limitedfeaturedactions").classList.add("show"),t.querySelector(".allfeaturedactions").classList.remove("show"),t.querySelector(".allfeaturedactions").classList.add("hide")):(t.querySelector(".limitedfeaturedactions").classList.remove("show"),t.querySelector(".limitedfeaturedactions").classList.add("hide"),t.querySelector(".allfeaturedactions").classList.remove("hide"),t.querySelector(".allfeaturedactions").classList.add("show"))))})),this.teamIcons.length>0&&this.teamIcons.forEach((t=>{t.dataset.team!==this.validTeam?(t.classList.add("findyourteam__teamicon--notselected"),t.classList.remove("findyourteam__teamicon--selected")):(t.classList.remove("findyourteam__teamicon--notselected"),t.classList.add("findyourteam__teamicon--selected"))})),this.teamVs.length>0&&this.teamVs.forEach((t=>{t.classList.add("findyourteam__vs--selected")}))):this.resetToNoTeam()}return this.headingState.scrollIntoView({block:"center",inline:"nearest"}),this}selectState(t){const e=t.innerText,i=this.statesKeys[e];if(this.input.value=e,this.input.removeAttribute(mt.ACTIVE_DESCENDANT),t.setAttribute(mt.SELECTED,gt.TRUE),this.input.focus(),this.closeDropdown(),e&&i){this.state=i.toLowerCase(),this.validState=this.state,this.select.value=i;const t=this.statesTeam[this.state],e=this.statesType[this.state];this.validTeam=t.teamSlug,this.validTeamColor=t.teamColor,this.queryParams.set(gt.STATE_PARAM,this.state.toUpperCase()),this.updateQueryParams();const s=this.headingStateText.replace("[[state]]",`${this.statesNames[this.state]}`).replace("[[team_name]]",`${t?t.teamName:""}`);this.headingState.innerHTML=s;const n=("state"!==e&&""!==e&&e?this.restartLocationText:this.restartStateText).replace("[[state]]",`${this.statesNames[this.state]}`).replace("[[team_name]]",`${t?t.teamName:""}`);this.restart.innerHTML=n,this.element.classList.add(ft.FIND_YOUR_TEAM_STATE),this.element.dataset.geoState=this.state,this.part1.classList.remove(ft.SHOW),this.part2.classList.add(ft.SHOW),this.validTeam.length>0?(this.teamStars&&(this.teamStars.style=`--selected-team-color:${this.validTeamColor};`),this.formSourceFields.length>0&&this.formSourceFields.forEach(((t,e)=>{this.source?t.setAttribute("value",`${this.source}__${this.validTeam}`):this.refcode?t.setAttribute("value",`${this.refcode}__${this.validTeam}`):this.utmSource?t.setAttribute("value",`${this.utmSource}__${this.validTeam}`):t.setAttribute("value",`${this.orginalFormSourceValues[e]}__${this.validTeam}`)})),this.statLists.length>0&&this.statLists.forEach((t=>{t.dataset.team!==this.validTeam?(t.classList.add("hide"),t.classList.remove("show")):(t.classList.remove("hide"),t.classList.add("show"))})),this.teamTargets.length>0&&this.teamTargets.forEach((t=>{t.dataset.team!==this.validTeam?(t.classList.add("hide"),t.classList.remove("show")):(t.classList.remove("hide"),t.classList.add("show"))})),this.teamOverviews.length>0&&this.teamOverviews.forEach((t=>{t.dataset.team!==this.validTeam?(t.classList.add("hide"),t.classList.remove("show")):(t.classList.remove("hide"),t.classList.add("show"),t.classList.contains("findyourteam__teamoverview--featuredactions")&&(this.limitedFeaturedActionsStates.includes(this.state)?(t.querySelector(".limitedfeaturedactions").classList.remove("hide"),t.querySelector(".limitedfeaturedactions").classList.add("show"),t.querySelector(".allfeaturedactions").classList.remove("show"),t.querySelector(".allfeaturedactions").classList.add("hide")):(t.querySelector(".limitedfeaturedactions").classList.remove("show"),t.querySelector(".limitedfeaturedactions").classList.add("hide"),t.querySelector(".allfeaturedactions").classList.remove("hide"),t.querySelector(".allfeaturedactions").classList.add("show"))))})),this.teamIcons.length>0&&this.teamIcons.forEach((t=>{t.dataset.team!==this.validTeam?(t.classList.add("findyourteam__teamicon--notselected"),t.classList.remove("findyourteam__teamicon--selected")):(t.classList.remove("findyourteam__teamicon--notselected"),t.classList.add("findyourteam__teamicon--selected"))})),this.teamVs.length>0&&this.teamVs.forEach((t=>{t.classList.add("findyourteam__vs--selected")}))):this.resetToNoTeam()}return this.teamIconsList.scrollIntoView(),this}restartFlow(t){return t.preventDefault(),this.part1.classList.add(ft.SHOW),this.part2.classList.remove(ft.SHOW),this.element.classList.remove(ft.FIND_YOUR_TEAM_STATE),this.queryParams.delete(gt.STATE_PARAM),this.input.value="",this.select.value="",this.validTeam="",this.validState="",this.filteredResults=[...this.states],this.setResults(this.filteredResults),this.updateQueryParams(),this.resetToNoTeam(),this.part1.scrollIntoView({block:"center",inline:"nearest"}),this}handleKeyboardEvents(t){const e=this.resultsList.childNodes;let i=null;switch([vt.ARROW_UP,vt.ARROW_DOWN,vt.ENTER].includes(t.key)&&t.preventDefault(),t.key){case vt.ARROW_DOWN:this.currentListItemFocused<e.length-1&&(this.isDropDownOpen||this.openDropdown(),this.currentListItemFocused+=1,i=e.item(this.currentListItemFocused),this.focusListItem(i));break;case vt.ARROW_UP:this.currentListItemFocused>0&&(this.currentListItemFocused-=1,i=e.item(this.currentListItemFocused),this.focusListItem(i));break;case"Home":this.currentListItemFocused>0&&(this.currentListItemFocused=0,i=e.item(this.currentListItemFocused),this.focusListItem(i));break;case"End":this.currentListItemFocused<e.length-1&&(this.currentListItemFocused=e.length-1,i=e.item(this.currentListItemFocused),this.focusListItem(i));break;case vt.ENTER:this.isDropDownOpen?e[this.currentListItemFocused].innerText&&this.selectState(e[this.currentListItemFocused]):this.openDropdown();break;case"Escape":this.isDropDownOpen&&this.closeDropdown();break;default:t.target!==this.input&&/([a-zA-Z0-9_]|ArrowLeft|ArrowRight)/.test(t.key)&&this.input.focus()}return this}},Form:class extends Ie{constructor(t){super(),this.element=t,this.init()}init(){return this.cacheDomReferences().setupHandlers().enable(),this}cacheDomReferences(){return this.form=this.element.querySelector(yt.FORM_ACTIONNETWORK),this.postSubmit=this.element.querySelector(yt.FORM_POST_SUBMIT),this.hpPostSubmit=document.querySelector(".homepageform__postsubmit"),this.firstName=this.element.querySelector(yt.INPUT_GROUP_FIRST_NAME),this.firstNameInput=this.element.querySelector(yt.FORM_INPUT_FIRST_NAME),this.lastName=this.element.querySelector(yt.INPUT_GROUP_LAST_NAME),this.lastNameInput=this.element.querySelector(yt.FORM_INPUT_LAST_NAME),this.email=this.element.querySelector(yt.INPUT_GROUP_EMAIL),this.emailInput=this.element.querySelector(yt.FORM_INPUT_EMAIL),this.mobile=this.element.querySelector(yt.INPUT_GROUP_PHONE),this.mobileInput=this.element.querySelector(yt.FORM_INPUT_PHONE),this.zip=this.element.querySelector(yt.INPUT_GROUP_ZIP),this.zipInput=this.element.querySelector(yt.FORM_INPUT_ZIP),this.ooeTeamFieldset=this.element.querySelector(".input-group--ooe-team"),this.endpoint=this.form&&this.form.dataset.actionNetwork?`${this.form.dataset.actionNetwork}?background_request=true`:"https://actionnetwork.org/api/v2/forms/03ce9525-efc2-40e9-8b33-25b893d93355/submissions?background_request=true",this.queryString=window.location.search,this.currentUrl=window.location.href,this.urlParams=new URLSearchParams(this.queryString),this.source=this.urlParams.get("source"),this.refcode=this.urlParams.get("refcode"),this.utmSource=this.urlParams.get("utm_source"),this.emailReferrer=this.urlParams.get("email_referrer"),this.autoresponse=this.element?.dataset?.autoresponse,this}setupHandlers(){return this.onSubmit=this.onSubmit.bind(this),this}enable(){return this.form.addEventListener(vt.SUBMIT,this.onSubmit),this.zipInput&&this.zipInput.addEventListener("invalid",(()=>{this.zip.classList.add("error")})),this.mobileInput&&this.mobileInput.addEventListener("invalid",(()=>{this.mobile.classList.add("error")})),this.emailInput&&this.emailInput.addEventListener("invalid",(()=>{this.email.classList.add("error")})),this.firstNameInput&&this.firstNameInput.addEventListener("invalid",(()=>{this.firstName.classList.add("error")})),this.lastNameInput&&this.lastNameInput.addEventListener("invalid",(()=>{this.lastName.classList.add("error")})),this}onSubmit(t){t.preventDefault();const e=new FormData(this.form),i=e.get("email"),s=e.get("zip"),n=e.get("phone"),r=e.get("first-name"),o=e.get("last-name"),a=e.get("sms_subscription"),l=e.get("default_source"),d=e.get("ooe_team");let h=!1,c=!1;let u=!0;if(i&&(h=Me(i),h?this.email.classList.remove(ft.ERROR):this.email.classList.add(ft.ERROR)),s&&(c=Re(s),c.valid?this.zip.classList.remove(ft.ERROR):this.zip.classList.add(ft.ERROR)),this.ooeTeamFieldset&&(this.ooeTeamFieldset.dataset.ooeTeamRequired&&!d?(this.ooeTeamFieldset.classList.add(ft.ERROR),u=!1):(this.ooeTeamFieldset.classList.remove(ft.ERROR),u=!0)),c.valid&&h&&u){const t={address:i,status:"subscribed"},e={postal_code:s};c.valid&&"N/A"!==c.country&&(e.country=c.country);const h={person:{}};if(h.person.email_addresses=[t],h.person.postal_addresses=[e],r&&(h.person.given_name=r),o&&(h.person.family_name=o),n){const t={number:n,type:"mobile"};"on"===a&&(t.status="subscribed"),h.person.phone_numbers=[t]}d&&(h.person.custom_fields={ooe_team:d}),(l||this.source||this.emailReferrer)&&(h["action_network:referrer_data"]={},l&&l.includes("__team")?h["action_network:referrer_data"].source=l:(l||this.source)&&(h["action_network:referrer_data"].source=this.source||this.refcode||this.utmSource||l),this.emailReferrer&&(h["action_network:referrer_data"].email_referrer=this.emailReferrer),h["action_network:referrer_data"].website=this.currentUrl),this.autoresponse&&(h.triggers={autoresponse:{enabled:!0}}),fetch(this.endpoint,{method:"POST",body:JSON.stringify(h),headers:{"Content-Type":"application/json"}}).then((t=>{if(!t.ok)throw new Error(t.status);return t.json()})).then((()=>{this.form.dispatchEvent(new CustomEvent(vt.FORM_SUBMIT)),this.form.classList.add(ft.HIDDEN),this.postSubmit&&(this.postSubmit.classList.remove(ft.HIDDEN),this.postSubmit.classList.contains("footer__post-submit")||this.postSubmit.scrollIntoView({block:"start",inline:"nearest"})),this.hpPostSubmit&&this.hpPostSubmit.classList.remove(ft.HIDDEN)})).catch((t=>{}))}return this}},GeoAlert:class extends Ie{constructor(t){super(),this.element=t,this.init()}init(){return this.cacheDomReferences().setupHandlers().enable(),this}cacheDomReferences(){return this.link=this.element.querySelector(yt.GEOALERT_LINK),this.text=this.element.querySelector(yt.GEOALERT_TEXT),this.paragraph=this.element.querySelector(yt.GEOALERT_PARA),this.electionAlerts=window.election_alerts,this.state=window.url_details&&window.url_details.vsa_state?window.url_details.vsa_state:"",this.state=this.state.toLowerCase(),this.stateElectionAlerts={},this.electionFlags={today:!1,tomorrow:!1,nextWeek:!1},this.today=new Date,[this.todayTime]=this.today.toTimeString().split(" "),this.tomorrow=new Date,this.tomorrow.setDate(this.tomorrow.getDate()+1),this.nextWeek=new Date,this.nextWeek.setDate(this.nextWeek.getDate()+8),this.alertBarContent={label:"",state:"",link:{}},this}setupHandlers(){return this.isToday=this.isToday.bind(this),this.isTomorrow=this.isTomorrow.bind(this),this.isNextWeek=this.isNextWeek.bind(this),this.setAlertBar=this.setAlertBar.bind(this),this.updateState=this.updateState.bind(this),this}enable(){return this.setAlertBar(),window.addEventListener(vt.PUSH_STATE,this.updateState),this}updateState(t){t&&t.detail.vsa_state!==this.state&&(this.state=t.detail.vsa_state,this.setAlertBar())}setAlertBar(){if(this.state&&(this.stateElectionAlerts=window.election_alerts[this.state],this.stateElectionAlerts&&(this.alertBars=this.stateElectionAlerts.alert_bar||[],this.alertBarContent={label:"",link:{},state:this.stateElectionAlerts.state.name},this.electionFlags={today:!1,tomorrow:!1,nextWeek:!1},this.alertBars.forEach((t=>{const e=new Date(`${t.election_date}T${this.todayTime}`);this.isToday(e)&&!this.electionFlags.today?(this.alertBarContent.label=t.alert_bar_label.replace("[[date]]","today"),this.alertBarContent.link=t.alert_bar_link,this.electionFlags.today=!0):!this.isTomorrow(e)||this.electionFlags.today||this.electionFlags.tomorrow?!this.isNextWeek(e)||this.electionFlags.today||this.electionFlags.tomorrow||this.electionFlags.nextWeek||(this.alertBarContent.label=t.alert_bar_label.replace("[[date]]",t.election_date),this.alertBarContent.link=t.alert_bar_link,this.electionFlags.nextWeek=!0):(this.alertBarContent.label=t.alert_bar_label.replace("[[date]]","tomorrow"),this.alertBarContent.link=t.alert_bar_link,this.electionFlags.tomorrow=!0),this.alertBarContent.label&&(t.election_title?this.alertBarContent.label=this.alertBarContent.label.replace("[[election]]",t.election_title):this.alertBarContent.label=this.alertBarContent.label.replace("[[election]]","election"))})))),this.alertBarContent.label){if(this.paragraph.innerHTML=`${this.alertBarContent.state}: ${this.alertBarContent.label}`,this.alertBarContent.link){const{target:t,url:e,title:i,name:s}=this.alertBarContent.link,n=i||s;this.text.innerHTML=n,this.link.setAttribute("href",e),"_blank"===t&&this.link.setAttribute("target",t),this.link.classList.remove(ft.GEOALERT_LINK_HIDDEN)}else this.link.classList.add(ft.GEOALERT_LINK_HIDDEN);this.element.classList.remove(ft.GEOALERT_HIDDEN)}else this.element.classList.add(ft.GEOALERT_HIDDEN)}isToday(t){return t.getDate()===this.today.getDate()&&t.getMonth()===this.today.getMonth()&&t.getFullYear()===this.today.getFullYear()}isTomorrow(t){return t.getDate()===this.tomorrow.getDate()&&t.getMonth()===this.tomorrow.getMonth()&&t.getFullYear()===this.tomorrow.getFullYear()}isNextWeek(t){return(this.nextWeek-t)/864e5<=7&&(this.nextWeek-t)/864e5>=0}},Header:class extends Ie{constructor(t,e){super(),this.element=t,this.ResizeService=e.ResizeService,this.init()}init(){return this.cacheDomReferences().setupHandlers().enable(),this}cacheDomReferences(){return this.menu=this.element.querySelector(".nav__set-wrap"),this.menuItems=this.element.querySelector(".nav__set"),this.navLinks=this.element.querySelectorAll(".nav__link"),this.hamburger=this.element.querySelector(".nav__hamburger"),this.logo=this.element.querySelector(".header__logo svg"),this.close=this.element.querySelector(".nav__close"),this.expand=this.element.querySelectorAll(".nav__expand"),this.overlay=document.querySelector(".overlay"),this.sentinel=document.createElement("div"),this.sentinel.style.position="absolute",this.sentinel.style.top="0",this.sentinel.classList.add("header__sentinel"),this.element.parentNode.insertBefore(this.sentinel,this.element),this.resizeTimer=undefined,this.toggleTimer=undefined,this}setupHandlers(){return this.toggleHamburgerHandler=this.toggleHamburgerHandler.bind(this),this.toggleSubnav=this.toggleSubnav.bind(this),this.closeSubnav=this.closeSubnav.bind(this),this.openSubnav=this.openSubnav.bind(this),this.handleMediaResize=this.handleMediaResize.bind(this),this.escapeListener=this.escapeListener.bind(this),this.outsideClickListener=this.outsideClickListener.bind(this),this.handleResize=this.handleResize.bind(this),this.mediaQuery=window.matchMedia("(max-width: 1199px)"),this}enable(){this.mediaQuery.addEventListener("change",this.handleMediaResize),this.ResizeService.addCallback(this.handleResize);return new IntersectionObserver((([t])=>{0===t.intersectionRatio?this.element.classList.add(ft.HEADER_PINNED):this.element.classList.remove(ft.HEADER_PINNED)}),{threshold:[0]}).observe(this.sentinel),window.innerWidth>=1200?(this.hamburger?.setAttribute(mt.EXPANDED,!0),this.menu?.classList.add("nav__set-wrap--open"),this.menuItems?.classList.add("nav__set--open"),this.element?.classList.add("header--open"),this.logo?.classList.add(ft.OPEN)):this.element?.classList.contains("header--open")&&(this.hamburger?.setAttribute(mt.EXPANDED,!1),this.menu?.classList.remove("nav__set-wrap--open"),this.menuItems?.classList.remove("nav__set--open"),this.element?.classList.remove("header--open"),document.body?.classList.remove(ft.NAV_OPEN),this.logo?.classList.remove(ft.OPEN)),null!==this.hamburger&&this.hamburger.addEventListener(vt.CLICK,this.toggleHamburgerHandler),null!==this.close&&this.close.addEventListener(vt.CLICK,this.toggleHamburgerHandler),null!==this.expand&&[...this.expand].forEach((t=>{t.addEventListener(vt.CLICK,(()=>this.toggleSubnav(t))),t.addEventListener(vt.MOUSEENTER,(()=>this.openSubnav(t))),t.addEventListener(vt.MOUSELEAVE,(()=>this.closeSubnav(t,!0)))})),document.addEventListener(vt.KEY_DOWN,this.escapeListener),document.addEventListener(vt.CLICK,this.outsideClickListener),this}escapeListener(t){const e="true"===this.hamburger.getAttribute(mt.EXPANDED);t.keyCode===_t.ESCAPE&&e&&this.toggleHamburgerHandler()}outsideClickListener(t){const e="true"===this.hamburger.getAttribute(mt.EXPANDED);t.target===this.overlay&&e&&this.toggleHamburgerHandler()}openSubnav(t){return[...this.expand].forEach((e=>{if(t!==e){const t=e.dataset.subnav,i=this.element.querySelector(`#${t}`),s=i.parentElement;i.classList.remove("nav__subset--open"),s.classList.remove("nav__subwrap--open"),e.setAttribute(mt.EXPANDED,!1),e.classList.remove("nav__expand--open")}else{const e=t.dataset.subnav,i=this.element.querySelector(`#${e}`),s=i.parentElement;i.classList.add("nav__subset--open"),s.classList.add("nav__subwrap--open"),t.setAttribute(mt.EXPANDED,!0),t.classList.add("nav__expand--open")}})),this.toggleTimer&&clearTimeout(this.toggleTimer),this}closeSubnav(t,e){return[...this.expand].forEach((i=>{if(t!==i){const t=i.dataset.subnav,e=this.element.querySelector(`#${t}`),s=e.parentElement;e.classList.remove("nav__subset--open"),s.classList.remove("nav__subwrap--open"),i.setAttribute(mt.EXPANDED,!1),i.classList.remove("nav__expand--open")}else if(e)this.toggleTimer=setTimeout((()=>{const e=t.dataset.subnav,i=this.element.querySelector(`#${e}`),s=i.parentElement;i.classList.remove("nav__subset--open"),s.classList.remove("nav__subwrap--open"),t.setAttribute(mt.EXPANDED,!1),t.classList.remove("nav__expand--open")}),1e3);else{const e=t.dataset.subnav,i=this.element.querySelector(`#${e}`),s=i.parentElement;i.classList.remove("nav__subset--open"),s.classList.remove("nav__subwrap--open"),t.setAttribute(mt.EXPANDED,!1),t.classList.remove("nav__expand--open")}})),this}toggleSubnav(t){return"false"===("true"===t.getAttribute(mt.EXPANDED)?"true":"false")?this.openSubnav(t):this.closeSubnav(t,!1),this}toggleHamburgerHandler(){const t="true"===this.hamburger.getAttribute(mt.EXPANDED)?"false":"true";return this.hamburger.setAttribute(mt.EXPANDED,t),this.menu.classList.toggle("nav__set-wrap--open"),this.menuItems.classList.toggle("nav__set--open"),this.element.classList.toggle("header--open"),document.body.classList.toggle(ft.NAV_OPEN),this.logo.classList.toggle(ft.OPEN),this}handleMediaResize(t){document.body.classList.remove(ft.NAV_OPEN),t.matches||this.element.classList.contains("header--open")?this.element.classList.contains("header--open")&&(this.hamburger.setAttribute(mt.EXPANDED,!1),this.menu.classList.remove("nav__set-wrap--open"),this.menuItems.classList.remove("nav__set--open"),this.element.classList.remove("header--open"),document.body.classList.remove(ft.NAV_OPEN),this.logo.classList.remove(ft.OPEN)):(this.hamburger.setAttribute(mt.EXPANDED,!0),this.menu.classList.add("nav__set-wrap--open"),this.menuItems.classList.add("nav__set--open"),this.element.classList.add("header--open"),this.logo.classList.add(ft.OPEN))}handleResize(){document.body.classList.add("disable-animation"),clearTimeout(this.resizeTimer),this.resizeTimer=setTimeout((()=>{document.body.classList.remove("disable-animation")}),200)}},InViewport:$e,LottieOnScroll:class extends Ie{constructor(t){super(),this.element=t,this.init()}init(){return this.enable(),this}enable(){return this.isReduced=!0===window.matchMedia("(prefers-reduced-motion: reduce)")||!0===window.matchMedia("(prefers-reduced-motion: reduce)").matches||"reduced"===this.element.dataset.isReduced,this.animation=this.element.dataset.animation,this.delay=this.element.dataset.delay,this.startFrame=this.element.dataset.startFrame?parseInt(this.element.dataset.startFrame,10):0,this.endFrame=this.element.dataset.endFrame?parseInt(this.element.dataset.endFrame,10):60,this.startThreshold=this.element.dataset.startThreshold?parseInt(this.element.dataset.startThreshold,10):.2,this.endThreshold=this.element.dataset.endThreshold?parseInt(this.element.dataset.endThreshold,10):.8,this.element.addEventListener(vt.READY,(()=>{this.isReduced?this.element.seek("100%"):"playOnShow"===this.animation?this.delay?(this.element.pause(),setTimeout((()=>{this.element.playOnShow({threshold:[.05]})}),600)):this.element.playOnShow({threshold:[.05]}):this.element.playOnScroll({segments:[this.startFrame,this.endFrame],threshold:[this.startThreshold,this.endThreshold]})})),this}},Meerkat:class extends Ie{constructor(t){super(),this.element=t,this.init()}init(){return this.cacheDomReferences().setupHandlers().enable(),this}cacheDomReferences(){return this.openButton=this.element.querySelector(yt.MEERKAT_OPEN),this.closeButton=this.element.querySelector(yt.MEERKAT_CLOSE),this.dialog=this.element.querySelector(yt.MEERKAT_DIALOG),this.eyes=this.element.querySelector(yt.MEERKAT_ICON),this.form=this.element.querySelector(yt.FORM_ACTIONNETWORK),this.idleTime=this.element.dataset.idleTime,this.idleTime=1e3*parseInt(this.idleTime,10),this.meerkatCookie=wt(gt.MEERKAT),this}setupHandlers(){return this.close=this.close.bind(this),this.open=this.open.bind(this),this.remove=this.remove.bind(this),this.destroyTimeHook=this.destroyTimeHook.bind(this),this.destroyListeners=this.destroyListeners.bind(this),this.show=this.show.bind(this),this.resetTimeHook=this.resetTimeHook.bind(this),this.initializeTimeHook=this.initializeTimeHook.bind(this),this.setupListeners=this.setupListeners.bind(this),this}enable(){return""===this.meerkatCookie&&(this.closeButton.addEventListener(vt.CLICK,this.close),this.openButton.addEventListener(vt.CLICK,this.open),this.form.addEventListener(vt.FORM_SUBMIT,this.remove),this.destroyTimeHook(),this.initializeTimeHook(),this.setupListeners()),this}setupListeners(){document.addEventListener(vt.CLICK,function(){this.resetTimeHook()}.bind(this)),document.addEventListener(vt.WHEEL,function(){this.resetTimeHook()}.bind(this)),document.addEventListener(vt.TOUCH_MOVE,function(){this.resetTimeHook()}.bind(this)),document.addEventListener(vt.TOUCH_CAN,function(){this.resetTimeHook()}.bind(this)),document.addEventListener(vt.TOUCHEND,function(){this.resetTimeHook()}.bind(this)),document.addEventListener(vt.TOUCHSTART,function(){this.resetTimeHook()}.bind(this))}show(){this.element.classList.add(ft.MEERKAT_SHOW)}open(){return this.element.classList.add(ft.MEERKAT_OPEN),this.element.classList.remove(ft.MEERKAT_CLOSE),this.openButton.setAttribute(mt.EXPANDED,gt.TRUE),this.closeButton.setAttribute(mt.EXPANDED,gt.TRUE),this.dialog.setAttribute(mt.HIDDEN,gt.FALSE),this.eyes.stop(),this}close(){return this.element.classList.remove(ft.MEERKAT_OPEN),this.element.classList.add(ft.MEERKAT_CLOSE),this.openButton.setAttribute(mt.EXPANDED,gt.FALSE),this.closeButton.setAttribute(mt.EXPANDED,gt.FALSE),this.dialog.setAttribute(mt.HIDDEN,gt.TRUE),this.eyes.play(),this}remove(){setTimeout((()=>{this.element.classList.remove(ft.MEERKAT_OPEN),this.element.classList.remove(ft.MEERKAT_SHOW),Ne(gt.MEERKAT,gt.SUBMITTED,31),this.meerkatCookie=wt(gt.MEERKAT),this.destroyListeners(),this.destroyTimeHook()}),1500)}destroyTimeHook(){clearTimeout(this.timeHook),this.timeHook=null}resetTimeHook(){this.destroyTimeHook(),""===this.meerkatCookie&&this.initializeTimeHook()}destroyListeners(){document.removeEventListener(vt.CLICK,function(){this.resetTimeHook()}.bind(this)),document.removeEventListener(vt.WHEEL,function(){this.resetTimeHook()}.bind(this)),document.removeEventListener(vt.TOUCH_MOVE,function(){this.resetTimeHook()}.bind(this)),document.removeEventListener(vt.TOUCH_CAN,function(){this.resetTimeHook()}.bind(this)),document.removeEventListener(vt.TOUCHEND,function(){this.resetTimeHook()}.bind(this)),document.removeEventListener(vt.TOUCHSTART,function(){this.resetTimeHook()}.bind(this))}initializeTimeHook(){null==this.timeHook&&(this.timeHook=setTimeout(function(){this.destroyTimeHook(),this.show()}.bind(this),this.idleTime))}},Parallax:class extends Ie{constructor(t){super(),this.element=t,this.init()}init(){return this.cacheDomReferences().enable(),this}cacheDomReferences(){return this.tx=this.element.dataset.tx,this.ty=this.element.dataset.ty,this.r=this.element.dataset.r,this.scl=this.element.dataset.scl,this.ex=this.element.dataset.ex,this.ey=this.element.dataset.ey,this.er=this.element.dataset.er,this.escl=this.element.dataset.escl,this}enable(){const t=Ue.create({elem:this.element,direct:!0,from:"top-bottom",to:"bottom-top",track:!0,props:{"--tx":{from:`${this.tx}px`,to:`${this.ex}px`},"--ty":{from:`${this.ty}px`,to:`${this.ey}px`},"--r":{from:`${this.r}deg`,to:`${this.er}deg`},"--scale":{from:`${this.scl?this.scl:1}`,to:`${this.escl?this.escl:1}`}}});return t.start(),setTimeout((()=>{t.update(),window.dispatchEvent(new Event("resize"))}),2e3),this}},Popup:class extends Ie{constructor(t){super(),this.element=t,this.init()}init(){return this.cacheDomReferences().setupHandlers().enable(),this}cacheDomReferences(){return this.closeButton=this.element.querySelector(yt.POPUP_CLOSE),this.house=this.element.querySelector(yt.POPUP_ICON_HOUSE),this.form=this.element.querySelector(yt.FORM_ACTIONNETWORK),this.popupKey=this.element.dataset.popupKey,this.popupCookie=wt(gt.POPUP),this.activeElement=document.activeElement,this}setupHandlers(){return this.close=this.close.bind(this),this.show=this.show.bind(this),this.remove=this.remove.bind(this),this.onEscape=this.onEscape.bind(this),this.outsideClickListener=this.outsideClickListener.bind(this),this}enable(){return""===this.popupCookie||this.popupCookie!==this.popupKey?(this.closeButton.addEventListener(vt.CLICK,this.close),this.trap=wi(this.element,{setReturnFocus:this.activeElement,allowOutsideClick:!0}),this.show(),this.form&&this.form.addEventListener(vt.FORM_SUBMIT,this.remove),document.addEventListener(vt.KEY_DOWN,this.onEscape),document.addEventListener(vt.CLICK,this.outsideClickListener),document.addEventListener("cmplz_before_cookiebanner",(()=>{if("function"==typeof window.cmplz_get_banner_status){"dismissed"===window.cmplz_get_banner_status()?this.trap&&this.trap.activate():document.addEventListener("cmplz_banner_status",(t=>{const{detail:e}=t||{};"dismissed"===e&&this.trap&&this.trap.activate()}))}}))):(this.element.setAttribute(mt.HIDDEN,gt.TRUE),this.closeButton.setAttribute(mt.EXPANDED,gt.FALSE)),this}onEscape(t){return t.keyCode===_t.ESCAPE&&this.close(),this}show(){return this.element.classList.add(ft.SHOW),document.body.classList.add(ft.POPUP_OPEN),document.documentElement.classList.add(ft.POPUP_OPEN),this.element.setAttribute(mt.HIDDEN,gt.FALSE),this.closeButton.setAttribute(mt.EXPANDED,gt.TRUE),this}remove(){setTimeout((()=>{this.close()}),1500)}close(){return this.element.classList.remove(ft.SHOW),this.element.setAttribute(mt.HIDDEN,gt.TRUE),this.closeButton.setAttribute(mt.EXPANDED,gt.FALSE),document.body.classList.remove(ft.POPUP_OPEN),document.documentElement.classList.remove(ft.POPUP_OPEN),Ne(gt.POPUP,this.popupKey,7),this.popupCookie=wt(gt.POPUP),document.removeEventListener(vt.CLICK,this.outsideClickListener),document.removeEventListener(vt.KEY_DOWN,this.onEscape),this.trap&&this.trap.deactivate(),this.house&&this.house.stop(),this}outsideClickListener(t){t.target===this.element&&this.close()}},ProgramMap:class{constructor(t,e){this.width=726,this.height=425,this.element=t,this.selectedOffice="show-all",this.ResizeService=e.ResizeService,this.init()}init(){return this.cacheDomReferences().enable().setUpHandlers().setUpMap(),this}cacheDomReferences(){return this.map=this.element,this.us=window.topojson,this.stats=window.states,this.queryString=window.location.search,this.queryParams=new URLSearchParams(this.queryString),this.state=(this.queryParams.get(gt.STATE_PARAM)||"").toLowerCase(),this.state||(this.state=(this.element.dataset.geoState||"").toLowerCase()),this}setUpHandlers(){return window.addEventListener("load",(()=>{const t=document.querySelector('input[name="program-map"]:checked');this.selectedOffice=t?t.value:"show-all","show-all"!==this.selectedOffice?this.element.classList.add("hide-pointer"):this.element.classList.remove("hide-pointer"),t&&this.updateMap()})),window.addEventListener(vt.PUSH_STATE,this.updateState),this.ResizeService.addCallback(this.handleResize),this}enable(){return this.showTooltip=this.showTooltip.bind(this),this.moveTooltip=this.moveTooltip.bind(this),this.hideTooltip=this.hideTooltip.bind(this),this.updateMap=this.updateMap.bind(this),this.updateState=this.updateState.bind(this),this.handleResize=this.handleResize.bind(this),this}updateMap(){this.mapFeatures.selectAll("path").attr("fill",(t=>{const e=t.id;return this.stats[e]?"show-all"===this.selectedOffice||this.stats[e][this.selectedOffice]?this.stats[e].selected_color?this.stats[e].selected_color:"white":this.stats[e].deselected_color?this.stats[e].deselected_color:"white":"white"})).attr("fill-opacity","1")}setUpMap(){this.svg=io.select(".programmap__map-svg").append("svg").attr("class","programmap__center-container").attr("preserveAspectRatio","xMidYMid").attr("viewBox",`0 0 ${this.width} ${this.height}`).attr("style","overflow:visible;"),this.mapFeatures=this.svg.append("g").attr("class","programmap__states"),this.mapCentroids=this.svg.append("g").attr("class","programmap__statecentroids"),this.tooltip=io.select(".programmap__tooltip"),this.tooltipContent=io.select(".programmap__tooltip").append("div").attr("class","programmap__tooltip-content");const t=io.geoAlbersUsa().scale(1),e=io.geoPath().projection(t),i=((t,e,i,s)=>{const n=e.bounds(t);return.95/Math.max((n[1][0]-n[0][0])/i,(n[1][1]-n[0][1])/s)})(Si(this.us,this.us.objects.states),e,this.width,this.height);return t.scale(i).translate([this.width/2,this.height/2]),this.mapFeatures.attr("id","states").selectAll("path").data(Si(this.us,this.us.objects.states).features).enter().append("path").attr("d",e).attr("class","programmap__state").attr("id",(t=>t.id)).attr("data-state",(t=>t.properties.name)).on("mouseover",(t=>{const{target:e}=t;io.select(e).raise()})).on("mousemove",((t,e)=>this.moveTooltip(t,e))).on("mouseenter",(()=>{this.tooltip.classed("hidden",!1)})).on("mouseleave",((t,e)=>{this.hideTooltip(t,e)})),this.mapFeatures.selectAll("path").attr("fill",(t=>{const e=t.id;return this.stats[e]?this.stats[e].selected_color:"white"})),this.mapFeatures.selectAll("path").each((function(t){const i=e.centroid(t),s=window.innerWidth<768?"translate(-310,-130)":"translate(-175,-60)",n=window.innerWidth<768?"translate(-30,-130)":"translate(-15,-60)";i[0]&&i[1]&&io.selectAll(".programmap__statecentroids").append("image").attr("x",i[0]).attr("y",i[1]).attr("transform",`${"me"===t.id||"ma"===t.id||"al"===t.id||"fl"===t.id||"ga"===t.id||"in"===t.id||"nj"===t.id||"nc"===t.id||"pa"===t.id||"ct"===t.id||"wv"===t.id||"de"===t.id||"dc"===t.id||"ky"===t.id||"mi"===t.id||"ms"===t.id||"nh"===t.id||"ny"===t.id||"oh"===t.id||"tn"===t.id||"vi"===t.id||"sc"===t.id||"vt"===t.id||"la"===t.id||"ri"===t.id||"md"===t.id?s:n}`).attr("fill","transparent").attr("class","programmap__statecentroid").attr("id",`${t.id}-centroid`).attr("data-state-centroid",t.id).attr("xlink:href",""+("me"===t.id||"ma"===t.id||"al"===t.id||"fl"===t.id||"ga"===t.id||"in"===t.id||"nj"===t.id||"nc"===t.id||"pa"===t.id||"ct"===t.id||"wv"===t.id||"de"===t.id||"dc"===t.id||"ky"===t.id||"mi"===t.id||"ms"===t.id||"nh"===t.id||"ny"===t.id||"oh"===t.id||"tn"===t.id||"vi"===t.id||"sc"===t.id||"vt"===t.id||"la"===t.id||"ri"===t.id||"md"===t.id?"/wp-content/themes/vsatwentyfour/images/mapmarkerright.svg":"/wp-content/themes/vsatwentyfour/images/mapmarker.svg")).attr("width",window.innerWidth<768?"350":"200").attr("height",window.innerWidth<768?"160":"61").attr("style","display:none;")})),this.state&&io.select(`#${this.state}-centroid`).attr("style","display:block;"),this.controls=io.selectAll(".programmap__radio").on("change",(t=>{this.selectedOffice=t.target.value,"show-all"!==this.selectedOffice?this.element.classList.add("hide-pointer"):this.element.classList.remove("hide-pointer"),this.updateMap()})),this}updateState(){this.queryString=window.location.search,this.queryParams=new URLSearchParams(this.queryString),this.state=(this.queryParams.get(gt.STATE_PARAM)||"").toLowerCase();const t=this.state;this.mapFeatures.selectAll("path").each((function(e){t===e.id?io.select(`#${t}-centroid`).attr("style","display:block;"):io.select(`#${e.id}-centroid`).attr("style","display:none;")}))}showTooltip(t,e){const i=`<h4 class="programmap__tooltip-title">${t}</h4>`;return this.tooltipContent.classed("programmap__tooltip-content--right",e).html(i),this}handleResize(){const t=window.innerWidth<768?"translate(-310,-130)":"translate(-175,-60)",e=window.innerWidth<768?"translate(-30,-130)":"translate(-15,-60)";this.mapFeatures.selectAll("path").each((function(i){io.select(`#${i.id}-centroid`).attr("width",window.innerWidth<768?"350":"200").attr("height",window.innerWidth<768?"160":"61").attr("transform",`${"me"===i.id||"ma"===i.id||"al"===i.id||"fl"===i.id||"ga"===i.id||"in"===i.id||"nj"===i.id||"nc"===i.id||"pa"===i.id||"ct"===i.id||"wv"===i.id||"de"===i.id||"dc"===i.id||"ky"===i.id||"mi"===i.id||"ms"===i.id||"nh"===i.id||"ny"===i.id||"oh"===i.id||"tn"===i.id||"vi"===i.id||"sc"===i.id||"vt"===i.id||"la"===i.id||"ri"===i.id||"md"===i.id?t:e}`)}))}moveTooltip(t,e){const i=e.properties.name,s=this.map.getBoundingClientRect().width/2>t.offsetX,n=s?t.offsetX+50:t.offsetX-90;return this.tooltip.style("left",`${n}px`).style("top",`${t.offsetY+20}px`),this.showTooltip(i,s),this}hideTooltip(t,e){return this.tooltip.classed("hidden",!0),io.select(e).lower(),this}},StatCounter:class{constructor(t,e){this.element=t,this.ScrollService=e.ScrollService,this.animating=!1,this.animated=!1,this.prepend="",this.init()}init(){return this.cacheDomReferences().setupHandlers().enable(),this}cacheDomReferences(){return this.ticker=this.element.querySelector(".targetstat__stat"),this}setupHandlers(){return this.onScrollHandler=this.onScroll.bind(this),this}enable(){return window.setTimeout(this.onScrollHandler,300),this.ScrollService.addCallback(this.onScrollHandler),this}onScroll(){return this.element.dataset.visible!==gt.TRUE||this.animating||this.animated||this.countUp(),this}countUp(){const t=this.getNumberFromString();return this.animateNumber(t.target,500,t.unit),this}getNumberFromString(){const t=this.ticker.textContent.split("");let e="",i="";const s=/^[0-9.,]+$/;let n=!1;return Array.prototype.forEach.call(t,((t,r)=>{s.test(t)&&!n?(Number.isNaN(parseInt(t,10))&&"."!==t||(e+=t),i=this.ticker.textContent.substring(r+1)):s.test(t)||0!==r?n=!0:this.prepend=t})),""===e&&(e=null),{target:parseFloat(e),unit:i}}animateNumber(t,e,i=""){if(t){this.animating=!0;const s=0;let n=s,r=t,o=!1,a=1,l=0,d=!1;if(t%1!=0)r=100*t,o=!0;else if(1===t.toString().length)r=t,o=!1,d=!0;else{for(let e=t;e>100;e/=10)r=Math.floor(r/10),a*=10;l=Math.abs(r*a-t)}const h=r>s?1:-1,c=(d?200*(n+h):50)*Math.abs(Math.ceil(r/e)),u=setInterval((()=>{n+=h,this.ticker.innerHTML=o?this.prepend+(n/100).toLocaleString()+i:this.prepend+(n*a).toLocaleString()+i,n===r&&(clearInterval(u),l&&(this.ticker.innerHTML=this.prepend+(n*a+l).toLocaleString()+i),this.animating=!1,this.animated=!0)}),c)}return this}},UsVoteStateVotingInformation:class extends Ie{constructor(t){super(),this.element=t,this.init()}init(){return this.cacheDomReferences().setupHandlers().enable(),this}cacheDomReferences(){return this.isDropDownOpen=!1,this.debounce_timeout_ms=100,this.currentListItemFocused=-1,this.bounce=void 0,this.states=[],this.statesKeys={},this.statesNames={},this.statesType={},this.stateIcon="",this.part1=this.element.querySelector(yt.US_VOTE_STATE_VOTING_INFORMATION_TITLE_PART_ONE),this.part2=this.element.querySelector(yt.US_VOTE_STATE_VOTING_INFORMATION_TITLE_PART_TWO),this.headingState=this.element.querySelector(yt.US_VOTE_STATE_VOTING_INFORMATION_HEADING_STATE),this.headingStateText=window.usvoteData.stateSelectedHeading,this.restart=this.element.querySelector(yt.US_VOTE_STATE_VOTING_INFORMATION_RESTART_LINK),this.restartStateText=window.usvoteData.selectAnotherStateLabel,this.restartLocationText=window.usvoteData.selectAnotherLocationLabel,this.stateCrossLink=this.element.querySelector(yt.STATE_CROSS_LINK),this.stateCrossLinkSpan=this.element.querySelector(yt.STATE_CROSS_LINK_TEXT),this.stateCrossLinkText=window.usvoteData.voteAbroadDomTextData,this.stateCrossLinkUrl=window.usvoteData.voteAbroadDomUrlData,this.stateVotingDetails=this.element.querySelector("#state-voting-details"),this.dropdown=this.element.querySelector(yt.STATE_SELECTOR),this.select=this.element.querySelector(yt.STATE_SELECTOR_SELECT),this.input=this.element.querySelector(yt.AUTOCOMPLETE_INPUT),this.resultsList=this.element.querySelector(yt.AUTOCOMPLETE_RESULTS),this.dropdownArrow=this.element.querySelector(yt.AUTOCOMPLETE_DROPDOWN_ARROW),this.comboBox=this.element.querySelector(yt.AUTOCOMPLETE_CONTAINER),this.statesDataField=this.dropdown.dataset.stateField,this.queryString=window.location.search,this.queryParams=new URLSearchParams(this.queryString),this.state=(this.queryParams.get(gt.STATE_PARAM)||"").toLowerCase(),this.state||(this.state=(this.element.dataset.geoState||"").toLowerCase()),this.statesData=window[this.statesDataField]||[],this.validState="",this.validStateLabel="",this.validStateType="state",this.statesData.length>0&&this.statesData.forEach((t=>{this.statesKeys[t.name]=t.slug,this.statesNames[t.slug]=t.name,this.statesType[t.slug]=t.type,this.states.push(t.name),t.prefix&&(this.statesNames[t.slug]=`${t.prefix} ${t.name}`),this.state===t.slug&&(this.validState=this.state,this.validStateLabel=t.name,this.validStateType=t.type,this.stateIcon=this.element.querySelector(`.state--${this.validState}`),this.stateDetails=this.element.querySelector(`.usvotestatevotinginformation__state-voting-details--${this.validState}`))})),this.filteredResults=[...this.states],this.loader=this.element.querySelector(yt.US_VOTE_STATE_VOTING_INFORMATION_LOADER),this}setupHandlers(){return this.openDropdown=this.openDropdown.bind(this),this.closeDropdown=this.closeDropdown.bind(this),this.outsideClickListener=this.outsideClickListener.bind(this),this.filter=this.filter.bind(this),this.debounce=this.debounce.bind(this),this.setResults=this.setResults.bind(this),this.handleKeyboardEvents=this.handleKeyboardEvents.bind(this),this.focusListItem=this.focusListItem.bind(this),this.selectState=this.selectState.bind(this),this.selectChange=this.selectChange.bind(this),this.toggleButton=this.toggleButton.bind(this),this.filterOnInput=this.filterOnInput.bind(this),this.selectResult=this.selectResult.bind(this),this.updateStateHeadingText=this.updateStateHeadingText.bind(this),this.updateRestartText=this.updateRestartText.bind(this),this.updateCrossLink=this.updateCrossLink.bind(this),this.setVotingVotingDetails=this.setVotingVotingDetails.bind(this),this.restartFlow=this.restartFlow.bind(this),this}enable(){return this.setResults(this.states),this.input.addEventListener(vt.KEY_DOWN,this.handleKeyboardEvents),this.resultsList.addEventListener(vt.KEY_DOWN,this.handleKeyboardEvents),this.input.addEventListener(vt.CLICK,this.openDropdown),this.select.addEventListener(vt.CHANGE,this.selectChange),document.addEventListener(vt.CLICK,this.outsideClickListener),this.input.addEventListener(vt.INPUT,this.filterOnInput),this.resultsList.addEventListener(vt.CLICK,this.selectResult),this.dropdownArrow.addEventListener(vt.CLICK,this.toggleButton),this.restart.addEventListener(vt.CLICK,this.restartFlow),this.updateStateHeadingText(),this.updateRestartText(),this.updateCrossLink(),this.setVotingVotingDetails(),this}updateStateHeadingText(){if(this.headingState)if(this.validStateLabel){const t=this.headingStateText.replace("[[state]]",`${this.validStateLabel}`);this.headingState.innerText=t}else this.headingState.innerText=""}updateRestartText(){if(this.restart){const t=("state"!==this.validStateType&&""!==this.validStateType&&this.validStateType?this.restartLocationText:this.restartStateText).replace("[[state]]",`${this.validStateLabel}`);this.restart.innerHTML=t}}updateCrossLink(){if(this.stateCrossLink&&this.stateCrossLinkSpan){const t=this.stateCrossLinkText.replace("[[state]]",`${this.validStateLabel}`);this.stateCrossLinkSpan.innerHTML=t,this.stateCrossLink.href=`${this.stateCrossLinkUrl}?${gt.STATE_PARAM}=${this.state.toUpperCase()}`}}setVotingVotingDetails(){let t=0;this.stateVotingDetails.innerHTML="";const e=window.usvoteData?.stateElectionData,i=this.state?.toLowerCase(),s=e?.[i];if(s){const{noStateElections:e,finalInfoLink:i}=s;if(s?.groupedData&&0!==s?.groupedData?.length)Object.entries(s?.groupedData).forEach((([e,s])=>{const[n,r,o]=e.split("--"),a=new Date(`${o}T22:00:00Z`),l=document.createElement("div");l.className="accordion__wrap usvote__elections";const d=document.createElement("div");d.className="accordion show usvote__election",d.dataset.loadcomponent="Accordion",d.id=`${n}-filter-accordion`;const h=document.createElement("h3");h.className="accordion__title usvote__electiontitle";const c=document.createElement("button");c.className="accordion__button usvote__accordionbutton",c.setAttribute("aria-expanded",0===t?"true":"false"),c.setAttribute("aria-controls",`${n}-accordion-panel`),c.setAttribute("role","button");const u=document.createElement("span");u.className="usvote__name",u.innerHTML=`\n            ${r}\n            <svg xmlns="http://www.w3.org/2000/svg" width="34" height="34" viewBox="0 0 34 34" fill="none">\n              <path d="M0 17H33.9411" stroke="currentColor" stroke-width="8" class="minus"/>\n              <path d="M16.9705 33.9706L16.9705 0.029437" stroke="currentColor" stroke-width="8" class="plus"/>\n            </svg>\n          `;const p=document.createElement("span");p.className="usvote__date",p.textContent=a.toLocaleDateString("en-US",{weekday:"long",month:"long",day:"numeric",year:"numeric"}),c.appendChild(u),c.appendChild(p),h.appendChild(c);const m=document.createElement("div");m.className="accordion__panel usvote__accordionpanel",m.setAttribute("aria-hidden",0===t?"false":"true"),m.setAttribute("style",0===t?"":"height: 0px;"),m.id=`${n}-accordion-panel`;const f=Object.keys(s).length;if(0===f||2===f&&!s.additional_information&&s["----"]){const t=window.usvoteData?.noElectionDeadlines.replace("[[state]]",this.validStateLabel).replace("[[state_election_info_link]]",i),e=document.createElement("p");e.className="usvote__additionalinfo",e.style.marginTop="0",e.innerHTML=`<em>${t}</em>`,m.appendChild(e)}else Object.entries(s).forEach((([t,e])=>{if(!["additional_information","custom","----"].includes(t)){const[,,i]=t.split("--");if(i){const t=document.createElement("div");t.className="usvote__deadline";const s=document.createElement("h4");s.className="usvote__kind",s.innerHTML=Ae.sanitize(i,{ADD_ATTR:["target"]});const n=document.createElement("ul");n.className="usvote__detes",e.forEach((t=>{const e=document.createElement("li");if(e.className="usvote__dete",e.innerHTML=t.note?Ae.sanitize(`${t.date_human_readable} `,{ADD_ATTR:["target"]}):Ae.sanitize(t.date_human_readable,{ADD_ATTR:["target"]}),t.note){const i=document.createElement("div");i.className="tooltip",i.dataset.loadcomponent="Tooltip";const s=document.createElement("button");s.className="tooltip__trigger",s.setAttribute("aria-describedby",`tooltip--${t.date_id}`);const n=document.createElement("span");n.className="tooltip__triggerspan",n.textContent="?",s.appendChild(n);const r=document.createElement("span");r.className="tooltip__content",r.id=`tooltip--${t.date_id}`,r.setAttribute("role","tooltip"),r.setAttribute("aria-hidden","true"),r.innerHTML=t.note,i.appendChild(s),i.appendChild(r),e.appendChild(i)}n.appendChild(e)})),t.appendChild(s),t.appendChild(n),m.appendChild(t)}}}));if(s.additional_information&&!window.usvoteData?.isOverseasDate){const t=document.createElement("p");t.className="usvote__additionalinfo",t.innerHTML=s.additional_information,m.appendChild(t)}if(s.custom){const t=document.createElement("div");t.className="usvote__custom",t.innerHTML=s.custom.custom_election_dates_and_deadlines,m.appendChild(t)}d.appendChild(h),d.appendChild(m),l.appendChild(d),this.stateVotingDetails?.appendChild(l),new Fe(d),t+=1}));else{const t=e.replace("[[state]]",this.validStateLabel).replace("[[state_election_info_link]]",i),s=document.createElement("p");s.className="usvote__additionalinfo",s.innerHTML=t,this.stateVotingDetails?.appendChild(s)}if(s?.content){const t=document.createElement("div");t.innerHTML=s?.content,this.stateVotingDetails?.appendChild(t)}}if(window.usvoteData?.votingInformationSourcingDisclaimer&&window.usvoteData?.votingInformationSourcingDisclaimer?.show_sourcing_disclaimer){const t=document.createElement("div");t.className="usvotestatevotinginformation__info-disclaimer",t.innerHTML=window.usvoteData?.votingInformationSourcingDisclaimer?.disclaimer_text,this.stateVotingDetails?.appendChild(t)}if(window.usvoteData?.votingInformationDisclaimer&&window.usvoteData?.votingInformationDisclaimer?.show_sourcing_disclaimer){const t=window.usvoteData?.votingInformationDisclaimer,e=t?.email_link_text?.email_to||"",i=t?.email_link_text?.email_cc||"",s=encodeURIComponent(t?.email_link_text?.email_subject||""),n=encodeURIComponent((t?.email_link_text?.email_body||"").replace("[[STATE]]",this.validStateLabel)),r=t?.email_link_text?.email_text||"",o=t?.before_email_link_text||"",a=t?.after_email_link_text||"",l=document.createElement("p");l.className="usvotestatevotinginformation__info-disclaimer";const d=document.createElement("a");d.href=`mailto:${e}?cc=${i}&subject=${s}&body=${n}`,d.innerHTML=Ae.sanitize(r,{ADD_ATTR:["target"]}),l.appendChild(document.createTextNode(o)),l.appendChild(d),l.appendChild(document.createTextNode(a)),this.stateVotingDetails?.appendChild(l)}}selectResult(t){return([...this.resultsList.childNodes].includes(t.target)||[...this.resultsList.childNodes].includes(t.target.parentNode))&&this.selectState(t.target),this}filterOnInput(t){const{value:e}=t.target;return this.debounce((()=>{this.filter(e),this.isDropDownOpen||this.openDropdown()})),this}toggleButton(t){return t.preventDefault(),this.isDropDownOpen?this.closeDropdown():this.openDropdown(),this}closeDropdown(){return this.isDropDownOpen=!1,this.resultsList.classList.remove(ft.VISIBLE),this.dropdownArrow.classList.remove(ft.EXPANDED),this.comboBox.setAttribute(mt.EXPANDED,gt.FALSE),this.input.setAttribute(mt.ACTIVE_DESCENDANT,""),this}openDropdown(){return this.isDropDownOpen=!0,this.resultsList.classList.add(ft.VISIBLE),this.dropdownArrow.classList.add(ft.EXPANDED),this.comboBox.setAttribute(mt.EXPANDED,gt.TRUE),this}outsideClickListener(t){[this.input,this.dropdownArrow,...this.resultsList.childNodes].includes(t.target)||this.closeDropdown()}filter(t){this.filteredResults=t?this.states.filter((e=>e.toLowerCase().includes(t.toLowerCase()))):[...this.states],this.setResults(this.filteredResults)}setResults(t){if(Array.isArray(t)&&t.length>0){const e=t.map(((t,e)=>`<li class="autocomplete-item" id="autocomplete-item-${e}" role="listitem" tabindex="0"><span>${t}</span></li>`)).join("");this.resultsList.innerHTML=e,this.currentListItemFocused=-1}}debounce(t){clearTimeout(this.bounce),this.bounce=setTimeout((()=>{t()}),[this.debounce_timeout_ms])}focusListItem(t){const{id:e}=t;this.input.setAttribute(mt.ACTIVE_DESCENDANT,e),t.focus()}updateQueryParams(){return window.history.pushState(null,null,`?${this.queryParams.toString()}`),window.dispatchEvent(new CustomEvent(vt.PUSH_STATE,{detail:{vsa_state:this.state}})),this}selectChange(t){const{value:e}=t.target,i=this.statesNames[e];return this.input.value=e,e&&i&&(this.state=e.toLowerCase(),this.validState=this.state,this.validStateLabel=i,this.validStateType=this.statesType[this.state],this.queryParams.set(gt.STATE_PARAM,this.state.toUpperCase()),this.updateQueryParams(),this.updateStateHeadingText(),this.updateRestartText(),this.updateCrossLink(),this.setVotingVotingDetails(),this.element.classList.add(ft.US_VOTE_STATE_VOTING_INFORMATION_STATE),this.part1.classList.remove(ft.SHOW),this.part2.classList.add(ft.SHOW),this.stateIcon=this.element.querySelector(`.state--${this.state}`),this.stateIcon&&this.stateIcon.classList.add(ft.SHOW),this.stateDetails=this.element.querySelector(`.usvotestatevotinginformation__state-voting-details--${this.state}`),this.stateDetails&&this.stateDetails.classList.add(ft.SHOW),this.part2.scrollIntoView({block:"start",inline:"nearest"})),this}selectState(t){const e=t.innerText,i=this.statesKeys[e];return this.input.value=e,this.input.removeAttribute(mt.ACTIVE_DESCENDANT),t.setAttribute(mt.SELECTED,gt.TRUE),this.input.focus(),this.closeDropdown(),e&&i&&(this.state=i.toLowerCase(),this.validState=this.state,this.validStateLabel=this.statesNames[this.state],this.validStateType=this.statesType[this.state],this.select.value=i,this.queryParams.set(gt.STATE_PARAM,this.state.toUpperCase()),this.updateQueryParams(),this.updateStateHeadingText(),this.updateCrossLink(),this.updateRestartText(),this.setVotingVotingDetails(),this.element.classList.add(ft.US_VOTE_STATE_VOTING_INFORMATION_STATE),this.part1.classList.remove(ft.SHOW),this.part2.classList.add(ft.SHOW),this.stateIcon=this.element.querySelector(`.state--${this.state}`),this.stateIcon&&this.stateIcon.classList.add(ft.SHOW),this.stateDetails=this.element.querySelector(`.usvotestatevotinginformation__state-voting-details--${this.state}`),this.stateDetails&&this.stateDetails.classList.add(ft.SHOW)),this}restartFlow(t){return t.preventDefault(),this.part1.classList.add(ft.SHOW),this.part2.classList.remove(ft.SHOW),this.element.classList.remove(ft.US_VOTE_STATE_VOTING_INFORMATION_STATE),this.stateIcon&&this.stateIcon.classList.remove(ft.SHOW),this.stateDetails&&this.stateDetails.classList.remove(ft.SHOW),this.queryParams.delete(gt.STATE_PARAM),this.input.value="",this.select.value="",this.setResults(this.states),this.updateQueryParams(),this}handleKeyboardEvents(t){const e=this.resultsList.childNodes;let i=null;switch([vt.ARROW_UP,vt.ARROW_DOWN,vt.ENTER].includes(t.key)&&t.preventDefault(),t.key){case vt.ARROW_DOWN:this.currentListItemFocused<e.length-1&&(this.isDropDownOpen||this.openDropdown(),this.currentListItemFocused+=1,i=e.item(this.currentListItemFocused),this.focusListItem(i));break;case vt.ARROW_UP:this.currentListItemFocused>0&&(this.currentListItemFocused-=1,i=e.item(this.currentListItemFocused),this.focusListItem(i));break;case"Home":this.currentListItemFocused>0&&(this.currentListItemFocused=0,i=e.item(this.currentListItemFocused),this.focusListItem(i));break;case"End":this.currentListItemFocused<e.length-1&&(this.currentListItemFocused=e.length-1,i=e.item(this.currentListItemFocused),this.focusListItem(i));break;case vt.ENTER:this.isDropDownOpen?e[this.currentListItemFocused].innerText&&this.selectState(e[this.currentListItemFocused]):this.openDropdown();break;case"Escape":this.isDropDownOpen&&this.closeDropdown();break;default:t.target!==this.input&&/([a-zA-Z0-9_]|ArrowLeft|ArrowRight)/.test(t.key)&&this.input.focus()}return this}},TakeAction:class extends Ie{constructor(t){super(),this.element=t,this.requestRoot="https://api.mobilize.us/v1/organizations/",this.requestEnd="/events?exclude_full=true",this.highPriorityParam="high_priority_only",this.eventTypesParam="event_types",this.organizationIdParam="organization_id",this.perPageParem="per_page",this.isVirtualParam="is_virtual",this.timeslotStartParam="timeslot_start",this.excludeFullParam="exclude_full",this.allEvents=[],this.regionalEvents=[],this.iniStartDate=new Date,this.formatDate={weekday:"short",month:"long",day:"numeric",timeZoneName:"short",hour:"numeric",minute:"2-digit"},this.finalRequest="",this.init()}init(){return this.cacheDomReferences().setupHandlers().enable(),this}cacheDomReferences(){return this.isDropDownOpen=!1,this.debounce_timeout_ms=100,this.currentListItemFocused=-1,this.bounce=void 0,this.states=[],this.statesKeys={},this.statesNames={},this.orgid=this.element.dataset.mobilizeOrg||206,this.sourceOne=this.element.dataset.sourceOne||"web-take-action-1",this.sourceTwo=this.element.dataset.sourceTwo||"web-take-action-2",this.numberOfEvents=this.element.dataset.numberEvents||100,this.nationalFallback=!1,this.stateToOrgs=window.take_action_state_to_orgs,this.eventToEvents=window.take_action_event_to_events,this.electionToOrgs=window.take_action_election_to_orgs,this.stateToHighPriority=window.take_action_state_to_high_priority,this.stateToHighPriorityAlways=window.take_action_state_to_high_priority_always,this.accordion=this.element.querySelector(yt.ACCORDION),this.pinnedEvents=window.take_action_state_to_pinned_events,this.formResp="",Object.keys(this.eventToEvents).length>0&&Object.keys(this.eventToEvents).forEach((t=>{const e=this.eventToEvents[t];e[gt.INCLUDE_EMAIL_SIGNUP]&&(this.formResp=this.element.querySelector(`#${e.shortened}`))})),this.part1=this.element.querySelector(yt.TAKEACTION_TITLE_PART_ONE),this.part2=this.element.querySelector(yt.TAKEACTION_TITLE_PART_TWO),this.headingState=this.element.querySelector(yt.TAKEACTION_HEADING_STATE),this.headingStateText=this.headingState.dataset.stateText,this.restart=this.element.querySelector(yt.TAKEACTION_RESTART),this.clear=this.element.querySelector(yt.TAKEACTION_CLEAR),this.appliedFilters=this.element.querySelector(yt.TAKEACTION_APPLIED_FILTERS),this.eventAppliedFilter=this.element.querySelector(yt.TAKEACTION_APPLIED_FILTER_EVENT),this.eventAppliedFilterBefore=this.eventAppliedFilter?this.eventAppliedFilter.dataset.filterText:"Than can ",this.electionAppliedFilter=this.element.querySelector(yt.TAKEACTION_APPLIED_FILTER_ELECTION),this.electionAppliedFilterBefore=this.electionAppliedFilter?this.electionAppliedFilter.dataset.filterText:"Related to ",this.filtersForm=this.element.querySelector(yt.TAKEACTION_FILTERS_FORM),this.eventCheckboxes=this.element.querySelectorAll(`input[name="${gt.EVENT_TYPE}"]`),this.electionCheckboxes=this.element.querySelectorAll(`input[name="${gt.ELECTION_TYPE}"]`),this.dropdown=this.element.querySelector(yt.STATE_SELECTOR),this.select=this.element.querySelector(yt.STATE_SELECTOR_SELECT),this.input=this.element.querySelector(yt.AUTOCOMPLETE_INPUT),this.resultsList=this.element.querySelector(yt.AUTOCOMPLETE_RESULTS),this.dropdownArrow=this.element.querySelector(yt.AUTOCOMPLETE_DROPDOWN_ARROW),this.comboBox=this.element.querySelector(yt.AUTOCOMPLETE_CONTAINER),this.fullRequest=`${this.requestRoot}${this.orgid}${this.requestEnd}&per_page=${this.numberOfEvents}`,this.statesDataField=this.dropdown.dataset.stateField,this.queryString=window.location.search,this.queryParams=new URLSearchParams(this.queryString),this.state=(this.queryParams.get(gt.STATE_PARAM)||"").toLowerCase(),this.state||(this.state=(this.element.dataset.geoState||"").toLowerCase()),this.statesData=window[this.statesDataField]||[],this.validState="",this.statesData.length>0&&this.statesData.forEach((t=>{this.statesKeys[t.name]=t.slug,this.statesNames[t.slug]=t.name,this.states.push(t.name),t.prefix&&(this.statesNames[t.slug]=`${t.prefix} ${t.name}`),this.state===t.slug&&(this.validState=this.state)})),this.filteredResults=[...this.states],this.eventsSelected=this.queryParams.getAll(gt.EVENT_PARAM)||"",this.electionsSelected=this.queryParams.getAll(gt.ELECTION_PARAM)||"",this.mobilizeResults=this.element.querySelector(yt.TAKEACTION_RESULTS),this.noResults=this.element.querySelector(yt.TAKEACTION_NO_RESULTS),this.eventList=this.element.querySelector(yt.TAKEACTION_EVENT_LIST),this.loader=this.element.querySelector(yt.TAKEACTION_LOADER),this}setupHandlers(){return this.openDropdown=this.openDropdown.bind(this),this.closeDropdown=this.closeDropdown.bind(this),this.outsideClickListener=this.outsideClickListener.bind(this),this.filter=this.filter.bind(this),this.debounce=this.debounce.bind(this),this.setResults=this.setResults.bind(this),this.handleKeyboardEvents=this.handleKeyboardEvents.bind(this),this.focusListItem=this.focusListItem.bind(this),this.selectState=this.selectState.bind(this),this.selectChange=this.selectChange.bind(this),this.toggleButton=this.toggleButton.bind(this),this.filterOnInput=this.filterOnInput.bind(this),this.selectResult=this.selectResult.bind(this),this.restartFlow=this.restartFlow.bind(this),this.clearFilters=this.clearFilters.bind(this),this.onFormSubmit=this.onFormSubmit.bind(this),this.buildRequest=this.buildRequest.bind(this),this.fetchEvents=this.fetchEvents.bind(this),this.renderEvents=this.renderEvents.bind(this),this.updateFromParams=this.updateFromParams.bind(this),this.updateAppliedFilters=this.updateAppliedFilters.bind(this),this.updateQueryParams=this.updateQueryParams.bind(this),this.makeStateRequest=this.makeStateRequest.bind(this),this.updateFromParams({onLoad:!0}),this}enable(){return this.setResults(this.states),this.input.addEventListener(vt.KEY_DOWN,this.handleKeyboardEvents),this.resultsList.addEventListener(vt.KEY_DOWN,this.handleKeyboardEvents),this.input.addEventListener(vt.CLICK,this.openDropdown),this.select.addEventListener(vt.CHANGE,this.selectChange),document.addEventListener(vt.CLICK,this.outsideClickListener),this.input.addEventListener(vt.INPUT,this.filterOnInput),this.resultsList.addEventListener(vt.CLICK,this.selectResult),this.dropdownArrow.addEventListener(vt.CLICK,this.toggleButton),this.restart.addEventListener(vt.CLICK,this.restartFlow),this.clear.addEventListener(vt.CLICK,this.clearFilters),this.filtersForm.addEventListener(vt.SUBMIT,this.onFormSubmit),this}selectResult(t){return([...this.resultsList.childNodes].includes(t.target)||[...this.resultsList.childNodes].includes(t.target.parentNode))&&this.selectState(t.target),this}filterOnInput(t){const{value:e}=t.target;return this.isDropDownOpen||this.openDropdown(),this.filter(e),this.debounce((()=>{this.filter(e),this.isDropDownOpen||this.openDropdown()})),this}toggleButton(t){return t.preventDefault(),this.isDropDownOpen?this.closeDropdown():this.openDropdown(),this}updateFromParams({onLoad:t}){this.state=(this.queryParams.get(gt.STATE_PARAM)||"").toLowerCase(),!this.state&&t&&(this.state=(this.element.dataset.geoState||"").toLowerCase()),this.eventsSelected=this.queryParams.getAll(gt.EVENT_PARAM)||"",this.electionsSelected=this.queryParams.getAll(gt.ELECTION_PARAM)||"",this.eventTypes=[],this.electionOrgs=[],this.isVirtual=[],this.stateHighPriority=!1,this.stateHighPriorityAlways=!1,this.nationalFallback=!1;let e="";if(this.state.length>0&&this.stateToOrgs[this.state]?(this.stateOrgs=this.stateToOrgs[this.state]?.organization_id,this.stateHighPriority=this.stateToHighPriority[this.state],this.stateHighPriorityAlways=this.stateToHighPriorityAlways[this.state]):this.state.length>0&&this.stateToOrgs?.national?.organization_id?(this.stateOrgs=this.stateToOrgs?.national?.organization_id,this.stateHighPriority=this.stateToHighPriority.national,this.stateHighPriorityAlways=this.stateToHighPriorityAlways.national,this.nationalFallback=!0,0===this.eventsSelected.length&&this.isVirtual.push(!0)):this.stateOrgs=[],this.eventsSelected.length>0&&this.eventsSelected.forEach(((t,i)=>{this.eventToEvents[t]&&(this.eventToEvents[t][gt.INCLUDE_EMAIL_SIGNUP]?this.formResp&&this.formResp.classList.add(ft.SHOW):"no"===this.eventToEvents[t].is_virtual?(this.eventTypes.push(this.eventToEvents[t].event_types),this.isVirtual.push(!1)):"yes"===this.eventToEvents[t].is_virtual?(this.eventTypes.push(this.eventToEvents[t].event_types),this.isVirtual.push(!0)):(this.eventTypes.push(this.eventToEvents[t].event_types),this.isVirtual.push(!0),this.isVirtual.push(!1))),e=`${e}<li class="${gt.EVENT_TYPE}>${this.eventsSelected.length>1&&i+1<this.eventsSelected.length?", ":""}</li>`,this.eventAppliedFilter.innerHTML=`${this.eventAppliedFilterBefore} <ul>${e}</ul>`,this.eventAppliedFilter.classList.add(ft.SHOW)})),this.electionsSelected.length>0&&this.electionsSelected.forEach((t=>{if(this.electionToOrgs[t]){const e=this.electionToOrgs[t]?.organization_id;e.forEach((t=>{this.stateOrgs.includes(t)&&this.electionOrgs.push(t)}))}})),this.isVirtual=[...new Set(this.isVirtual)],this.isVirtual.includes(!0)&&this.isVirtual.includes(!1)?this.isVirtual="":this.isVirtual.includes(!0)?this.isVirtual=!0:this.isVirtual.includes(!1)&&(this.isVirtual=!1),this.eventTypes=this.eventTypes.flat(),this.eventCheckboxes.length>0&&this.eventCheckboxes.forEach(((t,e)=>{const i=t.getAttribute("value");this.eventsSelected.includes(i)?this.eventCheckboxes[e].checked=!0:this.eventCheckboxes[e].checked=!1})),this.electionCheckboxes.length>0&&this.electionCheckboxes.forEach(((t,e)=>{const i=t.getAttribute("value");this.electionsSelected.includes(i)?this.electionCheckboxes[e].checked=!0:this.electionCheckboxes[e].checked=!1})),this.validState.length>0){const t={mobilizeOrganizations:this.stateOrgs,isVirtual:this.isVirtual,highPriorityOnly:this.stateHighPriority,highPriorityOnlyAlways:this.stateHighPriorityAlways,iniMobilizeOrganizations:this.stateOrgs};this.eventTypes.length>0&&(t.eventTypes=this.eventTypes),this.electionsSelected.length>0&&(t.mobilizeOrganizations=this.electionOrgs),(this.eventsSelected.length>0||this.electionsSelected.length>0)&&(t.final=!0,this.accordion.dispatchEvent(new CustomEvent(vt.FILTER_FORM_SUBMIT)),this.mobilizeResults.classList.add(ft.FINAL)),this.buildRequest(t)}return this.updateAppliedFilters(),this}closeDropdown(){return this.isDropDownOpen=!1,this.resultsList.classList.remove(ft.VISIBLE),this.dropdownArrow.classList.remove(ft.EXPANDED),this.comboBox.setAttribute(mt.EXPANDED,gt.FALSE),this.input.setAttribute(mt.ACTIVE_DESCENDANT,""),this}openDropdown(){return this.isDropDownOpen=!0,this.resultsList.classList.add(ft.VISIBLE),this.dropdownArrow.classList.add(ft.EXPANDED),this.comboBox.setAttribute(mt.EXPANDED,gt.TRUE),this}outsideClickListener(t){return[this.input,this.dropdownArrow,...this.resultsList.childNodes].includes(t.target)||this.closeDropdown(),this}filter(t){return this.filteredResults=t?this.states.filter((e=>e.toLowerCase().includes(t.toLowerCase()))):[...this.states],this.setResults(this.filteredResults),this}setResults(t){if(Array.isArray(t)&&t.length>0){const e=t.map(((t,e)=>`<li class="autocomplete-item" id="autocomplete-item-${e}" role="listitem" tabindex="0"><span>${t}</span></li>`)).join("");this.resultsList.innerHTML=e,this.currentListItemFocused=-1}}debounce(t){return clearTimeout(this.bounce),this.bounce=setTimeout((()=>{t()}),[this.debounce_timeout_ms]),this}focusListItem(t){const{id:e}=t;this.input.setAttribute(mt.ACTIVE_DESCENDANT,e),t.focus()}updateQueryParams(){return window.history.pushState(null,null,`?${this.queryParams.toString()}`),window.dispatchEvent(new CustomEvent(vt.PUSH_STATE,{detail:{vsa_state:this.state}})),this}selectChange(t){const{value:e}=t.target,i=this.statesNames[e];if(this.input.value=e,this.nationalFallback=!1,e&&i){this.state=e.toLowerCase(),this.validState=this.state,this.clear.href=`${window.location.pathname}?${gt.STATE_PARAM}=${this.state}`,this.queryParams.set(gt.STATE_PARAM,this.state.toUpperCase()),this.updateQueryParams();const t=this.headingStateText.replace("[[state]]",`<em>${i}</em>`);this.headingState.innerHTML=t,this.part1.classList.remove(ft.SHOW),this.part2.classList.add(ft.SHOW),this.mobilizeResults.classList.add(ft.SHOW),this.makeStateRequest(e)}return this}selectState(t){const e=t.innerText,i=this.statesKeys[e];if(this.input.value=e,this.input.removeAttribute(mt.ACTIVE_DESCENDANT),t.setAttribute(mt.SELECTED,gt.TRUE),this.input.focus(),this.closeDropdown(),this.nationalFallback=!1,e&&i){this.state=i.toLowerCase(),this.validState=this.state,this.select.value=i,this.clear.href=`${window.location.pathname}?${gt.STATE_PARAM}=${this.state}`,this.queryParams.set(gt.STATE_PARAM,this.state.toUpperCase()),this.updateQueryParams();const t=this.headingStateText.replace("[[state]]",`<em>${this.statesNames[this.state]}</em>`);this.headingState.innerHTML=t,this.part1.classList.remove(ft.SHOW),this.part2.classList.add(ft.SHOW),this.mobilizeResults.classList.add(ft.SHOW),this.makeStateRequest(i)}return this}makeStateRequest(t){return t&&this.stateToOrgs[t]?(this.stateOrgs=this.stateToOrgs[t]?.organization_id,this.stateHighPriority=this.stateToHighPriority[t],this.stateHighPriorityAlways=this.stateToHighPriorityAlways[this.state],this.buildRequest({mobilizeOrganizations:this.stateToOrgs[t]?.organization_id,highPriorityOnly:this.stateHighPriority,highPriorityOnlyAlways:this.stateHighPriorityAlways,iniMobilizeOrganizations:this.stateToOrgs[t]?.organization_id})):this.stateToOrgs?.national?.organization_id?(this.stateOrgs=this.stateToOrgs?.national?.organization_id,this.nationalFallback=!0,this.stateHighPriority=this.stateToHighPriority.national,this.stateHighPriorityAlways=this.stateToHighPriorityAlways.national,this.buildRequest({mobilizeOrganizations:this.stateToOrgs?.national?.organization_id,highPriorityOnly:this.stateHighPriority,highPriorityOnlyAlways:this.stateHighPriorityAlways,isVirtual:!0,iniMobilizeOrganizations:this.stateToOrgs?.national?.organization_id})):this.stateOrgs=[],this}restartFlow(t){return t.preventDefault(),this.part1.classList.add(ft.SHOW),this.part2.classList.remove(ft.SHOW),this.queryParams.delete(gt.STATE_PARAM),this.queryParams.delete(gt.EVENT_PARAM),this.queryParams.delete(gt.ELECTION_PARAM),this.mobilizeResults.classList.remove(ft.SHOW),this.noResults.classList.remove(ft.SHOW),this.input.value="",this.select.value="",this.eventCheckboxes.length>0&&this.eventCheckboxes.forEach(((t,e)=>{this.eventCheckboxes[e].checked=!1})),this.electionCheckboxes.length>0&&this.electionCheckboxes.forEach(((t,e)=>{this.electionCheckboxes[e].checked=!1})),this.eventsSelected=[],this.electionsSelected=[],this.updateAppliedFilters(),this.eventList.innerHTML="",this.updateQueryParams(),this.setResults(this.states),this.formResp&&this.formResp.classList.remove(ft.SHOW),this.mobilizeResults.classList.remove(ft.FINAL),this}clearFilters(t){return t.preventDefault(),this.queryParams.delete(gt.EVENT_PARAM),this.queryParams.delete(gt.ELECTION_PARAM),this.noResults.classList.remove(ft.SHOW),this.eventCheckboxes.length>0&&this.eventCheckboxes.forEach(((t,e)=>{this.eventCheckboxes[e].checked=!1})),this.electionCheckboxes>0&&this.electionCheckboxes.forEach(((t,e)=>{this.electionCheckboxes[e].checked=!1})),this.eventsSelected=[],this.electionsSelected=[],this.updateAppliedFilters(),this.eventList.innerHTML="",this.updateQueryParams(),this.updateFromParams({onLoad:!1}),this.formResp&&this.formResp.classList.remove(ft.SHOW),this.mobilizeResults.classList.remove(ft.FINAL),this}buildRequest({mobilizeOrganizations:t=[],eventTypes:e=[],isVirtual:i="",highPriorityOnly:s=!1,highPriorityOnlyAlways:n=!1,final:r=!1,iniMobilizeOrganizations:o=[]}){const a=n||!r&&s;this.nearestFifteen=parseInt((new Date(9e5*Math.ceil((new Date).getTime()/9e5)).getTime()/1e3).toFixed(0),10);let l="";t.length>0&&(l=`&${this.organizationIdParam}=${t.join(`&${this.organizationIdParam}=`)}`);let d="";e.length>0&&(d=`&${this.eventTypesParam}=${e.join(`&${this.eventTypesParam}=`)}`);let h="";return!0===i?h=`&${this.isVirtualParam}=true`:!1===i&&(h=`&${this.isVirtualParam}=false`),this.finalRequest=`${this.requestRoot}${this.orgid}${this.requestEnd}&per_page=${this.numberOfEvents||9}&${this.highPriorityParam}=${a}${l||""}${d||""}${h||""}`,this.finalRequest=`${this.finalRequest}&timeslot_start=gte_${this.nearestFifteen}`,this.part1.classList.remove(ft.SHOW),this.part2.classList.add(ft.SHOW),this.fetchEvents(this.finalRequest,r,i,l,o),this}fetchEvents(t,e,i,s,n){return this.loader.classList.add(ft.SHOW),this.eventList.classList.remove(ft.SHOW),fetch(t).then((t=>t.json())).then((t=>{const r=t.data.filter((t=>(t.location.region||"").toLowerCase()===this.state)),o=t.data.filter((t=>{const e=(t.location.region||"").toLowerCase(),i=t.is_virtual;return(t.sponsor&&t.sponsor.id)===parseInt(this.orgid,10)?""===e:""===e||i&&e!==this.state}));if(""===s&&0===!n.length&&e)this.renderEvents(r.slice(0,0),e);else if(r.length>0){const i=e?9:4;if(r.length<i){const s=t.data.filter((t=>{const e=(t.location.region||"").toLowerCase(),i=t.is_virtual;return(t.sponsor&&t.sponsor.id)===parseInt(this.orgid,10)?""===e:""===e||i&&e!==this.state})),n=i-r.length,o=s.slice(0,n),a=r.concat(o);this.renderEvents(a,e)}else this.renderEvents(r.slice(0,e?9:4),e)}else if(0===r.length&&this.nationalFallback&&!1===i)this.renderEvents(r.slice(0,0),e);else if(0===r.length&&this.nationalFallback&&!0===i&&o.length>0)this.renderEvents(o.slice(0,e?9:4),e);else{const t=e?9:4,i=t-r.length,s=o.slice(0,i),n=r.concat(s);this.renderEvents(n.slice(0,t),e)}e?this.appliedFilters.scrollIntoView({block:"center",inline:"nearest"}):this.part2.scrollIntoView({block:"start",inline:"nearest"})})).catch((t=>{this.renderEvents([],e)})),this}renderEvents(t,e){let i="";const s=/[a-z]/i;let n=[];e||(this.nationalFallback&&this.pinnedEvents.national&&(n=this.pinnedEvents.national),this.state&&this.pinnedEvents[this.state]&&(n=this.pinnedEvents[this.state]),0!==n.length&&n.forEach((t=>{const{event_date_text:e,event_hosted_by:s,event_location:n,event_image:r,event_title:o,event_link:a}=t,l=`?utm_source=${this.sourceOne}`,d=s?`<span>Hosted by ${s}</span>`:"",h=n?`<span>${n}</span>`:"";i+=`<li class="mobilizeevent mobilizeevent--horizontal">\n              ${r&&`<figure class="mobilizeevent__fig"><img class="mobilizeevent__img" src="${r}"></img></figure>`}\n              <div class="mobilizeevent__content">\n              <span class="mobilizeevent__date">${`${e}`}</span>\n              <h3 class="mobilizeevent__name">${o}</h3>\n              ${`<p class="mobilizeevent__det">${d}${d&&h?" | ":""}${h}</p>`}\n              <a class="mobilizeevent__link" href="${a}${l}" target="_blank" rel="noopener noreferrer"><span class="emphasizedlink" style="--text-color: #ffbf10; --text-hover-color: #F65137;"><span class="emphasizedlink__text">See details</span><svg class="emphasizedlink__svg emphasizedlink__svg--one" width="10" height="14" viewBox="0 0 10 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2 2L7 7L2 12" stroke="currentColor" stroke-width="4"/></svg><svg class="emphasizedlink__svg emphasizedlink__svg--two" width="10" height="14" viewBox="0 0 10 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2 2L7 7L2 12" stroke="currentColor" stroke-width="4"/></svg></span></a>\n              </div>\n            </li>\n          `}))),t.length>0||n.length>0&&!e?(t.forEach(((t,r)=>{const o=n.some((e=>e.event_link.includes(t.browser_url)));if(e||!o&&!e&&n.length+r+1<=6){const{sponsor:n={},location:r={},timeslots:o=[],timezone:a,featured_image_url:l}=t,d=Math.round(this.iniStartDate.getTime()/1e3),h=o.filter((t=>t.end_date>d)),c=`?utm_source=${e?this.sourceTwo:this.sourceOne}`,u=(h&&h.length)>0&&h[0].start_date,p=r?`${r.locality}${r.locality&&r.region?", ":""}${r.region}`:"",m=l||n.logo_url,f=l?"mobilizeevent__img":"mobilizeevent__img mobilizeevent__img--logo",v=new Date(1e3*u).toLocaleString("en-US",{timeZone:a,...this.formatDate}),g=h.length>1?` + ${h.length-1} more available time${h.length-1>1?"s":""}`:"",_=`<p class="mobilizeevent__det">${t.created_by_volunteer_host?`<span>Volunteer organized for ${n.name}</span>`:`<span>Hosted by ${n.name}</span>`} | ${t.is_virtual?"<span>Virtual"+(p&&!s.test(p.length)&&"null"!==p?` · Hosted from ${p}`:""):`${p&&`<span>${p}</span>`}`}</p>`;i+=`\n            <li class="mobilizeevent mobilizeevent--horizontal">\n              ${m&&`<figure class="mobilizeevent__fig"><img class="${f}" src="${m}"></img></figure>`}\n              <div class="mobilizeevent__content">\n              <span class="mobilizeevent__date">${v}${g}</span>\n              <h3 class="mobilizeevent__name">${t.title}</h3>\n              ${_}\n              <a class="mobilizeevent__link" href="${t.browser_url}${c}" target="_blank" rel="noopener noreferrer"><span class="emphasizedlink" style="--text-color: #ffbf10; --text-hover-color: #F65137;"><span class="emphasizedlink__text">See details</span><svg class="emphasizedlink__svg emphasizedlink__svg--one" width="10" height="14" viewBox="0 0 10 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2 2L7 7L2 12" stroke="currentColor" stroke-width="4"/></svg><svg class="emphasizedlink__svg emphasizedlink__svg--two" width="10" height="14" viewBox="0 0 10 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2 2L7 7L2 12" stroke="currentColor" stroke-width="4"/></svg></span></a>\n              </div>\n            </li>\n          `}})),this.eventList.innerHTML=i,this.eventList.classList.add(ft.SHOW),this.mobilizeResults.classList.add(ft.SHOW),this.noResults.classList.remove(ft.SHOW),this.loader.classList.remove(ft.SHOW)):(this.eventList.classList.add(ft.SHOW),this.mobilizeResults.classList.remove(ft.SHOW),this.noResults.classList.add(ft.SHOW),this.loader.classList.remove(ft.SHOW))}handleKeyboardEvents(t){const e=this.resultsList.childNodes;let i=null;switch([vt.ARROW_UP,vt.ARROW_DOWN,vt.ENTER].includes(t.key)&&t.preventDefault(),t.key){case vt.ARROW_DOWN:this.currentListItemFocused<e.length-1&&(this.isDropDownOpen||this.openDropdown(),this.currentListItemFocused+=1,i=e.item(this.currentListItemFocused),this.focusListItem(i));break;case vt.ARROW_UP:this.currentListItemFocused>0&&(this.currentListItemFocused-=1,i=e.item(this.currentListItemFocused),this.focusListItem(i));break;case"Home":this.currentListItemFocused>0&&(this.currentListItemFocused=0,i=e.item(this.currentListItemFocused),this.focusListItem(i));break;case"End":this.currentListItemFocused<e.length-1&&(this.currentListItemFocused=e.length-1,i=e.item(this.currentListItemFocused),this.focusListItem(i));break;case vt.ENTER:this.isDropDownOpen?e[this.currentListItemFocused].innerText&&this.selectState(e[this.currentListItemFocused]):this.openDropdown();break;case"Escape":this.isDropDownOpen&&this.closeDropdown();break;default:t.target!==this.input&&/([a-zA-Z0-9_]|ArrowLeft|ArrowRight)/.test(t.key)&&this.input.focus()}return this}onFormSubmit(t){t.preventDefault();const e=new FormData(this.filtersForm);this.eventsSelected=e.getAll(gt.EVENT_TYPE),this.electionsSelected=e.getAll(gt.ELECTION_TYPE),this.queryParams.delete(gt.EVENT_PARAM),this.queryParams.delete(gt.ELECTION_PARAM),this.eventTypes=[],this.electionOrgs=[],this.isVirtual=[],this.eventsSelected.length>0&&this.eventsSelected.forEach((t=>{this.eventToEvents[t]&&(this.eventToEvents[t][gt.INCLUDE_EMAIL_SIGNUP]?this.formResp&&this.formResp.classList.add(ft.SHOW):(this.eventTypes.push(this.eventToEvents[t].event_types),"no"===this.eventToEvents[t].is_virtual?this.isVirtual.push(!1):"yes"===this.eventToEvents[t].is_virtual?this.isVirtual.push(!0):(this.isVirtual.push(!0),this.isVirtual.push(!1)))),this.queryParams.append(gt.EVENT_PARAM,t)})),this.electionsSelected.length>0&&this.electionsSelected.forEach((t=>{if(this.electionToOrgs[t]){const e=this.electionToOrgs[t]?.organization_id;e.forEach((t=>{this.stateOrgs.includes(t)&&this.electionOrgs.push(t)}))}this.queryParams.append(gt.ELECTION_PARAM,t)})),this.isVirtual=[...new Set(this.isVirtual)],this.isVirtual.includes(!0)&&this.isVirtual.includes(!1)?this.isVirtual="":this.isVirtual.includes(!0)?this.isVirtual=!0:this.isVirtual.includes(!1)&&(this.isVirtual=!1),this.eventTypes=this.eventTypes.flat(),this.updateQueryParams();const i={mobilizeOrganizations:this.stateOrgs,isVirtual:this.isVirtual,highPriorityOnly:this.stateHighPriority,highPriorityOnlyAlways:this.stateHighPriorityAlways,final:!0};return this.eventTypes.length>0&&(i.eventTypes=this.eventTypes),this.electionsSelected.length>0&&(i.mobilizeOrganizations=this.electionOrgs),this.buildRequest(i),this.updateAppliedFilters(),this.accordion.dispatchEvent(new CustomEvent(vt.FILTER_FORM_SUBMIT)),this.mobilizeResults.classList.add(ft.FINAL),this}updateAppliedFilters(){let t="",e="";const i=window.location.pathname,s=window.location.search;return this.eventAppliedFilter&&(this.eventsSelected.length>0?this.eventsSelected.forEach(((e,n)=>{const r=new URLSearchParams(s);r.delete(gt.EVENT_PARAM,e),this.eventToEvents[e]&&this.eventToEvents[e].applied_filter_label&&(t=`${t}<li class="${gt.EVENT_TYPE}"><a href="${i}?${r.toString()}">${this.eventToEvents[e].applied_filter_label} (X)${this.eventsSelected.length>1&&n+1<this.eventsSelected.length?",</a> "+(n+2===this.eventsSelected.length?"and ":""):"</a>"}</li>`),this.eventAppliedFilter.innerHTML=`${this.eventAppliedFilterBefore} <ul>${t}.</ul>`,this.eventAppliedFilter.classList.add(ft.SHOW)})):(this.eventAppliedFilter.innerHTML="",this.eventAppliedFilter.classList.remove(ft.SHOW))),this.electionAppliedFilter&&(this.electionsSelected.length>0?this.electionsSelected.forEach(((t,n)=>{const r=new URLSearchParams(s);r.delete(gt.ELECTION_PARAM,t),this.electionToOrgs[t]&&this.electionToOrgs[t].applied_filter_label&&(e=`${e}<li class="${gt.ELECTION_TYPE}"><a href="${i}?${r.toString()}">${this.electionToOrgs[t].applied_filter_label} (X)${this.electionsSelected.length>1&&n+1<this.electionsSelected.length?",</a> "+(n+2===this.electionsSelected.length?"and ":""):"</a>"}</li>`),this.electionAppliedFilter.innerHTML=`${this.electionAppliedFilterBefore} <ul>${e}.</ul>`,this.electionAppliedFilter.classList.add(ft.SHOW)})):(this.electionAppliedFilter.innerHTML="",this.electionAppliedFilter.classList.remove(ft.SHOW))),this.electionsSelected.length>0||this.eventsSelected.length>0?(this.clear.classList.add(ft.SHOW),this.appliedFilters.classList.add(ft.SHOW)):(this.clear.classList.remove(ft.SHOW),this.appliedFilters.classList.remove(ft.SHOW)),this}},Tooltip:class{constructor(t){this.element=t,this.init()}init(){return this.cacheDomReferences().setupHandlers().enable(),this}cacheDomReferences(){return this.tooltipButton=this.element.querySelector(yt.TOOLTIP_BUTTON),this.tooltipContent=this.element.querySelector(yt.TOOLTIP_CONTENT),this.tooltipContent.getAttribute(mt.HIDDEN)===gt.TRUE&&(this.tooltipContent.style.display="none"),this}setupHandlers(){return this.onEnter=this.onEnter.bind(this),this.onLeave=this.onLeave.bind(this),this.handleTooltipPosition=this.handleTooltipPosition.bind(this),this.globalEscape=this.globalEscape.bind(this),this.attachGlobalListener=this.attachGlobalListener.bind(this),this.removeGlobalListener=this.removeGlobalListener.bind(this),this}enable(){return this.element.addEventListener(vt.MOUSEENTER,this.onEnter),this.element.addEventListener(vt.TOUCH_START,this.onEnter),this.element.addEventListener(vt.FOCUS,this.onEnter),this.element.addEventListener(vt.MOUSELEAVE,this.onLeave),this.element.addEventListener(vt.BLUE,this.onLeave),this}onEnter(){return this.tooltipContent.getAttribute(mt.HIDDEN)===gt.TRUE&&(this.tooltipContent.setAttribute(mt.HIDDEN,!1),this.tooltipContent.style.display="block",this.handleTooltipPosition(),this.attachGlobalListener()),this}onLeave(){return this.tooltipContent.getAttribute(mt.HIDDEN)===gt.TRUE||(this.tooltipContent.setAttribute(mt.HIDDEN,!0),this.tooltipContent.style.display="none",this.tooltipContent.style.left="",this.tooltipContent.style.right="",this.tooltipContent.style.transform="",this.tooltipContent.style.setProperty("--after-right",""),this.tooltipContent.style.setProperty("--after-left",""),this.tooltipContent.style.setProperty("--after-tx",""),this.removeGlobalListener()),this}handleTooltipPosition(){const t=this.tooltipButton.getBoundingClientRect(),e=this.tooltipContent.getBoundingClientRect(),i=e.x+e.width,s=t.x+t.width;if(e.x<0){const e=""+(42-t.x);this.tooltipContent.style.left="0",this.tooltipContent.style.right="auto",this.tooltipContent.style.transform=`translateX(${e}px)`,this.tooltipContent.style.setProperty("--after-tx",-1*e+"px"),this.tooltipContent.style.setProperty("--after-right","auto"),this.tooltipContent.style.setProperty("--after-left","0")}else if(i>window.innerWidth){const t=""+(window.innerWidth-s-42);this.tooltipContent.style.left="auto",this.tooltipContent.style.right="0",this.tooltipContent.style.transform=`translateX(${t}px)`,this.tooltipContent.style.setProperty("--after-tx",-1*t+"px"),this.tooltipContent.style.setProperty("--after-right","0"),this.tooltipContent.style.setProperty("--after-left","auto")}else this.tooltipContent.style.left="",this.tooltipContent.style.right="",this.tooltipContent.style.transform="",this.tooltipContent.style.setProperty("--after-right",""),this.tooltipContent.style.setProperty("--after-left",""),this.tooltipContent.style.setProperty("--after-tx","")}attachGlobalListener(){document.addEventListener(vt.KEY_DOWN,this.globalEscapeBound)}removeGlobalListener(){document.removeEventListener(vt.KEY_DOWN,this.globalEscapeBound)}globalEscape(t){"Escape"!==t.key&&"Esc"!==t.key||this.onLeave()}},VerticalVideo:class{constructor(t,e){this.element=t,this.ScrollService=e.ScrollService,this.playing=!1,this.paused=!1,this.autopaused=!0,this.init()}init(){return this.cacheDomReferences().setupHandlers().enable(),this}cacheDomReferences(){return this.videoCarousel=this.element.querySelector(".verticalvideo__carousel"),this.videos=this.element.querySelectorAll(".verticalvideo__video"),this.nextSlideButton=this.element.querySelector(".verticalvideo__button-next"),this.prevSlideButton=this.element.querySelector(".verticalvideo__button-prev"),this.pauseAllButton=this.element.querySelector(".verticalvideo__pause"),this.playAllButton=this.element.querySelector(".verticalvideo__play"),this}setupHandlers(){return this.onScrollHandler=this.onScroll.bind(this),this.playVideo=this.playVideo.bind(this),this.pauseVideo=this.pauseVideo.bind(this),this.showControls=this.showControls.bind(this),this.hideControls=this.hideControls.bind(this),this.pauseAll=this.pauseAll.bind(this),this.playAll=this.playAll.bind(this),this}enable(){return window.setTimeout(this.onScrollHandler,300),this.ScrollService.addCallback(this.onScrollHandler),this.swiper=new ia(this.videoCarousel,{slideClass:"verticalvideo__slide",wrapperClass:"verticalvideo__wrapper",slidePrevClass:"verticalvideo__slide-prev",slideNextClass:"verticalvideo__slide-next",slideActiveClass:"verticalvideo__slide-active",direction:"horizontal",slidesPerView:"auto",loop:!0,spaceBetween:15,watchOverflow:!0,observer:!0,resizeObserver:!0,navigation:{nextEl:this.nextSlideButton,prevEl:this.prevSlideButton},modules:[sa]}),this.videos&&this.videos.forEach(((t,e)=>{t.addEventListener(vt.MOUSEOVER,(()=>this.showControls(e))),t.addEventListener(vt.FOCUS,(()=>this.showControls(e))),t.addEventListener(vt.MOUSEOUT,(()=>this.hideControls(e))),t.addEventListener(vt.BLUR,(()=>this.hideControls(e))),t.addEventListener(vt.LOADED_METADATA,(()=>{this.element.dataset.visible!==gt.TRUE||this.playing?this.element.dataset.visible!==gt.TRUE&&this.pauseVideo(e):this.playVideo(e)}))})),this.pauseAllButton&&this.pauseAllButton.addEventListener(vt.CLICK,(()=>{this.paused=!0,this.pauseAll()})),this.playAllButton&&this.playAllButton.addEventListener(vt.CLICK,this.playAll),this}showControls(t){return this.videos[t].controls=!0,this}hideControls(t){return this.videos[t].controls=!1,this}playVideo(t){return this.videos[t].play(),this.hideControls(t),this}pauseVideo(t){return this.videos[t].pause(),this.hideControls(t),this}pauseAll(){this.videos.forEach(((t,e)=>this.pauseVideo(e))),this.playAllButton.classList.remove("hidden"),this.pauseAllButton.classList.add("hidden")}playAll(){this.videos.forEach(((t,e)=>this.playVideo(e))),this.playAllButton.classList.add("hidden"),this.pauseAllButton.classList.remove("hidden")}onScroll(){return this.element.dataset.visible!==gt.TRUE||"autoplay"!==this.element.dataset.autoplay||this.playing||this.paused||!this.autopaused?this.playing&&!this.autopaused&&this.element.dataset.visible!==gt.TRUE&&(this.pauseAll(),this.playing=!1,this.autopaused=!0):(this.playAll(),this.playing=!0,this.autopaused=!1),this}},VideoYoutube:class{constructor(t){this.element=t,this.videoId=Oe(this.element.dataset.url),this.iframeId="player-",this.playerready=!1,this.init()}init(){return this.cacheDomReferences().setupHandlers().enable().scriptInject(),this}cacheDomReferences(){return this.playTrigger=this.element.querySelector(yt.VIDEO_TRIGGER),this.featuredVideoEmbed=this.element.querySelector(yt.VIDEO_EMBED),this.iframeId+=Pe(16),this.featuredVideoEmbed.setAttribute("id",this.iframeId),this}scriptInject(){const t=document.createElement("script");t.src="https://www.youtube.com/iframe_api";const e=document.getElementsByTagName("script")[0];return e.parentNode.insertBefore(t,e),setTimeout((()=>{this.onYouTubeIframeAPIReady()}),2e3),this}setupHandlers(){return this.onClickPlayTriggerYTHandler=this.onClickPlayTriggerYT.bind(this),this.onPlayerStateChange=this.onPlayerStateChange.bind(this),this.pauseHandler=this.pause.bind(this),this}enable(){return this.playTrigger.addEventListener(vt.CLICK,this.onClickPlayTriggerYTHandler,!1),document.addEventListener(vt.PAUSE_VIDEO,this.pauseHandler,!1),this}onYouTubeIframeAPIReady(){this.player=new YT.Player(this.iframeId,{videoId:this.videoId,events:{onReady:()=>{this.playerready=!0},onStateChange:this.onPlayerStateChange},playerVars:{rel:0,fs:1,showinfo:0}})}onPlayerStateChange(t){0===t.data&&(this.element.classList.toggle(ft.PLAYING),this.playTrigger.classList.toggle(ft.CLICK),this.playTrigger.setAttribute(mt.HIDDEN,!1),this.replaceIframeElement=document.createElement(yt.DIV),this.replaceIframeElement.setAttribute("id",this.iframeId),this.replaceIframeElement.classList.add(ft.VIDEO_EMBED),this.featuredVideoEmbed.setAttribute(gt.TABINDEX,-1),this.element.querySelector(yt.VIDEO_EMBED).replaceWith(this.replaceIframeElement),this.featuredVideoEmbed=this.element.querySelector(yt.VIDEO_EMBED))}onClickPlayTriggerYT(t){return t.preventDefault(),this.element.classList.toggle(ft.PLAYING),this.playTrigger.classList.toggle(ft.CLICK),this.playTrigger.setAttribute(mt.HIDDEN,!0),this.featuredVideoEmbed.setAttribute(gt.TABINDEX,0),this.playerready&&this.player.playVideo(),this}pause(t){t.detail===this.videoId&&this.player.stopVideo()}}};let ra=0;class oa{constructor(){this.callbacks=[],this.init()}init(){window.addEventListener(vt.RESIZE,Et(this.onResize.bind(this),10))}onResize(){this.callbacks.forEach((t=>{t.callback()}))}addCallback(t){const e=(ra+=1,ra);return this.callbacks.push({id:e,callback:t}),this.removeCallback.bind(this,e)}removeCallback(t){this.callbacks=this.callbacks.filter((e=>e.id!==t))}}let aa=0;class la{constructor(){this.callbacks=[],this.init()}init(){window.addEventListener(vt.SCROLL,Et(this.onScroll.bind(this),10))}onScroll(){this.callbacks.forEach((t=>{t.callback()}))}addCallback(t){const e=(aa+=1,aa);return this.callbacks.push({id:e,callback:t}),this.removeCallback.bind(this,e)}removeCallback(t){this.callbacks=this.callbacks.filter((e=>e.id!==t))}}class da{constructor(){this.ResizeService=new oa,this.ScrollService=new la}}class ha{constructor(){this.Services=new da,this.inViewport=new $e(this.Services),this.loadPagecomponents()}loadPagecomponents(){const t="data-loadcomponent";Array.prototype.forEach.call(document.querySelectorAll(`[${t}]`),(e=>{new(na[e.getAttribute(t)])(e,this.Services)})),document.addEventListener("cmplz_before_cookiebanner",(()=>{Array.prototype.forEach.call(document.querySelectorAll("#cmplz-cookiebanner-container"),(t=>{const e=t.querySelector(".cmplz-close"),i=t.querySelector(".cmplz-cookie");i&&(i.load("/wp-content/themes/vsatwentyfour/images/lottie/cookie-blue.lottie"),e&&e.addEventListener("click",(()=>i.pause())),document.cookie.includes("cmplz_banner-status=dismissed")&&i.pause())}))}))}}n(4797);window.App=new ha}(),n(1440)({})}();
//# sourceMappingURL=app.js.map