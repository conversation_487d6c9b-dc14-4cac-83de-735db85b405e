{"version": 3, "file": "css/app.css", "mappings": "AAAA;;;;;;;CAAA,CCIA,WAME,iBAGF,CARE,sBAIA,iBACA,CAFA,eACA,CAHA,2HAOF,YAME,iBAIA,CATA,qBACA,CAGA,iBACA,CADA,gBAHA,yHAQA,YAQF,iBACE,CATA,qBACA,CAIA,iBAGF,CAJE,eACA,CAJA,qHAQA,YASA,iBACA,CAVA,qBACA,CAOF,iBACE,CAJA,gBAJA,mIASA,YAQA,iBACA,CATA,sBAOF,iBACE,CAJA,eAGF,CAPE,uHASA,YAQA,iBACA,CATA,sBAQA,kBADF,eACE,CARA,qIASA,YASA,kBATA,6BAQA,iBACA,CADA,gBARA,4IASA,YAUA,kBAVA,6BAUA,kCAVA,wJAUA,YASA,kBATA,6BASA,kCATA,oJASA,YAUA,kBAVA,6BAUA,kCAVA,gKAUA,YASA,kBATA,4BAGA,CAMA,kCANA,kJAMA,YAUA,iBAEA,CAVA,4BAEA,CAMA,kCANA,gJAQA,CACA,4BAGF,CACE,qBACA,CAGA,iBAGF,CALE,eAEA,CAHA,mHAMF,YACE,iBACA,kBACA,CAEA,iBAGF,CALE,qHAKF,YACE,iBACA,mBAGA,kBAHA,mIAGA,YAIF,iBACE,uBACA,kBACA,gBACA,gHACA,YAIF,iBACE,uBACA,kBACA,8IC5JF,YACE,iBACA,mBAGF,CASE,iBAGF,CAHE,gBATF,wGAYA,2BASE,sBAMA,kBAKA,kBAGF,MACE,kCAOA,kCAMA,CAbA,QAaA,uCAMA,kBACA,SACA,aACA,8BACA,2CAIF,aACE,GACA,oBAOF,UARE,cCrEF,CD6EA,OAQE,8BCrFF,iBDmFA,qBAEE,CCrFF,uBD+EE,gBC/EF,wHCgPE,mLAmaA,6DAIA,+BAvkBI,2BDhFN,CCupBE,wBAvkBI,CDhFN,0CC8oBE,uCACA,mBACA,gBACA,CDjpBF,YCipBE,gBACA,CDlpBF,4DCkpBE,mBACA,CDnpBF,sECuPI,kLDvPJ,6CC2PI,kLA8ZF,yfAKA,+wBD/oBF,6DAIE,2BAGA,CCymBA,2BAzEA,CDhiBA,wBCymBA,CAzEA,+BAEA,CA6GE,uDA7GF,oBACA,uBACA,ghBAQA,CARA,WA1eI,eAkfJ,iDAEA,kGAEE,6DAtfE,iGA+fF,wBA/fE,qBD1DJ,sDAMF,0CCkSE,kBACA,kBACA,mBAEA,qEAEA,6BAEA,uCAobA,4EAGA,0CACA,yCAEA,8DAEA,CACA,6BAEA,CACA,2BACA,CAFA,wBACA,CACA,6BAvrBI,CA4PF,gBA5PE,8BA0PF,6BAEA,cACA,CATF,4BACA,+BACA,CAOE,6BAPF,iBAOE,YACA,8CACA,kCACA,wCAhQE,0CAuQF,wCACA,0CACA,0CACA,CAVA,mBAUA,oCACA,wCA8aF,0CAKA,iFAEE,2CACA,CAtbA,mBAsbA,2DAEA,2DDnvBJ,6DAKE,2BAGA,CACA,2BACA,CAFA,wBACA,CACA,+BAEA,CCyuBE,uDDzuBF,sBACA,uBACA,qBAEA,gBACA,6BAGA,yBACE,8BACA,CATF,eASE,mBACA,6BACA,gBACA,SACA,kBAIF,+DAEE,yCAQF,SCujBA,iBDvjBA,SACA,CATE,2BC+jBF,CDtjBA,uDAGA,mBACA,6CAGA,mBACA,uBACA,qBACA,CACA,eACA,6BAEA,yBAEE,kBACA,eATF,eASE,mBACA,6BACA,gBAEA,kBAGF,2DC8KA,mBACA,qBACA,CAFA,2BAIA,OD1KA,CCyKA,2BACA,CD1KA,SExFM,CFyFN,yCC1BI,mBDsBN,OAWI,UACA,CCoKA,gCAtME,kBA0MF,gBD7KF,kBAKE,iCACA,CANF,wBAME,yBAIJ,oBAEI,2BAIA,iDASF,oBCyIA,CD5IA,2BACA,+BC2IA,uBACA,8BAEA,2BACA,eA0gBA,YACA,sBACA,kBACA,yBAhtBI,WAqtBF,aAHF,gCAltBI,kCDsDJ,iBCgJE,CDhJF,mCCoJE,kBA0gBF,cA9gBE,wBAtME,CAotBJ,iBACE,gEAOA,0BACA,wBACA,cACA,+BACA,iFAQF,CAIE,0BAEA,mCAQE,uBAdJ,mCACE,CACA,sBACA,CAFA,wBACA,CACA,eAWE,+BD1rBJ,wwBA8BA,CC4pBI,2BAtvBA,0BA2vBF,WACE,0BACA,kBACA,QACA,MDnsBJ,WA8BA,0BC1FI,8BD0FJ,yBCgHE,YD7GA,uBCkGF,iCACA,4BAEA,kBACA,iCAnMI,kCAsMF,yBAtME,wCA0MF,eDxGF,yBACE,+BAEA,kBAGF,2BAEE,+BCqFF,6DAEA,cACA,kDAQE,6BDxFF,CClHI,sBD8GJ,CC9GI,kCA0MF,CAJA,yBDpFF,wCAME,yTCwCF,wDACA,gCAEA,CACA,iCAyZA,mEACA,qBACA,yCACA,qBAEA,cACA,0BAEA,8BACA,WACA,4CAvkBI,yBDwHF,+kBCidF,2LDjdE,2ECidF,YDjdE,gBCmDA,CA8ZF,qCA9ZE,mBA8ZF,gvBAKA,wzDA9FA,6DAEA,4BAEA,4BAFA,wBAEA,iCAPA,uDAOA,4DAEE,oDAGA,cAzfE,iCDoJF,iFC2WA,wBA/fE,yBDoJF,+YCpJE,CDoJF,WC2WA,eA/fE,oBD6JA,4CC6CF,gBA0gBF,iDACE,+EAOA,6HAMA,wBACA,qBAEA,6BAGF,iFAEE,kBAEA,kBACA,mBACA,EACA,yBAKE,0EApvBA,sBA2vBF,iDCh0BiB,UFoPjB,CAOE,YACA,CCqkBA,gCAGA,kBDxlBE,kCAQJ,gBACE,mBChLA,kBDqLF,cAdI,wBACE,CAaN,iBAEE,yBC4XJ,sCEnoBA,cACA,iEAMA,2DAMF,iFASI,CAcE,2BACA,kCAGA,sBACA,CAnBF,gBAKF,mBAGE,CAKA,uBALA,wBAKA,gBAME,6CAkBF,wwBFyHA,CEpIA,2BAGE,qCACE,4CACA,QAMJ,iBFyHA,0BACA,4CACA,yBAtGE,wBELF,kEAOF,qCF0GA,oCACA,wBAEA,8BACA,CACA,4BAEA,yCACA,mDACA,oCACA,kDAtHI,wBEEJ,+BFyHE,wBAEA,mCACA,6DACA,+BACA,eAhIE,YAgIF,OAhIE,eEEJ,4BFmIE,eAEA,mCACA,2EACA,UACA,wCEpIF,uCF0IA,mBG/MsB,CHgNtB,gCACA,oBAEA,yBE3IE,wBFTE,2BEMJ,uBFiJE,KAUF,mCAEA,yCEpJE,wCAHF,0CFZI,kCEMJ,mBFqJE,gBE/IF,qBFoJA,iBAhKI,kBEYJ,CFoJA,wBEpJA,yBF+JE,GEzJF,uCF8JA,uCAEA,wCE7JE,yCAHF,CFyJE,gBEzJF,2BFoKE,GAtLE,uCA0LF,2CAKF,wCAEA,2CAEA,CAbE,oBAaF,0BAnMI,qBEwBJ,2BFxBI,6CAiHJ,uCAEA,wCACA,wCACA,wCACA,CE9FA,iCFoFA,kCACA,qBACA,CACA,wCAOA,yBAtHI,sBA2HF,uCAEA,qCACA,wCACA,yCACA,CEnGJ,cF6FI,qBAMA,2BAhIE,sBAuIF,sCACA,wCACA,uCACA,yCEzGA,CFoGA,iBEpGA,wBACA,uCAFF,mBAKI,gBACA,iBFtCA,CEgCJ,mBAUI,yBAKJ,sBF0BA,4CACA,sBAEA,sCAEA,uCAEA,mBACA,kCACA,4CACA,wCAnFI,2BE+CJ,sBF0CE,yCACA,uCACA,oDACA,6CA5FE,sBE+CJ,iBFkDE,2BAEA,uCACA,wDACA,kBACA,0DEnDF,yBF6FA,sBACA,yCAEA,sBACA,cE9FE,KFqGA,uCAKF,wCACA,wCAEA,CACA,uCExGE,CANA,iCAHF,kCFoGE,qBAvJE,gBEmDJ,yBFnDI,yBEyDJ,GF8GE,uCE9GF,qCAMA,wCFiHA,yCAEA,CEzHA,mCFyHA,2BEhHE,GFlEE,sCE+DJ,wCF/DI,uCA0LF,yCAKF,CE7HE,iBF6HF,+BACA,gBACA,yBACA,WACA,kBAnMI,2CAsMF,6BAtME,gBEqEJ,wBFrEI,mCAuJF,yCAIA,wCE5EA,0CAFF,CF6HE,iCA1DF,oBG/MsB,eHgNtB,qBAEA,iBACA,wBEvEA,yBAMI,sBFnFA,uCEwFA,uCFfJ,wCACA,yCAEA,CA5EI,gBA4EJ,EACA,yBACA,sBAEA,uCACA,2CACA,wCACA,2CESA,CFZA,oBEYA,wBFLE,uCAEA,mBACA,iCACA,oBACA,+CA5FE,iBE4FJ,2BFKE,sBAEA,sCACA,uCACA,oDACA,oBENF,yBFYA,wCACA,EACA,yBACA,sBACA,iBACA,wBAEA,uCACA,mCACA,qCACA,+CEnBE,iBFnGE,2BEgGJ,sBF0BE,iBACA,wBAEA,gCACA,kCACA,0CACA,+CAhIE,yCAqIF,sBAEA,0DACA,mBACA,qDACA,yBEpCF,sBF0DA,0CACA,+BACA,gBACA,yBE1DE,6BFzGE,gBEsGJ,2BFiEE,6BAvKE,gBEsGJ,wBASE,mCF/GE,yCAsLF,CAtLE,uCE4GJ,0CAMA,CFyDE,iCAKF,wDACA,gBACA,yBEhEA,yBF6EA,sBAEA,uCAEA,uCEjFA,wCFlHI,yCEkHJ,CF8EA,gBAUE,2BA1CF,sBACA,uCAEA,2CAnKI,mFE0HJ,CFsCA,oBErCE,wBASE,uCAKJ,wCC1MY,CH2IZ,uCAEA,wCAEA,CE8CE,iCF5HE,CE0HJ,iBAKI,gBACA,qBFhIA,yCEoIA,CFtDJ,mBAEA,+CAEA,uCACA,qCAnFI,wCEyIJ,yCFhDE,CARF,mCAQE,2BACA,sBACA,sCACA,wCA5FE,uCEyIJ,yCFtCE,CATA,iBASA,wBACA,0DACA,iCACA,oBEuCF,yBFjCA,wCACA,2BAEA,sBACA,iBACA,wBAEA,uCACA,mCACA,8DACA,sBE0BE,iBFhJE,2BE6IJ,sBFnBE,iBACA,wBAEA,gCACA,mDACA,0BACA,8CAhIE,yCE6IJ,sBFNE,0DACA,mBACA,qDACA,8CESF,2BFHA,oBACA,+BACA,gBACA,yBEGE,6BFtJE,gBEmJJ,2BFIE,WAvJE,kCEmJJ,wBASE,mCAHF,yCFzJI,wCEyJJ,0CAMA,CFJE,iCAqBF,mCACA,qBACA,gBACA,yBEpBA,yBFgCA,sBAEA,uCAEA,uCEpCA,wCF/JI,yCA0MF,CAVF,gBAUE,2BA1BF,sBACA,uCAjLI,2CAsLF,CAtLE,uCA0LF,2CEjBA,CFOF,oBEPE,wBAaF,uCF7GA,wCAEA,wCAEA,wCAGA,CAhFI,iCE4KA,kBACA,gBF7KA,qBEuKJ,gBAUI,yBAKJ,CFtGA,4CACA,sBACA,uCACA,qCEmGA,wCF7FE,yCACA,CATF,cACA,qBAQE,iDACA,sCACA,wCE0FF,uCFnFE,yCACA,CATA,iBASA,+DACA,mBACA,kCEoFF,mBF9EA,+CAEA,iBACA,2BACA,sBACA,cAEA,+DACA,mBACA,qDACA,yBEuEE,sBF7LE,4CA0HF,sBACA,iBAEA,wDACA,kBACA,0DACA,yBAhIE,oCE0LJ,2BFnDE,yCACA,uCACA,oDACA,4BEsDF,4CFhDA,oBACA,+BACA,gBACA,yBEgDE,6BFnME,gBEgMJ,2BFzCE,WAvJE,kCEgMJ,wBF5BA,mCApKI,yCAuKF,wCAvKE,0CA2KF,CAhBA,iCAKF,mCACA,qBACA,gBACA,yBAQE,CEiCF,wBFbA,sBACA,uCAEA,uCAlMI,wCAsMF,yCEMF,CFbA,gBEaA,2BFFE,sBAVF,uCAEA,2CAlMI,mFEoNJ,CFrBA,oBEqBA,wBAUI,uCAKJ,wCF1JA,wCAEA,wCAGA,CEuIE,iCFrNE,kBEoNJ,CAKI,oCFzNA,yCE8NA,CFhJJ,4CAEA,sBAEA,uCACA,6EEgJA,yCF1IE,CARF,mCAQE,2BACA,sBACA,sCACA,+EEuIF,yCFhIE,CATA,iBASA,+DACA,mBACA,iCACA,6CAMF,uCACA,2BACA,sBACA,cACA,wBACA,uCAEA,mBACA,iCACA,oBACA,+CEoHE,kBF1OE,2BEuOJ,sBF7GE,iBACA,wBAEA,gCACA,kCACA,0CACA,+CAhIE,cEuOJ,2BFlGE,sBAEA,mDACA,kBACA,0DACA,yBEmGF,mBF7FA,0CACA,+BACA,gBACA,yBE6FE,6BFhPE,gBE6OJ,2BFtFE,WAvJE,kCE6OJ,wBFzEA,mCApKI,yCAuKF,wCAvKE,0CA2KF,CAhBA,iCAKF,mCACA,qBACA,gBACA,yBAQE,CE8EF,wBFzEA,sBACA,uCAEA,uCAnLI,wCAsLF,yCAtLE,CAgLJ,gBAhLI,CEyPJ,0BF/DE,sBAKF,uCACA,2CAGA,wCAnMI,4CE+PJ,oBF/PI,wBAkPJ,uCACA,wCACA,wCACA,wCACA,CESA,iCFrDE,kBAmCF,iBG1SW,oBH4SX,gBACA,yBAEA,CAIA,4CI9TmB,sBJoUjB,uCACA,qCACA,wCACA,yCACA,CAhQE,cA0PF,qBAMA,iDEIJ,sCFGI,wCACA,uCACA,yCACA,CA1QE,iBA0QF,+DACA,mBEFA,iCFoHF,6CACA,uCACA,2BACA,sBACA,cACA,wBACA,0DACA,gBAEA,oFAEE,6CAEA,sBE7HA,mHFIF,qCACA,yBAEA,sBApRI,iBE6QF,0HFUA,kBAvRE,iCE6QF,iHFcA,sBETA,yDFcF,CG7VW,oCHgWX,uCEjBE,4MFoBA,EAtSE,wCEkRF,4HAKA,2CFeA,mBEfA,qEFwBF,0CAEA,wCAjTI,yCEuRF,qRFkCA,wEE7BA,yHFmCF,6BAEA,cAjUI,2IE4RF,4BFwCA,kBApUE,gBE4RF,kKF5RE,kBEqSF,2BFrSE,wHAgPJ,kBAEA,0HAGA,8CACA,iCE4DE,yBACA,wHFzDA,kBAEA,mJAGA,kBACA,kIAOA,4BACA,mCACA,0CACA,gIE8CE,kBACA,2BACA,gIFmEJ,uJAMA,6DACA,0CAEA,oIAIE,iBExEE,+JASA,8CAEE,kBACA,2BAIA,+CAUI,4ED3aK,CCgbH,gFACE,yCAIA,cAnBN,4BAIA,gDDlaG,CCibG,2BACE,uBFvWZ,mBEkXF,yBAGE,yBAGE,wCFxXF,0CEqYA,wCAGE,0CAGE,0CF9JR,CE2IM,mBF3IN,2BACA,yBAIA,wCACA,0CACA,wCACA,yCACA,2CEiKE,CFvKF,mBEuKE,iCAEA,oCACA,4BEleiB,CJwEf,sBEmZJ,yDFvJE,6DACA,+BACA,4BACA,uCACA,wFAKA,sBAEA,+FAEA,6BACA,gFACA,aEkJA,yEAGE,eACA,2EFpCJ,gBEuCI,eFvCJ,sDACA,yEAEA,aACA,kEAEA,qFAGA,2FAGE,aACA,yBE8BE,gCACE,6BAGF,eACE,CADF,eACE,2BAEA,cACE,+CAIJ,0DAYA,uCAGE,qCAGE,uCAON,0CAII,CEvhBc,wCF2hBZ,0BAQV,aFxgBE,CEieM,4BFtbF,+BE6bF,iBAGE,CF3eJ,2BAEA,wBAEA,mBAEA,yBACA,yBK/CA,wCAGA,0CAEA,wCAGA,CACE,yCAVJ,0CAeI,CAfJ,mBAgBI,2BACA,yBAjBJ,wCAuBI,0CAvBJ,wCA6BI,yCAEA,2CAOF,CL8CI,mBK9CJ,iCAGA,iBL2MA,4CACA,6BACA,qBAEA,oCApKI,4FA2KF,4BKlNF,yBLoRA,cACA,eACA,yEAIA,qHAGA,2CAtPI,4EA4PF,qDACA,2BACA,4CACA,sGKtSF,eL4SE,CK5SF,eL4SE,yBAGA,6DACA,eACA,CADA,eACA,2BACA,6DK7SA,eAGF,CAJA,eAIA,aACE,kBACA,CADA,SACA,uBACA,yBACA,kBACA,UACA,qBACA,iBACA,2BACA,gCACA,CACA,gBAGF,kBACE,CALA,eAKA,uBACA,WACA,yBAEA,gBAEA,QACA,uBACA,iBACA,0CAGF,gBACE,QACA,gBAEA,4CACE,gBAGF,+BAEE,gBACA,qCAOF,qDACE,oCAGF,0DACE,iCAGF,gEACE,kBACA,2BAGE,wDACE,uCC7GR,qCACA,uCAEA,CAEA,yCNiCA,yCAGA,CKmEM,2DACE,iBLpER,yBAEA,kBMlCA,wCAIA,0CAGE,wCNuBF,0CAGA,0CAGA,CADA,mBACA,2BMvBE,kBAEA,wCACA,0CAEA,wCAEE,yCAGA,2CACE,CAVJ,mBAUI,uCAEA,iCAEA,kDACE,oBAKN,mDAEE,gBAII,yHACE,uBN+BN,CMvBF,YA5DJ,OA4DI,gBNuBE,uBMnFN,CNmFM,uBMnFN,kCAkEI,CAlEJ,UAkEI,mBACE,6BN+jBJ,mCAzEA,oBACA,yBACA,kBACA,kBACA,gbAEA,0BACA,oBAEA,4BACA,eACA,gBACA,kBAJA,kBAIA,qCAEA,4BAEE,sCOnkBQ,CPokBR,qCACA,uBACA,sCAzfE,oBA6fF,qCAEA,kBA/fE,CA+fF,SA/fE,UOhFN,YPmlBI,gBO5kBF,4BP+iBA,gCACA,oBACA,kBACA,gCACA,kYACA,uHAKA,0BAEA,8DAEA,8CAEE,kBACA,uBCtjBI,eDwjBJ,UAzfE,gCA6fF,iCAEA,gBA/fE,COzEJ,8XCOE,CRkEE,uBOzEJ,CPyEI,eQlEF,iGAEE,gCAGF,+EAGE,6BAIF,yEACE,wBACA,kBAEA,qEACE,mBACA,2BRgDF,0BQlDA,gEAMI,gBR4CJ,CQtDF,8XA0BI,CR4BF,WQlDA,eAsBE,gEACE,gBACA,iBR0BJ,gCQ5BE,sHAMI,wBAIJ,0GACE,kBACA,mBACA,2BReJ,aQlBE,iHAMI,6BAEA,yBAMR,6FAKE,mBAFA,kCAEA,oDAEE,kBAGF,yGACE,UACA,qEAFF,sIAOI,gHAOF,yEACE,uEADF,kFAKI,qBACA,CANJ,CAMI,4FAMJ,4BACA,yBACA,4FAHF,wIAWE,gBAEA,CAFA,SAEA,gJAKA,iJAIA,gBACE,CADF,wBACE,yBAEA,uHASA,kHAEE,YAGF,oHAIA,qBAHE,UAGF,sGAGE,gBR7EN,CQ4EM,SR5EN,yBQkFI,qGACE,aACA,0GAGF,mBACE,2GAQJ,sBACA,CACA,oBRlGF,yBQJF,yGA4GI,8HAKE,oBAMJ,oJAKA,2GAKA,gHAIA,kIAIE,mJACE,UAGF,kNAGE,oIAEE,2HAMF,iKAMJ,iJAME,kBAEA,8EACE,0BAGF,sHRtKF,oBQ0JA,6EAkBI,cACA,WACA,CAFA,iBACA,CACA,gCR9KJ,gIQoLM,eAEA,qFACE,CADF,YACE,oEAOF,iJAIE,0MAYF,6JAKE,WAMR,4JASE,sBAEA,CAJJ,8BAEI,CAFJ,gCAII,4LAIA,0FASE,0BAQF,mCAPI,mBACA,CAFF,gBACE,CACA,iBAMJ,8EACE,0HAKF,qCACE,kEAIJ,uCACE,uBAEA,CAHF,oBAGE,2BAEA,6FAEA,aACE,kBAGF,oGACE,wBAEA,gJRlRJ,YQoQA,8JAwBI,yDACE,iHAEA,WACE,aACA,mCAQR,qGAKF,8DACE,gEAMA,2BAFA,YAEA,wEACE,YACA,CAGF,kBAHE,kBAGF,+FAoBJ,6FR9UI,uCQiVN,wGAUI,8YC7ZA,wGTkaF,0iBS/SQ,0FASN,aA9IJ,UA8II,SAEA,kPASI,gIAIE,WAGF,CAHE,UAGF,kHAUJ,gQCzKF,WACA,CADA,UACA,CACA,0EAKA,WANA,CAMA,mEVycA,uFACA,uFAIA,gBACA,oHAGA,iFAGE,mBACA,CADA,gBACA,yBU9cF,kCAhBF,mBAqBI,CV+DE,gBU/DF,gDArBJ,kBA6BI,CA7BJ,QAyBI,CV2DE,sBUpFN,CAyBI,yBAIA,sCVuDE,kBUpFN,CAiCI,gCAIJ,YACE,yBACA,kBACA,iBACA,gBACA,yBAEA,uBACE,2BAGF,uBACE,cAGA,kBACA,CAHA,YACA,sBACA,CACA,sBACA,8BV2XF,uCACA,YACA,+BUrXE,aNtDa,gBJ4EX,sDUpBF,iBVoBE,CUxBF,gBACA,kBACA,CACA,iBAIF,CViBI,uBU3BJ,4BV2BI,kCU3BJ,kBAUA,iIAIE,gBVaE,sKUPF,oEAIE,CVGA,gBUHA,gBVGA,0BUEF,+BACA,WAEA,8KAIE,YVTA,mPUoBA,aACA,CACA,gBADA,eACA,WVtBA,uEA6cJ,qCACA,yBAEA,6BWjiBA,2BAEA,oFAEA,qDACA,yBACA,8BACA,qKAGE,eACA,0BACA,YAKA,oCACE,gBACA,yBACA,kBACA,6CACA,uEAWA,sCXgDA,YWpFN,oBAyCI,qKCpCF,eAEA,sMZmQE,eAtLE,8CA0LF,eYhQF,2BZqQA,gCACA,qKYtQA,0CAMA,YZ6gBA,qCAGA,kBY7gBE,CZ2gBF,YACA,sBACA,CY7gBE,sBZ6DE,iBYhEJ,qBZmhBE,wBAndE,YYhEJ,+BZuhBE,oFY7gBE,+BAEA,4BACA,gEAGA,yEAKE,sBAEA,yEAGA,6BAGF,wBAEE,kBACA,0BAEA,iBACE,yBACA,oCACA,2BZ2BJ,oCYnBF,2BAEE,+EAEA,oBAEA,iBAKA,kBAEE,CANA,YACA,sBAGF,CAEE,sBZMF,wBYCA,uBACA,YAKN,kCCtFE,YACA,iBAGA,8EDoFE,gBCzFJ,kBACE,CAEA,iBAGE,CADF,0BACE,yZAQA,eACA,yBAEA,sCACE,iLAUE,eACA,2BbmDF,kCa5CA,mMAQI,eAGF,0CAEE,oMAeJ,sDACE,2DAEA,cAKE,uBAKF,CAVA,6BACE,mBAGF,iCACE,CAKF,yDACE,4EbEJ,0CaOA,kCACE,8EAGA,2BACE,YAKF,0EACE,gFAQV,CAEI,oBC3GF,CDyGF,qBAEI,CC1GA,4BAEA,CAHF,mCAGE,mBACA,UAEA,oBACE,CADF,UACE,2BdwEA,YchFN,0CAeI,qCAGE,yCd8DA,2CcvDF,cACE,iBC9BN,oBAWE,cAVA,uCAKE,mBACA,gBACA,kBAGF,kBACE,yBAIF,oCAEE,2BAGF,mBACE,iBACA,oBASA,aAGF,CAVE,gCACA,kBACA,CAGF,iCACE,wBAIF,yBACE,kBACA,cACA,2BAGA,kBAEI,cAIA,oBAWN,aANA,6BAEE,mCACA,iBAGF,yBACE,kBACA,mBAGA,6CASA,cARE,mBACA,CAOF,yCACE,kBdpEW,Cc8Eb,iBAPA,4EAXE,CAkBF,wIACE,iCACA,uBCpFJ,4BACA,CADA,SACA,4BAME,oBACA,sBAGF,CATA,6BAEA,6BAOA,CDqDI,CCrDJ,yBACE,kBAGF,cACE,CAKF,gBALE,4EAKF,kEhB6QA,qBGhRY,CHiRZ,yBACA,kBACA,cACA,gBACA,EgBzQE,gBhBqDE,sBgB9DJ,0gBhBkSE,gCgBvRA,mOhB4HF,+BAEA,6BACA,0BAEA,wCAEA,qBACA,8FAEA,8CAnFI,SgBnDF,sNhB0IA,2BAEA,wCACA,+DAEA,8CA5FE,uDgBnDF,+KhBoJA,wCAEA,4EAEA,8CACA,yEgBrJA,6HhB2JF,0CAEA,YACA,8BACA,eACA,yBAEA,uDACA,iDACA,eACA,sDAtHI,kBgB/CF,oNhByKA,aACA,yBAGA,sBADA,sBACA,kBACA,CAFA,wBACA,CACA,iDACA,yBAhIE,8BgB/CF,2OhBsLA,wCACA,uCAEA,sBADA,iCACA,qEgBrLA,yOhB6LF,oCAEA,yBgB5LI,chBwCA,gCgB3CF,gNhBkMA,sBAvJE,oBgB3CF,4MAMA,+BANA,wBAMA,4LhBqMF,gGAIA,iBgBtMI,2BhBkCA,gGgBrCF,0IhB4MA,kBAvKE,0NgB/BF,qHhB+MF,yCG/OsB,Ca0BpB,iChBgNA,mBgB1MA,8EhBgNF,yBACA,wNgBjNE,8EhB+BE,wCgB/BF,6SAMA,0HhBwNF,2CACA,CgBzNE,oBhByNF,0NgBzNE,wPhB+NA,gBAtME,yBgBzBF,iPhBgMA,uCgB3LF,qCAOA,wCAEE,0ChBWE,cgBpBJ,qBASE,wWAEA,yCACA,CAHA,iBAGA,0NhB2aF,uCAGE,mCACE,iBACA,CAxaA,mBgBHJ,yBhBgbE,wNgB7ZA,kBACA,0BAIF,wNAWI,cAGF,0NAeE,uCAIF,mBACE,sSAOA,kBACA,2BACA,wNAmBF,2OAcE,uCAKA,mBACE,gBAEA,iBACA,CAGF,4CACE,wNAoBN,4CACE,wNC1MF,iBACE,0NAeA,gCACE,kBjBsDA,iCiBpFN,yBAoCI,yBACA,wNAcE,cjBiCA,2BiBpFN,wNAoEM,+BjBgBA,uCiBXF,mBAEA,kCAEE,sBAGF,SALA,mBAKA,yBACE,eACA,kBACA,2BCnFN,eACE,iBACA,qBA8BM,cA7BN,oZA4BI,mEACE,QlBoDF,uCmBpFN,WDsCI,4BlB8CE,kBkBpFN,CCAA,iBACE,CDDF,eA0CI,CCzCF,qBDyCE,gBC1CJ,CACE,cACA,4BAEA,4FAIA,yBACA,yBnB2EI,cmBpFN,2BAYI,yBACA,uEAIA,8BACA,iBnBkEE,iBmBpFN,oBAsBI,kBACA,qBACA,0BCvBF,+BACA,0DAME,sBACA,UACA,qCAGA,2CACE,oCAEA,WAEA,aAFA,WAEA,mCACE,aAIJ,0CACE,mBACA,gBACA,sBAGF,kCACE,oBAGF,mOAgBE,mBACA,CACA,mCAEA,YAEA,4CACE,kBACA,8CAIJ,gDACE,UAGE,oEACE,0CAQJ,CACE,iBAEA,CAHF,qBAGE,yHAMA,6BACE,0BACA,oBAQJ,qDpBgEJ,qBACA,mBACA,oCAEA,YACA,4BAEA,sCACA,0CACA,iBACA,CADA,sBACA,uCAnFI,kDoBSA,yBpB8EF,eAEA,oDACA,+DACA,eACA,iDA5FE,gCoBSA,sEpB0FF,2EAEA,YACA,QADA,iBACA,gEoBrFE,oBpB2FJ,0CACA,kBACA,gBACA,eACA,+DAGA,6CACA,mBACA,kDACA,2CAtHI,2BoBiBA,iBpByGF,mBACA,CoB1GE,gBpByGF,CACA,cAEA,+DACA,eACA,kDACA,2CAhIE,2BoBiBA,+FpBuHF,+CACA,qCACA,2CoBjHE,4CpBuHJ,4CACA,gBACA,iBACA,4BACA,iCApJI,sBoByBA,6DpB8HF,iBAvJE,WoByBA,yFAUA,6BpB6HJ,mHoB1HM,0BpBtCF,yBoBmCA,kCpBoIF,2BAvKE,kCoBmCA,sBAUA,mBAVA,uCAUA,sGpBsIJ,mBoBnIM,gBpBhDF,yBoB6CA,yCpByIF,yFoBzIE,mBpB6IF,gBoBnIE,+CpBwIJ,oBG7Pe,CH8Pf,kBACA,gBACA,aACA,sBAnMI,CAkMJ,oBAlMI,4BoBuDA,2BpB+IF,CoB/IE,gBpB+IF,SAtME,6BoBuDA,+CpBmJF,gBoB3IE,oBpB8KJ,CoB9KI,oBpB8KJ,iDAEA,SACA,8BAGA,UADA,4BACA,8DACA,YACA,wCACA,4CoBvLI,mBpB/DA,+BoB+DA,8BpB6LF,0CACA,8CACA,6CACA,qEACA,uBAhQE,qCoB+DA,iDpBwMF,+EAEA,sEACA,cACA,mEoBpME,iFpB2NJ,qBACA,mBAnSI,oFoB8EF,yCAGE,wCAGG,CAAD,yCAIF,CAjBA,iCpBvEA,mBoBuEA,qDpBmOF,yBoBlNE,yEAMA,uCAIA,uCAIA,wCACE,yCAMA,CAfF,gBAeE,2BACE,gDAGE,uCAKF,2CACE,wCpBvHN,2CoBgIF,CAhBM,oBAgBN,kDAcI,uCAGF,wCAOE,gFAEE,CAvBJ,iCACE,kBAKF,qCACE,CAGF,wCAaI,CAEA,wEAIA,uCAEE,qCAOF,iFACE,CAbA,cAGF,qBAUE,EACA,yBC/PZ,gDAKI,sCrB+EE,+EsBpFN,yCACE,CDGE,iBCHF,kDAEA,uCAGA,mBAEA,iCtBqJA,CG1IY,mBH2IZ,CACA,wBACA,gDAEA,iBAEA,2BACA,gDACA,gEACA,uCAnFI,mBsB5EJ,gBtBmKE,iBAEA,oBACA,yEACA,kBACA,2EsBxKF,iBtB6KE,kDAGA,wCACA,mDACA,6CsB7KA,gDAKF,iBACG,CAAD,0BACA,gDAKA,iBAEA,kDAKF,gCAGE,kCACE,iBAIJ,yBACE,yBAEA,gDAKF,cACE,2BACA,gDACA,eAEA,4CAQE,uCAMF,qCACE,uCAGA,0CAGF,yCAGE,CAtBA,4BACA,eAIF,iCAiBE,CACA,wBAGF,2CAIE,wCArFN,0CA2FI,wCAIE,0CAGE,0CAlGR,CAoFM,mBApFN,2BAyGI,2CAKE,wCAGE,0CAKF,wCAGE,yCAIF,2CAIE,CAnBF,mBAmBE,mDAKF,4BAEE,kBAEA,gBAGF,0CAEE,iDC9IN,6CACA,iDAEA,kBAEA,0BAEA,4BvBqJA,cG1IY,CH2IZ,gCACA,WACA,gBACA,eACA,4DAGA,+DACA,oBACA,oDuB5JE,wCAHF,evBmKE,+CAEA,cACA,mEACA,gBACA,iEuBxKF,qBvB6KE,mBAEA,uDACA,+DAEA,yDuB7KA,YACE,wCAKF,eACA,+CAIF,mBACE,+CAEA,qCAEA,kEAIE,gBAGF,mDAGE,WACA,gBAGF,0BACE,iEAQA,mBAEA,CAJF,oBAIE,uBAGF,mBAGE,yBvBwBA,CuB1BA,uBvB0BA,yBuBpFN,qBAkEI,2BAEA,qBAGE,qBACE,wCAGA,wBvBSF,mBuBHF,gBACA,CADA,gBACA,iBACA,sBAkBE,mCACE,yCAGA,wCAKF,0CAEE,CA1BF,iCAGE,mBAEA,gBAGF,qBACE,gBACA,CAgBA,iBAEA,CAlBA,wBAkBA,yBAGF,qBAGE,uCCvHN,uCACA,wCAEA,yCAEA,CDgHM,gBChHN,2BAIA,qBAEE,uCAEA,2CAKF,wCACE,2CAKA,CAdA,oBAcA,oCACA,sDACA,iBAEA,8CACE,aAGF,mCACE,YACA,2BAIJ,aACE,kCAEA,oFAKF,YxBqNA,iDACA,kBACA,0DwBnNE,iCxBiCE,iCwBrCJ,CAKE,SxBgCE,CwBrCJ,UxB2NE,gCwB3NF,UxB+NE,CA1LE,YwBrCJ,QxB+NE,YwBxNA,iCAKF,oBxBkGA,CwBtGI,YAIJ,axBkGA,gDAGA,WACA,SAFA,iBACA,CACA,UACA,oCAEA,oCACA,oCACA,qCACA,uEwBxGE,UACA,oBxBoBE,sCwBzBJ,mBxBgHE,kBAEA,mCACA,iCACA,gCACA,CADA,UACA,0CA5FE,UwBzBJ,CxByBI,sBwBzBJ,6CxB6HE,aADA,iCACA,2DACA,WACA,UADA,QACA,+CwBxHA,YACE,mBAGF,4BACE,wCAEA,wBACA,mBAIJ,CxBmYA,gBwBnYA,exBmYA,mCAMA,4EACA,wCAEA,0CAEE,CAXF,iCACA,mBACA,gBACA,qCAEA,CAME,kBANF,wBAOE,yBACA,gBwB3YA,uCAEA,uCAIF,CACE,uCAIA,yCAEA,CAdF,gBAeE,2BAEA,gBACE,uCAKE,2CACE,wCAMJ,2CAEA,CAdA,oBAcA,+BAEA,gBACA,oBACA,8GAGA,0DAMI,uCACE,YACA,OACA,WAGF,8DACE,SACA,YACA,6BAGF,6NAIE,cAMR,kCACE,6FAIE,yDACE,gBACA,mBACA,4BAGA,gFACE,YACA,kBAGF,yCAKE,aALF,aACE,sBAIA,kEAMJ,iFxBoEN,YACA,mBACA,4BACA,gCAnKI,2BwB4FE,0DxB2EJ,kBAvKE,0BwB4FE,6CxB+EJ,cwB5EM,yDACE,8BAIJ,2CACE,8BACA,CACA,oEAEA,cAGF,wDACE,sCAGF,+BACE,qBACA,YACA,iBACA,8EAQI,+LAEE,yBACA,kBACA,iBAMR,6CxB0BN,+CACA,UACA,wBwB5BM,mCxBtIF,yCwBsIE,wCxBqCJ,0CwB9BI,2BxBkaN,CA5YA,iCwB1BQ,mBACA,gBxB1IJ,qBwBsIE,iBxByaN,eAzEA,kBACA,CwBjWM,wBxBiWN,yBACA,uBAEA,gKCziBM,gBDyiBN,uNACA,CADA,oBACA,gCACA,kBAEA,+BACA,kBACA,iBACA,kBACA,qGAEA,oFAEE,0BACA,yEwB1WI,2FxBkXJ,6BA/fE,WwB6IE,4DxBsXJ,kBwB9WI,oDACE,oCAEA,+DAEE,0BAGF,mDAEE,iDAGF,gBACE,4BAIF,0DACE,iBACA,kBAGF,mHAKA,iCxBrLJ,wEwB+LA,axB3LA,qBwBpFN,6DA0RI,aAHE,uBAGF,iPAwBA,YACE,4BACA,oGAOE,wDAQI,YACE,cAKF,2GAKF,oCACE,6DxB3PN,kFwBsQF,eACE,sDAOF,uCAIA,mBACE,gBACA,iBAGF,oBACE,yBAQM,qDACE,kBAIF,gFAKA,0EAEE,eACA,2DAQA,aAGF,CAPA,mBACE,aACA,mCAEA,CAGF,gFAGE,WACA,sDAGF,kBACE,0BAEA,aACA,sBASF,qDAMA,+KAMA,wBACA,qBAEA,oFASF,cALE,0DACE,iCAIJ,uCACE,yBAGF,sDACE,kBAEA,iFAEE,iBAIF,yDAEE,wBAIF,0CACE,8XAkCN,CAnCI,UACE,CANA,cACA,CAuCN,oBxB1ZF,6BwBgaF,eACE,iCAIJ,+GAIE,2HAIA,wBACE,qBCjgBJ,6BACC,CAAD,wBACA,uDAME,oCACA,CACA,kBACA,2BAIA,uDAMA,mEAMF,gCACE,4DAIA,UAKE,sEAIA,iBACE,6DASF,WAHF,SAGE,6DApDN,uBA2DI,CzByBE,WyBvBF,6DAEE,SADA,QACA,6DAKE,YAMA,gDACE,2BAEA,0BAQF,oCACE,8BAIF,0CAEE,kBAGF,0BACE,mDzBZJ,qCyBmBF,WACE,4CACA,iBAKE,4DACE,gBACA,+BASF,iDACE,0EAON,eACE,yEAKI,6ECrIR,cAFF,mCAKI,iEACE,8BAEA,sF1B0eJ,wCACA,WACA,gBACA,qB0B1eM,sBACA,4DAIA,SACA,qE1BseN,+FACE,0BAEA,yHACE,6D0BnfA,8G1ByfF,yBA7aE,e0B5EA,uJAaE,8E1B4bN,c0B5bM,mC1B4bN,8DAEA,mCACA,8DAGA,yF0B/bQ,iB1BkcR,gPAEE,iF0BhcE,kFAEE,UAEA,yHAIA,+FAIA,mH1B0cN,2BACA,uBACA,kBACA,E0BtcQ,0J1BwcR,2GACE,8CAEA,sGACE,iBACA,kBAxaA,kB0B5CE,6G1BydJ,gBA7aE,wC0B5CE,wG1B6dJ,U0BhdM,kJ1B4ZR,0CACA,8BACA,mBAGA,oBACA,CAHA,8CAGA,yDACA,2CAEA,0QAGE,2BACA,kB0BzZQ,qB1BeN,8C0BfM,2LACE,CACA,kBASV,qBAIE,C1BAA,8C0BAA,yFAKM,CAnBE,CAmBF,gHACE,QACA,iH1BPR,uB0BpFN,iCA6GI,YACA,CAZQ,4BACE,mBAWV,iBAGE,CAdQ,eACA,iB1BhBR,C0B6BA,2DAKM,mHACE,yBACA,yHAOF,iBACE,yCACA,gHCzHV,cACA,2BAGF,gHAOE,cACA,CACA,mHAQA,6DAMF,+BAEE,qDACA,6BAIF,CAKE,oBAGA,CARF,wEAIE,sBAIA,iPAmBA,6BAGF,CACE,+B3BWE,kF2BHA,wBAGF,cApFJ,6LAsGQ,0HAcF,sBACE,WAGF,uBAIA,aAjBI,4BAIJ,mBAaA,iBACE,CAdF,eAEE,iBAGF,CAYA,eACE,CAJA,SAGF,mBACE,eACA,kHAUF,yBAGF,wHAWE,iCACE,yB3BvEF,+G2BkFA,cAKE,0IAOF,cACE,mHClLN,6DAEA,+BAGE,qDCNJ,cACE,eACA,CAKA,qBALA,wEAGA,sBAEA,+OAgBE,6BACA,eAGF,0CAEE,oOAaA,0BACA,uBACA,0BAEA,kBACA,kBAEA,gBACE,4DAOF,2HASF,4BAEE,0BACA,kHAGA,uD7ByYF,2IAEA,QACA,mHAGA,iDACA,mHAEA,kJAEE,oCACA,sCACA,iB6B5YE,+C7BiNJ,C6BjNI,sB7BiNJ,OACA,kBACA,6BACA,kBACA,aACA,sBApNI,+B6BFA,oE7ByNF,MACA,CAxNE,uC6BFA,C7B0NF,sCAxNE,C6BFA,8B7B8NF,+BA5NE,C6BFA,c7BEA,gB6BFA,6B7BEA,iDAgOF,eAhOE,WAoOF,CApOE,wB6BFA,+B7BsOF,wC6B9NE,0B7BsGJ,kCACA,iBACA,0BACA,gBACA,yBACA,aAEA,qCACA,gEACA,iBACA,2CAtHI,8C6BMA,a7BqHF,mB6BrHE,oC7BqHF,yCAIA,qBAFA,YACA,+CACA,CACA,kF6B1HE,8C7B+HF,oBAEA,sCACA,8CACA,gDACA,8B6B5HE,+D7BkIJ,mDACA,0BAEA,gBACA,2B6BnIM,mCAHF,uD7ByIF,kBAvJE,C6BcA,kB7BdA,uB6BcA,kFAUA,gF1BvFkB,CHgOtB,mCAEA,0BACA,0B6BzIM,gB7B3BF,c6BwBA,2E7B+IF,sC6B/IE,mE7BmJF,eAOF,6B6B7IM,C7B2IN,iBACA,kB6B5IM,4CAHF,kB7B8IJ,C6B9II,mC7B8IJ,sB6B9II,CAGE,6BAHF,Q7BlCA,e6BkCA,C7BoJF,iBAtLE,C6BkCA,yCAUA,wCAVA,sCAUA,CAVA,OAUA,sB7BmJJ,gCACA,QACA,kBACA,iCACA,oB6BpJM,qEAHF,wB7B0JF,wB6B1JE,mB7B5CA,Y6B4CA,iB7B8JF,C6B9JE,uBAQJ,c7BsJE,gB6BtJF,UACE,2BACA,eAGA,6BACE,qBACA,aAIJ,4CACE,SACA,iBACA,CAFA,WAEA,qDAGF,6BAEE,gBACA,kBACA,mHASE,0GAQA,+BAEA,4BAGF,4DAEE,eACA,yEAKA,mRAKE,gJAME,iCAEA,mBAIA,+EACE,yB7BzHN,gF6BqIF,qBACE,EACA,yBAGF,6DAME,iBAGF,wFAOE,0CACE,6DAIA,kBAGF,6DAcA,uCAEA,wC7BlLA,wC6B2LA,wCAIF,CAxBQ,wFACE,gBAOV,yBAgBA,yBACE,2DAOE,uCAIJ,6EAQM,0CAbJ,mCAaI,2MAIE,yCAGF,CAPA,iBAOA,6D7B1NJ,uC6BmOF,mBACE,gBAGF,kBACE,mBAEA,yBACE,2DAMJ,iBACA,2BCpUA,2DACE,sXAIJ,iB9B2eA,6DAEA,uC8BzeE,mB9B4eF,qDACE,yBAEA,2DACE,iBACA,CAxaA,0B8B7EJ,2D9B0fE,iBA7aE,6DAibF,gCAjbE,kB8BpEA,iCACE,iaCjBR,gBACE,kBACA,CDeM,eCfN,uEAKE,eACA,0BAGF,WACE,wZACA,uDAIA,YACA,uVAGE,mJAIA,oCCxBJ,gCACA,+CAEA,oBACA,qBAIA,uBACE,yBACA,iBACA,uEAKA,uBACE,uCAKF,eACA,8CAEA,iBACA,gDAGA,kBACA,2FASA,uCACE,6GAMA,2hBAeE,2HAOF,8HACE,uCAEA,+dAoCJ,CACE,8CAEA,4BAEA,kBACA,gBAGE,oGACE,gFAKJ,0DACE,8CAEA,qGACE,CAGF,ycAyCA,CA5CE,CA4CF,uGACE,gCACA,oZAgBF,8GACE,8ZAwCA,wBAIJ,qEACE,8CAEA,uGACE,aAGF,2HAIA,sGACE,yBAGF,6BAEI,8BAQA,aAGF,kBAKE,CAOV,WAPU,SAOV,oChCwPA,oFgCnPE,+XAQA,+TACA,CADA,6BACA,sEAIA,2CAIA,0GASE,kBAIJ,0GAME,mBACA,0GAME,oBAIJ,CAGE,gCAGA,oBACE,4BACA,eAEA,iCACA,0GAOA,kCAIF,8BACE,sEAMF,6CAGE,0GAOF,kBACE,0GASF,qBACE,0GhCpSA,mBgCpFN,CAyYM,gCACE,oBAGF,6BACE,kCAGF,0HAOE,kCAIF,CACE,mBAGF,CAJA,6BAIA,sEAOF,6CACE,0GAMA,kBAEE,0GASA,qBACA,0GASF,oBACE,CAOA,gCAEA,oBACA,CACA,2BAKF,kBACE,iBAGF,iBACE,yGAQF,kCAEA,CAIA,oBAJA,6BAIA,sEAOF,2CAGE,0GhC7ZA,kBgCpFN,0GAqgBM,6HACE,gVAGF,gCACE,yBACA,sCAGF,eACE,yBACA,6BAIF,6CAEE,6BACA,gBACA,CAGF,0ZAQA,wHCrce,eDucf,+BACE,eAGF,+BACE,eAGF,8BACE,oBAGF,YACE,gBAKF,sDACE,CACA,UACA,CAFA,SAEA,6CAIF,oBACE,4BACA,2BAIF,+BACE,0DAMF,iDAEE,oBAIF,mDACE,WACA,wCAIF,oBACE,WACA,CAEA,WAIJ,CANI,2BAEA,CAIJ,8CAEE,oBACA,uBAGF,6CAEE,oBACA,YAGA,WACE,CAJF,kBACA,CAEA,iCACE,gDAEA,oBACA,WAIJ,CAEE,WhCjiBA,CgC+hBF,mBACE,gBACA,CAFF,gBACE,ChChiBA,2CgCpFN,oBA0nBI,WAEA,CACE,WAEA,CAHF,kCACE,CAEA,mCACE,oBhC5iBF,8CgCkjBF,oCAIA,uCAGE,wDAMA,wDAIA,oBACE,CADF,UACE,kCAMJ,qBACE,YAFJ,eAEI,iCAGF,qBACE,sCAWF,UAGF,CAIA,kBAIA,CARA,cAQA,8BEprBI,sFACE,6CAMN,qBACE,CADF,WACE,gBACA,kBlCsEE,mBkCjEA,qDACE,WAMA,gFACE,iCAQF,gDACG,CADH,eACE,iClCgDJ,+BkCvCA,kBACE,CADF,gCACE,+BAMA,6DACE,2BACA,qBCrDR,kBACA,aAEA,oBACE,mCAGF,YnC4PA,oCACA,eACA,wBACA,6BAnLI,8DAsLF,gBAtLE,iCA0LF,kBmChQF,0BnCqQA,uCACA,2BAEA,oCACA,oCAnMI,+bmC/DJ,qBnCyTE,gBAEA,iCACA,sDACA,iCACA,qBACA,cADA,iBACA,iDAhQE,sBmC/DJ,CnCoUE,iBAEA,CmCtUF,enCsUE,0bmCpTF,+BAEE,gBACA,8BAEA,eAEA,CAEA,4DACA,eAIA,uCACE,qBAGF,6BAEE,YACA,wCAQA,iFAIA,iCAHE,WAGF,iEACE,YAIJ,kEACE,+DAIE,qHACE,oBnCCJ,6BmCMF,sBAGE,iBAHF,iBACE,mBAEA,6CACA,qBAEA,CACE,gBACA,CAFF,oCAEE,2BACA,qBAKA,oHACE,iBCzGV,6CAGE,gBACA,2BAGA,0BACE,mBACA,8BAEA,iBAGF,6CAGE,gBACA,iCAEA,gDACA,eACA,iCAEA,6BAEA,2BAEA,2BAEA,iCACE,YACA,sBAGF,+HAKE,sFAEE,cACA,CADA,eACA,yBAGA,mDAOJ,iBAEA,yEpCVF,eACA,aACA,2EoCcI,cAIF,mBACE,2BAEA,mDAMJ,iBAEE,2EAKF,cAEE,mBACA,iBAKA,4BACE,0BACA,4EAEA,mBACA,iCAEA,mBAKA,+DACE,iEAMJ,uBAKE,cAHA,kEACA,iBACA,CACA,kBACA,CAFA,wBAEA,yBAMA,8DACE,yDAQF,uCAEA,qCACA,uCAOA,mFACE,CAbJ,4BAEE,gDAWE,yBAMJ,oBAEE,kFAEA,wCACA,0CAOA,2CAZF,mBAYE,2BACE,oBASJ,0HAEE,yCAOF,2CACE,CAnBE,mBAmBF,CClLN,qBACE,qBACA,6BACA,6BACA,uBAIA,mBACE,iBACA,0BACA,kBACA,kCACA,gCACA,aAGF,uBrCmPA,CACA,YADA,OACA,gBACA,uBqClPE,CrCgPF,wBqChPE,iCACA,CrC+OF,UqC/OE,CACA,qCrC8DE,oBqCnEJ,4BrCmEI,oCqCnEJ,6FrCmQA,wBACA,mFqCxPE,mBAEA,2ErC2PA,6BAtME,kBqC3DJ,+HrC2SA,0BAEA,0BACA,2BACA,oBACA,CAFA,kBACA,cACA,+CACA,gDqCpSE,6BAJF,CAGE,eACA,CAJF,0HrC+SE,yBACA,gCACA,gCACA,qDAhQE,qBqClDJ,iBrCuTE,0BAEA,0BACA,iBACA,eAFA,aACA,eACA,iCACA,iCACA,oCqCtTF,CrCsTE,gBqCtTF,cACE,kBAEA,CrCgTA,kBqChTA,wBACE,CACA,iCAOF,8FACE,+BACA,2EAGE,qlBCtCJ,aACA,CDqCI,WCrCJ,oCACA,WACA,mCAGF,yBAEE,kBACA,CAFA,SAEA,sBACA,gBACA,4BACA,UAGF,wBtCMA,aACA,kBACA,MACA,oBACA,aACA,CACA,6BsCRA,2DtC6CI,oBsCxCF,uBACA,qCAGE,gCACA,qCAIA,iOAwBA,wBACA,4BAGF,wBACE,CA9BF,UACE,CA6BA,sBtCEA,+DsCKF,iOClFA,yBAEA,+NA0BA,wBACA,CAEA,oDA7BA,WA6BA,WACE,wEAKF,+NAoBA,yBAGA,qNAsBA,oDAGF,wBAEE,CA3BA,WA2BA,WACA,wEAKE,qNAcF,yBvC9BE,oNuC0CA,wBAGF,4BAEE,wBAGF,CvClDE,WuCkDF,WACE,WACA,6DvCpDA,oNwC3EF,8GAKA,+BAEE,gBACA,gBAGF,6BACE,iBACA,iBAIF,oBACE,CADF,aACE,4CAKF,yCvCvBS,uBuC6BT,gCACE,iBACA,0DAKA,WACA,wBAUF,wBAEA,CAPF,uCAEE,mBACA,gBACA,CAGA,cAHA,gBACA,CAGE,kBACA,yBAGF,wCACE,2BAEA,wCACE,CxCiBF,0BwCpFN,uBAkFI,cAEA,CApFJ,gCA2EI,kCAEE,CAOF,aACE,CARA,gBxCOA,CwCCA,kBACA,CxCFA,wBwCEA,yBAEA,yBAEE,cAGF,oDAEE,cAGF,wBAUA,uCAEE,qCAGF,uCAGE,0CCnHN,yCACA,yBACA,CDgGM,4BAIF,+BACE,CCrGN,aACA,CDoGM,gBCpGN,yBACA,sBzC4ZA,wCAEA,0CAEA,wCyC7ZA,0CzC4EI,0CyCzEF,CAHF,mBAGE,2BACE,sBAZN,wCAiBI,0CACA,wCAEA,yCCpBJ,2C1CoQE,CAhLI,mBAgLJ,kEAEA,WACA,C0C/PE,Q1C+PF,iB0C/PE,iG1C4EE,6B0C/EJ,kB1CyQE,qJ0CzPE,0BACA,CAIA,4DACE,6CAEA,uBAKN,gB1CiPA,sCACA,gBACA,oBACA,+C0ChPE,uBACA,gBACA,2B1CkPA,kBAtME,kB0ClDJ,kB1CkDI,6B0ClDJ,kBASA,mC1CsRA,+BAEA,CACA,eAEA,CAHA,iBACA,CAEA,uCACA,CACA,uCACA,CAFA,sCACA,CACA,sDACA,2BAtPI,mC0CzCJ,kB1CmSE,0BAEA,oCACA,4BACA,gDACA,sDACA,yCAhQE,a0CzCJ,kB1C8SE,kBAEA,+CACA,kDACA,2BACA,0CACA,yD0C/SA,wBAEE,uBAOF,iFACE,qCACA,4BAGE,gGACE,+BAOV,0BAEI,gBCxEJ,2BACE,aACA,kBAEA,uB3C4LA,yCACA,0BAEA,kBACA,qBACA,sBAEA,gCACA,CADA,aACA,kCACA,0D2CjME,aACA,C3CiMF,wB2CnME,CAEA,WAFA,yBACA,CACA,SACA,oB3C0EE,mB2ChFJ,mB3C0ME,0BAIA,sBACA,CAFA,wBAFA,cAEA,aACA,oBACA,0EACA,8BAhIE,C2ChFJ,wB3CqNE,uBAEA,wDAEA,mBADA,+BACA,CACA,WADA,sBACA,4B2CjNF,kB3CoTA,qCACA,qBACA,CACA,8BAEA,sCACA,mCACA,gDACA,8BACA,mD2CzTE,WADA,WACA,QACA,kBACA,W3CiEE,+B2CvEJ,iB3CiUE,6DAEA,CACA,+CACA,6CACA,yCACA,+CAhQE,4BAwQF,mCACA,wBACA,C2CjVF,gC3C8UE,mDACA,CAEA,gBAFA,yBAEA,kBACA,oD2CzUF,cACE,2BACA,2BAEA,cACA,sDAKA,sBAEA,yBACA,qB3CiDE,mB2C9CJ,6BAKE,oCACE,kB3CwCA,sB2CpFN,aAiDI,mCAEA,wBACA,0BAGA,yCAEE,gBACA,0BACA,YAEA,2BAKF,kCACE,CANA,iBACE,kBAKF,uBAGF,WACE,kBACA,8E3CYA,wD2CFF,aACA,WAEA,6CAMA,WAJE,aACA,YAGF,oCAKE,YAGF,CAJA,cACE,WAGF,oCACE,a3ChBA,mB2CpFN,oCA0GI,qBAEA,8CAIE,SAFA,aACA,WACA,0BAGE,aCnHR,CDkHM,uCACE,CCnHR,WACE,iBACA,oCAEA,eAEA,kB5C6RA,qCACA,iBACA,kDAEA,eACA,yB4C/RE,iC5C2EE,kC4C9EJ,W5CqSE,2BACA,gBAxNE,yCA4NF,wBA5NE,mCAgOF,mBAhOE,oCAoOF,a4ClTF,U5CkTE,C4C5SF,mCAEE,WADA,WACA,sFACA,WAGF,6CAKA,YACE,CAJA,SAGF,YACE,aAGF,2CAGE,yCAGA,mBACE,qDACA,cAEA,4BACA,mBAEA,kBACA,gBACA,iBAEA,sCACE,iBAGF,2BACE,+BAGF,4BACE,eAGF,oCACE,4CAKE,0CAKA,CAKA,kBACA,gBAIA,CAVA,mBACA,iBASA,CACA,CAIA,2BAJA,iB5CMJ,C4CFI,iBACA,uCAKA,mBAIA,gBACA,iBAMR,mB5CfI,yB4CpFN,iBAwGI,iBACA,2BAGE,iBAGF,qC5C3BE,iC4CiCF,kBACE,wBAGF,WACE,wB5CtCA,oB4CpFN,sBA+HI,2CC7HF,gCAEA,mBACA,6BAGE,oBAQA,cAPA,gCAEA,kBACA,gBACA,kBAGA,kBAEA,kBAGF,CARE,wBAGA,CAKF,iBACE,yBACA,mBACA,cACA,2BAEA,mBAEE,cACA,kB7CsNJ,uCAGA,qCA0ZA,iFACA,yCAEA,C6CjnBE,2C7CiNF,iCAgaA,yBACA,gBAGA,wCAEA,0CACA,wC6CpnBI,0CAJF,0C7CwNA,CA2ZF,mBA3ZE,2BAvKE,gB6CjDF,wC7C0nBF,2HAKA,4C6C/nBE,mB7C+nBF,sIAGE,4KAKA,0B6CjoBE,6CACE,C5CrBA,C4C0BN,4EAQE,gKARF,iCAEE,kBACA,gB5C7Ca,C4C8Cb,oBACA,gBACA,CAEA,oCAEE,iCACA,CALF,wBAKE,yBACA,wBAMA,uC7C3CJ,qC6CiDE,wCAGE,yCAEE,C7CQF,c6CpBF,qBAYI,2BAEA,wBAKF,sCAGE,wCAIJ,gFAEE,CATA,iBASA,0BCtFJ,uCAGE,qCACE,uCAEA,0CAGA,yCAGE,CD4EF,4BC7FJ,eACA,gBACA,iBAGA,CAYM,kBACA,iBACA,kCACA,yBAEA,wBAEA,wC9C2DF,0C8CvDI,wCAdJ,0CAkBI,0CAKF,CAdA,mBAcA,2BAEA,wBAEA,wCAEA,0CAGA,wCAIA,yCAEA,2CAEA,CAbA,mBAaA,2BAEA,kBACA,iBACA,kBACA,gBAEA,mCACE,YACA,mEACA,wBAEA,WACA,yBAKJ,yBACE,iBACA,0BAEA,iBACA,+BACA,sBACA,mBACA,gBACA,wBAGF,+BAMI,gIACE,kBACA,CAEA,gIAEE,4DACA,SACA,+BAEA,C9CgJZ,yEACA,kBACA,2BAnKI,oC8C2BJ,iB9C4IE,wBAvKE,+B8C2BJ,gB9CgJE,iB+C/PJ,4CAEE,0BAEA,gCACE,kBACA,gBAGF,oCACE,iCAEA,mBACA,gBACA,sBACA,gBACA,CAGF,uCAIE,CAPA,wBAOA,yBACA,iBAGF,oBACE,oBACA,2BACA,iBAEA,4CAEA,iBAGF,0CAEE,iBAEA,kBACA,2BAGF,oBACE,qEAKA,4IAWE,aACA,CACA,eAGF,mBAJE,aAIF,yBACE,gDAKF,gEAEE,CAFF,kBAEE,kBACA,WACA,qCAGF,uDAEE,wDAEA,mBAIJ,uDACE,wBACA,YACA,qBAEA,oCACE,sGAMA,6YACA,gBAEA,0BACA,CAGF,iBACE,CADF,oCACE,UACA,kBACA,8EAIF,8CACE,c3C9GU,iB2C+GV,CACA,6BACA,kBACA,wBAGE,wEACE,UACA,eACA,CACA,iBAGF,6CACE,qLADF,uCACE,kOAEA,4CAON,6CACE,4CAEA,iBACA,uGAKI,0BAEE,iIAaJ,6DACE,iHAPE,uDAOF,6dAcJ,wBAII,iFAEE,kBACA,CAIF,SAJE,gBAIF,mFACE,wBAEA,aACA,eAGF,wFACE,cACA,UACA,qGAMJ,gBACE,+YACA,gBACA,iBACA,mBACA,gBAKN,2DAEE,gBACA,yBACA,kCAGA,cAEA,wDACE,CAOJ,cACA,C/CrJE,e+C0JA,+EAbE,iBACA,cAKN,6BACE,CACA,iB/CrJE,C+C0JA,gBAUM,CAVN,6BACE,aAGF,sBAKI,gDACE,oC/CpKN,oN+C4LA,C/C5LA,W+C4LA,4DAEE,kCAEA,+BAGF,CAJE,2BACA,CAGF,4BACE,e3C/QU,C2CkRZ,kDACE,+BAKF,gJAOM,+BAEE,+IAWF,+NAyBN,CAzBM,WAyBN,0CACE,4CAKE,0DACE,iBAMR,6DAGE,+FAEE,iBACA,EAGF,sGAGE,kB/CnRF,C+CkRE,gB/ClRF,mC+CyRF,kBAEA,mCACE,S3CzWW,sB2C4WX,uEAGE,eACA,CAFA,cACA,CADA,yBAEA,kCAKA,qFACE,gBADF,iBACE,CACA,4BAIJ,OACE,eAKF,CANA,cACE,CADF,yBAMA,8CACE,mBACA,kBACA,iKAMI,0ZAEA,4CAIF,qGAIE,oFASJ,+XAsBE,WACE,+aACA,YACA,8FAOJ,8XAqBI,8BACE,iBACA,YACA,qDAMJ,sDACE,6vBA0BR,aAGE,8CACE,qDAKE,mFACE,yFAON,WACE,YACA,sBACA,0FAIE,wBACE,WACA,wGAKA,aACE,YAQR,aATM,SACE,CAQR,+FAMI,8XA+BF,8BACA,iBAGE,iEAQJ,SARI,kBAEE,kBACA,mBAKN,uCACE,uDACA,cACA,gBACA,qCAIE,wGAGE,iBAKN,qDACE,gBACA,gBACA,oFAKE,sFAEE,mBACA,6BAIA,gJAUJ,yCAGF,0EAOF,iBACE,C/CtjBA,4B+CqjBF,CACE,wBC1oBN,yCAEE,yDAKE,eACA,CAGF,oDAEE,gBAGF,iCAEE,yFAMI,iBACA,qDAKF,2BAEE,6FAII,aAGF,2EACE,oBACA,iBhD2CN,oGgD9BA,YACA,6BACA,8CAIE,4BACE,gEAKF,4BACE,uCAGA,2GASE,2HA/EZ,aAyFI,6CAGE,mBAGF,mBACE,6BACA,oCAEA,0EAMI,iBAIF,CALE,4BACA,CAIF,wBACE,4GASE,iBACE,iEAIF,oEACE,oBhDzCR,iBgDpFN,mBAsII,iBAEA,oGASI,8ZA6BA,8BACE,+FAQE,yBACE,4CAIF,qEACE,kDhDoIZ,0GAKA,WACA,2HAGA,wBAtPI,CAsPJ,qBAtPI,oFA4PF,8ZAeA,8BiD5VF,YjD8cA,oFACA,iBACA,gBACA,yFAGA,gDACA,YAEA,yFAIE,aiDvdF,uBACE,YACA,4FjDwcF,yCACA,gGAIA,8ZAJA,8BACA,iEAGA,mBACA,4FAGA,qEAEE,eACA,2BAzYE,uCiDpDF,qDAII,iBjDgDF,iCiDpFN,oFCCE,aACA,gBACA,iEAKE,oBAIF,CALE,iBACA,CAIF,gCAEE,oFAOF,YAEE,iBAEA,sGAOA,YlD6HF,4GAGA,4BACA,6CAGA,4CACA,2GkD9HI,gBACA,kBlD4CA,gEAyFF,wDACA,iCACA,oFA3FE,6BkDhDF,YlDiJA,qDAEA,gBACA,kCACA,mCACA,oGA0DF,YACA,iBACA,YACA,qDAnKI,oBkDxCF,iClDwCE,CkDpCA,iBlDoCA,iCkDxCF,oFAaF,YACE,iBACA,YACA,0FAME,WACE,6CAMJ,iCAGA,4CAIF,iCAEE,2BACA,0DACA,CAEA,yCAEE,0BAEA,uBAGF,oBACE,CACA,gCAEA,0BAKF,mBACA,sBAIF,YACE,gBACA,yDAOF,6BACE,iBACA,oDAGE,cAIJ,iBACE,wEAEA,YACE,2EAMA,0CAIA,yBAGF,sBAEE,iBACA,CACA,qBAGF,oBACE,8BAIF,aACE,UACA,6CAEA,aACE,WACA,0BlD5EF,2CkDmFA,aAGF,2BACE,6BAGF,oDAKE,kBACA,yEAKA,qCAEA,0BAEA,cAGF,uBACE,sBACA,wCACA,oBAGF,8BAEE,aACA,UAGF,6CAME,wBACE,uCAGA,2CAIA,aACA,WACA,gBACA,2EAUJ,sCACE,yEAQF,gCAIA,2BACE,cAGF,iBAEE,sBAEA,kBAGF,sBAGE,6BlD9KA,CkD6KA,SlD7KA,CkDyLA,sHAIA,6CAIA,CACA,wEAOA,uCAIA,gDAIA,6BAGF,CAHE,SAGF,CAWE,sHAQF,6CC1TF,yEnDmOA,uCG/MsB,CHgNtB,aAjJI,uCAuJF,qCAvJE,uCmDtEJ,mFAME,CnDiNF,4BAEA,eACA,gBmD7NE,iBASA,yBACA,YAGA,8MnDqIF,CmDvIE,mBnDuIF,2BACA,YAEA,wCAEA,0CAEA,wCACA,yCACA,2CACA,CARA,mBAQA,gBmD5II,6DAHF,0LnDmJA,sBAEA,yCACA,6BACA,iCACA,yDA5FE,kBmD5DF,sMnD6JA,yEAGA,sBACA,2DACA,6BmD5JA,6NnDkKF,+BAEA,4BAEA,yBACA,6BAEA,yEAEA,6EACA,6BAtHI,gCmDtDF,8LnDgLA,kBACA,aAEA,oDACA,gCACA,eACA,CADA,sBACA,yCAhIE,wBmDtDF,+MnD2LA,yBAEA,mCmDvLA,+JnDwLA,iCACA,mCACA,sCmD1LA,enDgMF,sBANE,wBAMF,yBACA,uBAEA,uCmDhMI,uCnD6CA,wCmDhDF,0CnDkMF,gBmDlME,oInDgDE,wCmDhDF,oLAOA,kBnDoMA,QmD3MA,mBAOA,qInDyMF,mCmDzME,2BnD6MF,CAJA,iCACA,CADA,eACA,cACA,CACA,UACA,yCmDzMI,8CAJF,kMnDgNA,sBAvKE,yBmDzCF,oMnDoNA,kCmD7MA,8MnDkNF,sCAEA,gCmDjNI,yBACA,oBnD8BA,sBmDlCF,4MnDwNA,wBAtLE,emDlCF,wMnD4NA,emDrNA,6JnD2NF,aACA,CmD5NE,sEnD4NF,uBACA,YACA,uBmD3NI,wBnDwBA,wBmD3BF,yNnD2BE,4CmD3BF,sKnDqOA,SmDrOA,MnDqOA,WmD9NF,uBACE,UACA,aAGF,uBACE,2BAIA,0CACA,iBACA,6CAIA,CAGF,oBACE,uaAEA,iBACA,gBACA,6BACA,kCACA,UACA,eACA,uCAKE,mBACE,gBACA,iBAKN,CACE,mBAII,wDACE,2BnDsIR,kHmD5HI,uBnDpCA,mCmDiCF,uMAqBI,mCAOF,yCACE,wCAIF,0CAEE,wBACA,CAvBJ,iCACE,mBAGF,qCACE,CAGE,wCAeA,yBAEA,sMAoBE,uCAIJ,uCACE,wCAGE,0CATA,gBASA,2BAEE,sMAUA,uCAEA,2CAGF,wCACE,2CnDrHN,CmD8GM,oBnD9GN,wMmDgJE,uCACE,wCAIF,yCACE,uCACA,wBAGF,CAnBF,iCAEA,kBAGF,qCACE,yCAaE,+NAmBF,uCACE,qCAGE,wCACG,CAAD,wCAEA,CAPN,mCAOM,2BAIF,sMASA,8EAEE,uCnDnMN,yCmD4MF,CAdQ,iBAcP,CAAD,uMAyBI,uBACE,CARJ,uCAGF,mBACE,gBAGE,kBACE,mBACA,yBAGF,sMAME,wcAeF,uBAGF,CAbI,uCAMJ,qDAQE,mBAIA,+NAUE,kBAEE,2BACA,sMAIA,yNAEA,uBACA,CAHA,0EACA,iBACA,CACA,mBACA,yBACA,sMAgBJ,4CACE,sMAmBA,yNCrYJ,uBAQI,CAhBN,gCAIE,kCACE,iBAGF,yBAQI,+NAgBN,cACA,iOASA,cClDJ,eACE,kBACA,gBACA,oBACA,UAIA,qBrD4NA,oCACA,wBACA,kBACA,kBACA,YqD7NE,mBACA,8XAYA,qOACA,mBACA,mBACA,0DAEA,mCASA,uBACA,CARA,uCAIF,mBACE,iCACA,oBAEA,yBAEA,qBACA,kBAGF,2BACE,qBAIA,iBAEA,EAKE,wGACA,kBAEA,4GAOF,mDAEA,iBACA,mBACA,qCAIA,qCAGE,4BACE,UAGF,iDAEE,mBAGA,qDACE,kBAIJ,oDACE,sBrDTF,CqDQA,YrDRA,0DqDgBF,wBAEA,2EAEE,kBAIF,uCACE,yFAMA,wBAEA,wCACA,2BAEA,kBACA,iCAGF,8EAMM,0CAGF,0BAEE,8BAGF,uDACE,aACA,sBACA,uBACA,mBAEA,uCAGF,2DACE,wBrDhEJ,0DqD0EF,gBACE,CADF,YACE,6DAGA,qGAKE,wBACE,WAKN,2BACE,kBAGF,UACE,uBAGF,oCAKE,sBAGE,CAJJ,YAII,0DAEE,wBAGF,2EAKA,kCACE,4aACA,0DAEA,kBAEA,aASJ,gIAME,kFC1NN,gBAIA,0EAMA,YtDwPA,yFAGA,8ZsDrOI,yBAIA,gDACE,uBAKN,CASE,0HACA,oCAEA,sBACA,uEAGA,wBAEA,2EAMA,mCACE,aAGF,4EtD6LF,8XsD1KI,iEAIE,0IAME,qEAYF,4FAGA,0FAKF,0CACE,4EAIA,0BACA,uBAEA,8DAIA,8EACE,CAGF,0FACE,wDAIF,0DAGE,cAFA,uDAEA,sDAEE,4DAYN,sJAQA,mBACE,gBACA,crDhKK,kBqDmKL,4DACE,uCAIJ,mCACE,kBAGF,sCACE,yBACA,erD9KK,iBqDgLL,wDACE,gBAGF,iFACE,8XAeE,WASN,4BACE,iBAGF,uCACE,CALF,kBAKE,UACA,uBACA,CAFA,UAEA,CrD1MI,qBqDiNN,aACE,CANA,oCACE,CAIJ,wBAJI,uCAIJ,CACE,8BAIF,4DACE,UACA,yBAEA,qFAIA,mGAIA,qEACE,wBACA,mCAGF,uDrDrPS,CqDwPP,iGAEE,WAFF,aAEE,WrD1PK,oDD+EX,gBsDqLF,0DCxQF,sBvDkSA,qCACA,kBACA,gBACA,sBAlNI,auDjFJ,CvDqSA,uDApNI,CAmNJ,iBuDpSA,uCvDySE,kBAxNE,uBuDjFJ,mBvD6SE,mBA5NE,sBuDjFJ,mCvDiFI,uBuDjFJ,mBvDqTE,iBuDjTE,0CACE,wBAMJ,sCAEE,sEAIA,YACA,qEACA,mCAGF,2BvD0PF,4BACA,uBACA,0DuDzPI,sBACA,2BAEA,8BAEA,gBACA,sBATF,cAUE,qCvDiDA,CuD3DF,wDvD2DE,iBuD3DF,oEAgBE,qCAEA,eACA,CACA,6BACA,kBAME,4CACE,4GAGA,YAIA,qEACE,8ZAoCN,yBACE,WAGF,2BACE,iCvDlBF,2BuDyBA,qBAII,qEACE,wDAOA,wDACE,uEAEA,uBC1HZ,kBACA,iBACA,cAEA,CDsHY,uCAWV,mBCvIJ,gBACE,iBACA,CAIA,gCxD8OA,4CACA,WACA,yBACA,sBACA,iBwD/OE,2BAEA,sBACA,iBxDwEE,iCwD9EJ,WxDqPE,kBAvKE,+BwD9EJ,oCxD8EI,mDwDnEA,6CxDmEA,yBwD9DF,wBxD8DE,6BwDxDF,6BACE,yBCzBJ,yBAGA,CAPF,uBAEE,CAKA,wBALA,qBACA,CAIA,yBAJA,sBACA,CAGA,6BAGE,gBACA,4BAGF,SACE,mBACA,2CAGF,kBzD8SA,4BAMA,eALA,uCAEA,mBAEA,iCACA,eACA,oDACA,4CACA,2ByDnTE,mDAEA,sBACA,qBAEA,gCzDwDE,+CyDjEJ,kBzD2TE,mEAGA,wFAEA,mBACA,oGyDjUF,kBzDsUE,8CAEA,qBACA,gCACA,oDACA,+EyD/TF,wBACE,gDzDkdF,sBG3dW,oBH6dX,mEAGA,kFAGE,uCACE,yEyDxdJ,wBzD8dE,CA7aE,6ByDjDJ,mDAOA,8HAQA,yBACE,+BACA,2GAMA,4DAGA,wBzDuBE,qByDpFN,WAiEI,wDAIE,wBzDeA,2DyDRA,wBzDuMJ,oDAEA,wBACA,qBACA,uE0DtRA,sF1D6RE,uC0DtRF,6EAME,wBACA,8BAEA,uDAKA,sIAME,wBAEA,mCACE,4CACA,UAIJ,qEAEE,+BACA,kDAGE,qGACE,4DAON,yBACE,oBACA,iEAKA,gFAGE,uCAEA,uEASE,oHACE,6GAUN,wBCjFJ,qBACA,YACA,wBACA,uBACA,YACA,mCACA,iBAEA,iCACE,mBACA,gBACA,sBAEA,iBAEA,kBACA,CAHA,wBAGA,yBAGF,gBACE,mBAEA,gDACA,iCAGF,2BACE,gBACA,eAEA,2BAEA,kCACE,oCACA,cAMF,6CANE,oBACA,wCAcA,CATF,sBAKA,mDAIE,uBAYF,uBAIF,eAfI,gCAKJ,kBAEE,gBACA,iBACA,CAMF,aACE,kBACA,gBAGF,kC3D0dA,iBGzgBY,CH0gBZ,kB2DteE,wB3DseF,yBAEA,sB2D1dE,yCAEA,sBACA,c3DOE,6C2DbJ,sB3DoeE,C2DpeF,uB3DgeE,C2DheF,Y3DgeE,iBAndE,CAmdF,WAIA,oB2D5dA,gDACE,oEAEA,mGAGA,oCAEA,6BACA,uDAEA,mCACA,wFAKE,oBACE,uBAKF,iBACE,iBAKN,8CACE,CACA,iBACA,CAFA,WACA,CACA,8BAIF,YACE,6CAEA,oBACA,2BAGA,iCACE,gDAEA,4DAQN,oBACE,CACA,qDAEA,oEAKE,wBAMF,6CACE,mBAGE,6CACE,iCACA,2BACA,uCAII,mBAGF,gBACE,iBAIA,CAIA,gCAOV,mCAXU,mBAWV,yBAOM,uEACE,0BACA,2CACA,0BACA,6CACA,0BACA,kBACA,2BAGF,8DACE,kBAGF,gBAEI,CAIA,0GAVJ,aACE,CAKE,uBAsBA,uBAIA,kB3D/IR,sD2DiKA,uCACE,qCAGA,uCAGF,0CAIE,yCACE,C3D7KJ,qB2D6KI,oBAEA,CAvBN,4BAGE,eACE,iBAKF,iBArPN,iCA2QI,mBACA,yBAEA,sCAME,wCAIF,0CAKE,wCAGF,0C3D5ME,0C2DoNA,CArBF,mBAqBE,2BAEA,sCAKF,wCC/SJ,0CAEE,wCAEA,yCAGA,2CAEA,CDTF,mBCSE,qBACE,eAGF,qC5DocA,8CACA,iCACA,mCACA,uCAEA,yBACA,8DACA,gB4DxcE,yBAEA,oC5DwcF,6EAGE,cACA,qC4DpcF,gBALA,yBACE,0BAIF,qD5D0OA,wBAEA,CACA,wBAnLI,CAkLJ,kBACA,CAnLI,uB4D1DJ,0C5DgPE,wBAtLE,mB4D1DJ,mC5DoPE,yB4D9OF,gBACE,iDAIF,uCAIA,sC5D0OA,kCA/LI,a4D3CJ,C5D2OA,gCAEA,kBACA,gB4D3OE,iBACA,CAJF,mBAIE,wBAJF,yB5D2CI,kC4D3CJ,2B5DqPE,oB4D9OF,qDAEE,gBACA,8CAGA,0CAEE,uCAGA,8BACE,uCACA,uCACA,kBAMA,2DACE,4CAMR,kBACE,0BAEA,+EAIA,+CACE,mIASF,0BAIA,yBACE,qCAEA,eAEA,qEACE,wBAEA,uCACA,kBAEA,8DACE,0BAGF,mIAKA,qDACE,qCAEA,iDAKE,6BAEA,CAHF,mCAGE,sDAHF,mBAJA,YACE,sBAGF,uBASF,CANI,kCAMJ,iFAEE,OAON,uCAEA,CAHF,sCACE,CAPM,OASN,2BAEA,eACA,iBACA,0BAIF,sCACE,yFAKA,wBACA,6BAGA,gCAIA,iBAJA,sBAIA,CACE,cADF,gBACE,UAIJ,gCACE,eACA,kBAEA,qCAEA,yCACA,gBACA,QAEA,wDAEE,OACA,kD5D6DJ,6BACA,C4D5DI,6BAIJ,mC5DuDA,kBACA,eACA,kBACA,wB4DvDE,yBAEA,+B5D9GE,mB4DyGJ,0D5DzGI,oB4DyGJ,qCA2BE,gFAIA,CAvBF,oIAQE,CAIA,gCAEA,yCAGA,4BAEA,CAXA,uCAIA,C5D8CA,iB4D1DF,CARA,c5DkEE,C4DnCA,eACE,CAIA,yCAMF,mBACA,kBACA,mBAHA,SAGA,0CAKA,UACA,CAFA,iBACA,CAGA,SAFA,uBAEA,UACA,8CAME,gBACA,CAJA,WACA,CAEA,kBAEA,WADA,QACA,CAJA,sBAIA,CACA,+BAEA,kBAGE,CALF,YACA,mBACA,CAGE,kFAKE,2IAQA,+CAIA,uEACE,uBAIA,qGAKM,4FAkBd,oBAEA,sBACA,CArBc,4DAYhB,CAIE,6BAEA,CANF,4BAEE,4BAOA,2FAME,oBAEA,yCACA,KACA,mBAIJ,0C3DzSM,G2D2SJ,qBACA,0BACA,KACA,mBACA,uBAEA,6CACE,kBAIJ,qG5DxDA,iBACA,kBACA,WACA,iBACA,0CAnMI,oB4DuPJ,0D5DjDE,W4DiDF,W5DjDE,C4DiDF,Q5DjDE,0BAtME,mB4DuPJ,6E5D7CE,Q4DmDA,gHAGE,0Q5DjEJ,mB4DiEI,kC5DjEJ,uBACA,CAEA,6BACA,CAHA,iBACA,kB4DoEM,mC5DrQF,S4DgQA,sM5DhQA,WAsMF,kBAtME,C4DgQA,yE5DhQA,iB4DgQA,6PAQA,WARA,0E5DtDF,iB4D8DE,2EACE,2BAEA,0M5D7BN,W4D6BM,gCAGE,kBAMR,iC5DvCA,CACA,SACA,CAFA,wBAEA,yBAGA,iDACA,8DACA,cACA,sCACA,0CAtPI,sB4DoRJ,mD5DxBE,mFAGA,sBACA,CAFA,8BACA,CADA,gCAEA,mEAhQE,uB4DoRJ,oC5DbE,2BACA,CADA,gBACA,mDACA,uDACA,0BACA,kD4DaF,aACE,sD5DzGF,8CACA,aACA,mBACA,a4D4GE,C3D9VI,gED+DF,oB4D4RJ,qB5DtGE,aAtLE,C4D4RJ,0B5DlGE,wEAKF,oG4DsGI,kBACA,W5DtSA,iG4DkSF,oF5D6QF,UArCA,oFAGA,eACA,qYACA,8BAGA,uCAEA,CAJA,sCAIA,6BACA,gBACA,wEAEA,kBACE,oCACA,kBACA,gCA3hBE,wB4D0SJ,4B5DsPE,kBACA,sCAjiBE,kB4D0SJ,C5D1SI,U4D0SJ,qC5D4PE,S4DxPF,kBACE,uCAEA,CACA,kBAGF,CAJE,SAIF,wCAOE,UACA,CAPA,uCAGA,mBACA,iCACA,CAEA,eACA,oBAEA,yBACE,uCAEA,6CAEA,uCAKF,+CAOA,a5D8NF,C4D9NE,iB5D8NF,iBArCA,iE4D9LE,kBACA,CAFA,YACA,CAKA,iBAJA,sB5DiMF,CAJA,0BAIA,gYACA,cACA,mBACA,iBACA,CAFA,UACA,WACA,kCAEA,YACA,eACA,CAFA,SACA,CACA,sD4DlMI,uC5DoMJ,8FCjmBgB,0BDqmBd,0F4D3MA,gB5D+MA,oEAhiBE,U4DiVF,uE5DqNA,0B4D9MF,sEAMA,sKAWI,sCAIJ,qBACE,CAGF,yCACE,iBACA,CACA,kBADA,qBACA,iCAEA,oB5D1XE,W4D8XF,6BACE,iDAKF,kEAEE,UACA,gBACA,iBAIA,mCAKF,4D5D7YE,kB4DpFN,sFAqfI,YACE,CATF,gCACE,CAEA,kDACE,yBAKF,yBAEA,qFAIE,yCACE,qFAIA,+GAKA,4TAqBJ,eAVM,kEACE,iBACA,yBAQR,sU5DmCJ,sVAOA,cACA,qGAEA,wP4D7CI,6D5DvcA,uC4DucA,4E5DvcA,0C4DmdA,yCAGF,C5DuCA,4BAEA,+BA/fE,iB4DsdF,yBAKE,+GAMF,wCAEE,0CAIF,0CAGE,CAfA,mBAeA,wDAMA,wCAEA,0CAKE,wCAGA,yCAKF,2CAIA,CApBF,mBAoBE,oCACE,eAIJ,4BAME,UACA,CANA,uCAEA,mBACA,gBACA,iBACA,CACA,kBACA,yBACA,2BAGF,iBACE,2BAEA,2BAEA,uDASM,cARN,gCAEA,kBAMM,iDACE,CADF,wBACE,yBAOF,6EAIA,+EAEE,4BACA,yBACA,gBAEA,+XAoBE,CApBF,cAJA,eAwBE,oBAGF,6EACE,gCAGE,wIAIA,6IACE,mBAUhB,2BACE,2BACA,kBACA,2BACA,aAEA,0CACE,gCAQA,kBALF,mCACE,0BAIA,2DACE,C5D9mBJ,kB4D8mBI,UAON,qCACE,SAEA,yDAEE,CAGE,mBAHF,SAGE,yDAEE,0BACA,iGAIA,qDACE,gBAIF,+XAcI,CAdJ,cALA,eAmBI,oBACA,6BASZ,+BACE,iBACA,gCAEA,kBAGF,wDAIA,WACE,yEAKA,4BAQM,8EAIA,kEACE,mBACA,oCACA,2BACA,kEAIA,iDACE,8BAIA,uGAIA,4I5D/sBV,O4D4tBA,0CAKE,aAIJ,CARI,YAGF,iDACE,CAIJ,wCAIA,uDAGE,6DAKF,kCACE,CAGF,gBAHE,OAGF,oCAEE,0CACA,OAGF,0BACE,mBACA,kBACA,CAIA,oEAGF,eACE,0CAEA,kBAGF,0BACE,oBAMI,6HAGE,gFAKF,wFAGE,6GAEE,oBAEA,mFAWR,oBACA,qFAKM,gGAIA,kBACE,mBAIJ,mEAGE,wBACA,yCAEA,+XAuBN,CAvBM,WAJA,cACA,CA0BN,oBACA,6BACA,eACA,iCAEA,gC5D71BA,kB4Dk2BF,6DAEE,4JCx7BN,wBACE,qBACA,WACA,kBACA,+CAGE,kEAIA,kBAEA,mB7DmLF,kEAEA,kEAKA,4CACA,+CACA,oCACA,wHAKE,qBAEA,6BACA,oBACA,YADA,gBACA,yCACA,mDAhIE,O6DvEF,iD7D4MA,qBAEA,4CACA,oBACA,2CACA,sD6D7MA,oC7DgTF,kBACA,6BAEA,wBAEA,iCACA,uDACA,+BACA,qDArPI,cAsPJ,kC6DtTI,a7DgEA,2C6DnEF,C7D6TA,gCAEA,+BACA,0FAEA,gFA/PE,mFAuQF,oBACA,4BADA,qBACA,6BACA,CAEA,+I6DtUA,6EAzBJ,oBA8BI,wEAIA,YAEA,mFAMA,0GCxCF,gJAMA,UACA,uJAaM,oDAKE,cAJA,aAIA,8FAQR,yB9D+cA,6CAEA,6B8D9cE,kEAGA,e9D8cF,0BACE,yBAEA,mDACE,kBAvaA,kG8DjDJ,C9DiDI,a8DjDJ,+DAWE,wFAEA,6BAEA,8EAOA,kBACE,WACA,mFAQF,uGAOA,kBACA,WACA,sF9DyNF,iBACA,CAHA,kBGhRY,CHiRZ,kBAEA,oCACA,kBACA,gB8DpNE,gBACA,CADA,OACA,8BAEA,kB9DHE,0B8DHJ,sC9D0NE,2BAvNE,gB8DHJ,C9D2NE,iB8D3NF,uJ9DuOE,oB8D/NA,4BARF,oB9DuOE,C8D/NA,iIAKE,iHAGE,kI9DgcN,qCAEA,mC8DpbE,kBACA,oCAEA,kBACA,C9D/BE,oE8DwBJ,e9D2bE,0CAndE,kB8DwBJ,gC9D+bE,iB8DpbA,6BACE,aACA,2CACA,oGAGA,6CAEA,oCACA,mCAEA,6CAEA,gBAKA,+FACE,oFAEA,gBACA,oFAMI,iBAIJ,0FAUJ,qCACA,cACA,sCAEA,4BACE,mBAEA,+CAIJ,CACE,mEAIF,Y9DkGA,uEAGA,oBACA,uD8DtGA,oB9DyGE,CAtME,kB8D6FJ,e9DyGE,+C8DzGF,4D9DkGA,gBACA,CADA,6BACA,kBACA,iKAjMI,wB8DoGJ,oCAOA,mCACE,CAGF,yC9DgFA,oCACA,CACA,0CAEA,mBAnMI,2B8D+GJ,4B9DuFE,kBAtME,+C8D+GJ,6BAGE,+BAKF,wC9DyBA,kCAGA,aACA,CAHA,YACA,8BACA,CACA,e8D1BE,6B9D1HE,kB8DuHJ,a9DgCE,mBAvJE,2B8DuHJ,mCAaE,uCACE,wCAIJ,wCAEE,wCAIF,C9DYE,iC8D/BA,kBACE,gBAIJ,qBACE,yCAaF,2D9DEA,uCAGA,qC8DAE,wCALF,0C9DCA,mC8DDA,2B9D/II,kC8D+IJ,sC9DYE,wC8DFA,uCACA,yCAIA,CAfF,iBAeE,qCAQM,uCACE,qCAIF,iFAGE,yCAGF,CAjBJ,4BAEA,eAII,kCAWA,6CACE,mCAQR,wCACE,0CAEA,wCAKI,oFAKA,CAnBE,mBAmBF,8DAEE,wCAIF,kFAEE,yCAQV,2CAEE,CAlBM,mBAkBN,0BAGF,gBAEE,aACA,CAFA,iCAEA,0BAEA,gBAMF,WALE,kBACA,gCAIF,uBACE,6BACA,WACA,0BACA,gBAEA,iCACA,6BAGA,WAEA,mBACE,gCAIJ,2B7DvUe,C6D4Ub,kCACE,uBAIF,4CAfA,kBACE,CAMF,YACA,sBAEA,CAKA,iBASA,CATA,0BAQA,UACA,8EAKA,eACE,wFArWN,SA+WI,4BAMQ,2BAIA,CALF,+EACE,CAIA,+EAQR,yBACE,oCAGA,kBACE,eACA,yBAGF,2BACE,cAIF,sDAKA,uCACE,uBAKF,4GAQA,WAGF,oCAIA,qBACE,oCAOM,wDACE,YACA,SAGF,yEACE,YACA,0BAGF,sDACE,iBASF,qCAME,0BAIF,CAVA,iCACE,mBACA,gBAGF,uCAKA,mCALA,wBACE,CAIF,0CAQR,0BACE,mBACA,qBAEA,2BACE,0BAKF,4CAGA,yCACE,2BAKJ,0BACE,kBAGF,+FAMA,sCACE,0GAUA,2BAEA,wBACE,cAJJ,WACE,CAGE,WAIJ,yBACE,QACA,WAEA,2B9DtbA,uC8DkcF,CAZE,6BAEE,mBAIJ,gBACE,iB9D7bA,C8DkcF,iCAKM,0EACE,0BAIA,wEACE,mBAOV,iCAKE,8HAYF,iCAMA,yCAIA,4BACE,CAXF,wCAbA,iBACE,CADF,eAwBE,eAGF,oHAMI,gFC5kBN,mBAEA,uCAGE,qCACA,sBAEA,eAEA,mCAEA,yB/DoaF,4CAEA,CACA,kB+D/ZE,C/D8ZF,mB+D9ZE,qC/D+DE,kB+DlEJ,C/DqaE,gCAnWE,gCAuWF,kB+DnaF,kDAWA,cATE,gCACA,CACA,iBAEA,iCACE,CAIJ,kB/D6ZA,C+DjaI,wB/DiaJ,wDAEA,eACA,0BACA,+B+D7ZE,4C/D6CE,a+DjDJ,CAME,gCANF,mD/DqaE,C+DraF,gB/DqaE,wB+DraF,yB/DyaE,4B+DhaF,cACE,2BAGF,4BAEE,cACA,4BAKF,8CAEE,gCAKF,kCACE,iBACA,yBAEA,yBAEE,8BACA,cAEA,2BAIA,4CAEE,kZACA,4BAEA,4CAEA,yCAEA,kBAGF,+CAEE,kBAGF,4EAgBE,cATJ,uCAGE,mBAEE,CAGA,cACA,CAJA,eACA,iBACA,oBAEA,yBACA,yDASJ,iB/DnCE,2B+DuCF,yDAKE,cACG,CAAD,yB/D7CF,a+DpFN,C/DoFM,a+DpFN,mCA0II,uCACE,oBAEA,CAGF,kCACE,iBAGF,6EAMI,6FAEE,6CACA,yEAIA,8GAEE,aAIJ,kHAIA,UACE,CADF,WACE,uEAKA,oBACA,2BAON,wEAEE,YAEA,qEAEE,mCACA,CAQE,2DAJF,6DACE,aACA,qDACA,WAUF,CATE,gCAGA,WAEE,QAIJ,uFAIA,2EAGE,YACA,4CAEA,sBACA,yBAGA,wBAKN,2BACE,gB/DjJA,iB+DpFN,kBA0OI,UACA,gCAEA,YACE,oCAGF,mCAEE,iBACA,sBAGF,8EAII,gBAKF,wFAGE,iCACE,sBACA,oCACA,UAIA,kEACE,qCACA,iBACA,qCACA,aAEA,6JAMF,kIAKA,sBACE,wEAIA,UACA,WACA,yEAQJ,sFAGE,WACA,mFAKE,UACA,YASF,wEAEE,gDACA,gBACA,gCAEA,gJAEE,gCAIJ,8EAIE,iCACA,+BAEA,cAEA,4DAOF,WACE,WACA,iCAEA,WAMR,uCCnWJ,oBACE,4BAEA,WhEiQA,kDACA,WACA,uCgEhQE,eACA,2BhE6EE,iBgEjFJ,uBhEuQE,8EgEhQF,gBhEyQA,wFAIA,oCgEzQE,wChEsEE,mBgE1EJ,wBhE0EI,sBgE1EJ,gChEoRE,kBgE5QA,0FChBF,cACA,yBACA,aACA,sGAEA,qCAEA,eACE,iBACA,4BAEA,8BACA,kBAGF,gBACE,mBACA,mBACA,oCAIF,SACE,uBACA,yBACA,WACA,W7DlBa,eJ2Of,uBAEA,CiEvNA,4BjEoNA,kCG/NsB,CHgOtB,gBAEA,yBACA,eiErNE,2CACA,+BACA,2BAEA,uBACA,uBjE4CE,4BiEpDJ,yCjEoDI,yDiEhCA,uBjEgCA,eA2KF,iCiElNA,mBACE,kBjEsCA,iCiEjCF,CjEiCE,aiEpFN,CAmDI,wBAnDJ,yBAyDI,yCAGE,mBACA,2BAGF,yCjEoBE,iBiEpFN,qBAsEI,eAEA,uBACE,kBACA,UAGF,CAHE,4BAGF,8BACE,cACA,gBjEKA,CiENA,iBACA,CjEKA,kDiEDA,oBAEE,4BCrFR,WACE,yBACA,UACA,8BACA,0CAGA,YACE,+XA0BA,alEkDE,UkEpFN,CAkCI,gBACE,iBACA,4BlEgDA,mBkEpFN,uBA0CI,4CAIA,kEAGE,mBCjDN,CAEE,mBACA,kDAEA,UAGA,qBACA,4BACA,mBACA,6BAEA,4BACA,qBACA,oBACA,uCAIE,2EACE,eAIJ,0BACE,uFAGA,8CACA,sBACA,4CAEA,qBACA,WACA,qEAWA,oBACA,CANA,YACA,2CAIA,CAEA,gBADA,YACA,gJAMA,+EAGE,SACA,gEAMF,sBACE,WAKF,CALE,oFAKF,uDAEE,uFAMA,8IAQA,oBnEFA,CmECA,YACA,4CnEFA,emEOF,iKAQE,iFAMJ,SACE,kECzGF,wBACA,CAUE,WAVF,YACA,gBAEA,2BACA,kBAME,mGACE,aAIJ,kBACE,gBACA,oBACA,qCAEA,uBAEA,kBACA,UACA,UAKE,sJAMJ,wBACE,oDAIA,wBACA,CAFA,sBACA,CACA,uBACA,uEpEwcF,oBACA,CoEncA,YpEgcA,2CAEA,CACA,sBoEhcE,oJpEkcF,aACE,oEAIE,mBAxaA,6FoElCJ,2BAYE,mBAEE,4EAEA,iEAEA,WACA,yBAEA,UACA,wBACA,8DAEA,SAGF,+EAOE,oBACA,CAJA,YACA,2CAEA,CACA,sBpEFA,gJoEiBF,aACA,gEAKE,WAGF,aAGE,WpEiYJ,yCACA,cACA,kBACA,WoEpYI,oEAIA,qBACA,4BAEA,CAFA,UAEA,oBpE+XJ,eACE,4CAEA,oBAOA,aA7aE,CAuaA,uCAvaA,mCoE0BF,iBpEmZA,CA7aE,8CoE0BF,mBpEuZA,iBAjbE,2BoEsCA,mBACE,iBACA,oBAOA,aACA,CARA,gCACA,kBAGF,iBACE,iBAGA,qBCnIN,CDgIM,wBChIN,yBACE,kBACA,cACA,2BAGF,kBrE6OA,kDAEA,eACA,iBACA,gCqE7OE,oCACA,uBrEwEE,qGA2KF,mBqEhPA,0BAEE,kBACA,aACA,sBACA,CAEA,sCACE,sBACA,gCACA,iCrEwSN,uBAGA,cAHA,YACA,2BAEA,CAEA,cAFA,gBAEA,wBAIA,wCqEzSE,gCrEmDE,CAmPJ,uCACA,mCACA,kBArPI,gCqEtDJ,iBrEgTE,kBAEA,CAPF,mBACA,CAME,mCACA,yCACA,kDACA,2CACA,wCAhQE,0BAqQF,sBAEA,uDACA,uCACA,0CACA,6BACA,+DqEzTA,gBACA,qBAEA,0CAKF,iCrEmRA,uBAGA,aAEA,CAJA,iCAIA,iDACA,sBACA,+CAEA,mBADA,sCACA,mBqEzRE,gBACA,+BrEkCE,eqEtCJ,arEgSE,2CAEA,sBACA,oCACA,qDACA,iBACA,mBADA,6BACA,+BAhQE,eqEtCJ,arE2SE,gBAEA,2CACA,mCACA,6CACA,wDACA,mCqE1SF,mBrE4QA,iBG1SW,8BH4SX,eACA,aAEA,8CACA,kCACA,sCACA,wCACA,+DqEhRE,wBrE0BE,iBqE/BJ,qBrEyRE,iFAGA,2BACA,+GAEA,CAKA,kCAEA,CqEtSF,gCrEoSE,CALA,gCAhQE,CAuQF,YACA,sBACA,SADA,0BACA,wBACA,2CACA,kDqElSF,CACE,4BAGF,CAJA,iBACE,CAIA,gBACA,uBACA,yBAEA,wCAEA,oDAEE,2CAKF,eACA,qBAKI,qEACE,wFrE6OR,CACA,4BAEA,sEAEA,iDACA,wFqE1OE,CACA,4BrEZE,gBqEQJ,uBrEkPE,oDAEA,2BACA,gHAGA,+CAhQE,qDAqQF,+BAEA,0BACA,0CACA,iCACA,kDACA,qHAkHF,aACA,gCACA,6BACA,mBAGA,cAHA,eAEA,4BACA,2JAKE,aACA,uBACA,wBA7JF,gCACA,cACA,6BAGA,8CACA,oCACA,mBAEA,+BAtPI,yBqEoBF,WrEsOA,cANF,4BACA,kCACA,kBAME,6CACA,+CACA,oDACA,iCACA,2CAhQE,yCAqQF,kBAEA,wCAGA,6BACA,CADA,kBACA,CADA,oCACA,uBAkHF,qCACA,0DAnHE,iCAkHF,2CAOA,CANA,yBACA,4BACA,aACA,CACA,WACA,0BACA,yBAIE,2CACA,WChdY,CDidZ,yBqE9WA,2BrEiNF,mBACA,aACA,mBACA,aAEA,8BAEA,+BACA,yBACA,yBAJA,4BACA,kBACA,kCAEA,eqEvNI,kBACA,yBrEhCA,YqE4BF,qBrE8NA,yBAEA,4CACA,+DACA,kBACA,8DACA,kBAhQE,wCAqQF,wBAEA,+CACA,sCACA,0BACA,aACA,YAFA,uBACA,YACA,mDqEzOE,mBACE,CAEA,kCrE0VN,YACA,CAHA,0DACA,iBAEA,6BAEA,CAJA,gBACA,CAGA,eACC,CAJD,oBAIA,8FAGA,gEAEE,6BACA,eACA,WqE/VA,yBrEqMF,sBAEA,CAJA,8BAEA,CAHA,gCAKA,eAEA,uCACA,qCACA,uCqExMI,0CrE9CA,yCqE2CF,CrE+MA,cARF,4BACA,+BACA,kBAME,eAEA,uCACA,wCACA,0CACA,wCACA,0CAhQE,0CAuQF,CAVA,mBAUA,yCACA,wCACA,0CACA,wCACA,yCqEzNE,2CAGE,CrEmNJ,mBqEnNI,ErEwUN,iFACA,8BAKA,4EACA,uCAEA,0CAEE,yCAEA,CAXF,4BACA,eACA,gBACA,CAQE,eqE/UA,CrEuUF,gBACA,CqExUE,kBACE,yBrE5DA,cqEkEA,wCACA,0CAGA,wCAKF,0CAEE,0CAKF,CAjBA,mBAiBA,2BACE,cAIF,wCAEE,0CAOF,wCAEE,yCACA,2CAGE,CAlBF,mBAkBE,iBAQA,uCAKF,qCAME,uCAMF,0CrE/HA,yCqEuIA,CA7BF,4BAGE,gDACE,CAyBF,eAGF,gBACE,kBAGF,yBAEE,eAIA,wCAKE,0CAGA,wCAOF,0CACE,0CAMA,CA1BF,mBA0BG,CAAD,0BC9PR,eACE,wCACA,0CAGA,wCAEA,yCAEA,2CAEE,CAXJ,mBAWI,gBAEA,qDACE,CACA,eAGF,CAJE,eACA,CAGF,uFAGE,oCAEA,0BACA,yBAEA,mEAEE,wBAIJ,+DAGE,qCAGE,2HANJ,6EAMI,eACE,gBAMR,iDtEgHA,wCAGA,0CAEA,wCAEA,0CACA,0CACA,CATA,mBASA,2BACA,wBsEtHE,wCAEA,0CtEiCE,wCsEvCJ,yCtEgIE,2CACA,CsE9HA,mBtE8HA,4BACA,6DACA,+BA5FE,4BsEvCJ,yBtEwIE,6BAEA,yEACA,sBACA,iEACA,6BsEnIA,gCACE,mBAQJ,uCACE,qCAEA,uCAGA,0CAIA,0CAfA,2CACE,gBAIJ,iBAUE,yBACE,kBAEA,6PAEA,kBAMJ,wCACE,0CAEA,wCAEA,yCAIA,2CACA,CAfE,mBAeF,oBAEA,6DAGE,+BAEA,4BAEA,yBACA,cACA,eAIJ,yEAGE,uEAGA,6BACE,cACA,kBACA,CACA,mBAOA,uCAGF,qCAIE,uCAGF,0CAGE,yCAIF,CArBA,2CACE,gBAEA,iBACA,CAiBF,iCACE,yBAGA,mBAIJ,wCtEyTA,kFACA,0CAEA,0CAEA,CsE9TA,mBtE8TA,2BACA,2DAGA,2HAEE,2CAEA,CAPF,mBsE3TA,yCtEqTA,sFACA,+BACA,4BACA,yBACA,6BACA,yEACA,sBACA,6DAEA,6DAEE,oBsE1TF,uCAIA,qCAIA,uCACE,0CAEA,yCAIF,0BAEE,CtEySA,4BAEA,gBsE5TF,iCAiBE,kBACA,kBACA,yBAGA,mBACE,wCAGF,0CAKF,kFAIE,2CAbA,mBAaA,EACE,yBACA,mBAEA,kFAOJ,yCACE,wCAGA,2CtE6PF,CsExQI,mBtEwQJ,kEACA,6DAEA,+BAEA,4BACA,+HAGA,mFAEE,6BAEA,esErQF,6CACE,eAGF,2CAEE,4DAKI,kEACE,CAKF,yGAIA,wFAIA,6GAIA,kEACE,yBAMJ,qDAGE,oBACA,gBACA,eAEA,mBtEpLF,2BsE0LF,uCACE,etEvLA,2BsEpFN,uCAgRI,eACA,eACA,mBAEA,sCACE,gBAGF,kCACE,4BAGF,2BACE,yBAGF,qDACE,kBACA,4BAEA,+CACE,wCAEA,wBACE,CACA,kCAOA,CARA,gBACA,CAOA,kLAQJ,kFAIA,oBACE,mBAEA,kBAMF,2DACE,yDAIA,kBAIJ,mIAKE,6BACE,iPAaJ,mCACE,yCAGF,wCAII,0CAEE,CAvBF,iCAMA,wDACE,gBACA,CAeA,gBACA,kBAMF,sDAtBE,wBAKN,CAiBI,kCAGE,8EAIA,+EACE,yCAKF,CAVA,gBAUA,kEAMA,kFACE,wCAKF,4CAVE,oBAUF,gDAIA,mFACE,+CtE5TN,4BsEpFN,sBA0ZI,iBACA,iBACA,aAEA,6BACE,CADF,SACE,sDAIA,qBAGF,oOAqBE,CArBF,WAqBE,qCAEE,WACA,sCAMF,mBAJE,uBAIF,cACE,gBAEA,SAHF,sBACE,CAGA,SACA,iBAGF,wDAEE,kBACA,CADA,UAEA,2CAKF,qBACE,kBAIJ,6CACE,mBACA,CADA,SAEA,qCAKE,sFAEE,sDAKN,YACE,YAGF,gEAIA,kCACE,wDAGE,YACE,iBACA,YACA,gDAMF,8CACE,+CAIA,sFAIE,oFAIF,+BACE,4BAGA,yBAIF,gHAIE,sBAEA,qGAIA,6BAEA,wBAEA,kBAGF,8HtEhdJ,+BsE6dA,6BACE,wBAIJ,wCAOE,eACE,CARJ,wEAOE,sBACE,+FAOF,qDAEE,kBAIJ,yCACE,YAGE,gGAKE,kGAIA,uEACE,6CAEA,uBAGF,+GAIA,iFAOF,6GAGE,kBtExhBJ,gBsEkiBI,iFACE,kDCtnBV,6DAGA,+BAEA,4BACA,yBAEA,cACE,eACA,yEAKA,sBACA,+GvEieF,6BAEA,cACA,kBuE1dI,sCvE4dJ,kDACE,eAEA,iCACE,kFAMF,aA7aE,qFuEnDF,aACA,gFAOF,4FAOE,WACA,oFvEoMF,wDAGA,YuEjME,yBACA,kBACA,mBACA,mCvE0BE,uCAuKF,2BAvKE,0BuEhCJ,8BASA,kBACE,oCACA,sCAIF,yDAIE,qDAEA,eAGF,6FAIE,oDAEA,oRAOA,0BACA,CACA,4CAEA,yDAEA,0CAEE,+CAGF,iEAMA,kBACA,+CAGA,gCAEA,CAFA,YAEA,wSAmBE,+EvE/CA,kBuEpFN,gBA2II,gDAEA,eAEE,sCAGA,kEAKA,WACA,qBACA,sFAKA,kBAGF,kFAOE,YACA,mFAQE,0GAEE,UvE/FJ,4FuEyGA,YAGF,0GAOE,SACA,qFASF,0BAIA,qCACE,8BAGF,kBAEE,gBACA,qBACA,sCAGF,kBACE,wCAIE,gHvEjJF,sBuEpFN,aAuOU,SvEnJJ,CuEpFN,UA8OI,2CAEA,kCAEE,kBvE9JA,6CuEmKF,YACE,qCAGF,sBACE,4DC3PJ,aACA,YACA,kBACA,2DAIE,oCACA,qEAEA,kBAGF,+CACE,+YAGA,WACA,aAGF,YACE,sFAME,kBACA,iFAIA,WACA,WAGF,yBACE,CANA,OACA,CAQF,0FAME,YACA,aACA,2BACA,kFAIA,sEAMA,qFAMA,aACA,YAGF,2CAEE,oFAGE,WAGF,0DACE,8CAOJ,kBAIF,+CAIE,gBACA,mBAGA,oBACE,4CAMF,wBAGE,6CxEuIJ,uBACA,gDAGA,wBwExIM,CxEuIN,SwEvIM,sCxE5BF,gBwEyBA,iLxEqYJ,MG3dW,mFqEiGL,YxE+XN,wBACE,8FAIE,uBAxaA,iFwEgCA,0EApHN,YA8HI,2BAEA,YACA,2BAGE,mFAEA,uBAIF,iBACE,2DACA,gCACA,gBACA,kBACA,kBACA,0BxE5DA,kBwEiEF,CACA,gBAEA,CACE,eADF,wBACE,iBAJF,iBAIE,iBACA,oCAIA,ubAKE,aACA,CAFF,aACE,CACA,kBACA,CACA,iBADA,eACA,YACA,uBCtKN,wCACA,0DAEA,uBAQE,uBACA,CARF,uCAEA,mBACA,gBACA,iBAEA,CAEE,kBACA,mBACA,kBACA,CALF,mBAKE,yBAEA,sBAEA,mBACE,0BAGF,sBAKF,kBACE,uBACA,uBAGA,mBACE,CADF,iBACE,4CAEA,kBzE+CA,WyE1CF,kBA1CJ,yBA0CI,+GAEA,yBAEA,uPAiBI,+BzEqBF,6ByEfF,qDACA,UACA,qCAEA,qEAKE,yBC7EJ,gDAIA,Y1E8RA,6BAGA,qBACA,CAJA,YACA,kBACA,UACA,CACA,oCACA,C0EhSE,oEAHF,0B1EuSE,gD0EvSF,Y1E2SE,iCA5NE,wBAgOF,4BAhOE,mBAoOF,iB0E5SF,+G1E0TA,kCACA,gCACA,4CACA,kBACA,eADA,aACA,8DAtPI,aA0PF,kBAEA,8CACA,uCACA,uCACA,qDACA,8BAhQE,4B0ExEJ,mB1E6UE,iBAEA,+GAEA,QACA,0CACA,0C0E9UA,2DAKF,kBACE,yCACA,gBACA,C1E2DE,kC0EtDA,4BAIA,yC1EkDA,U0E7CF,6BACE,WAGF,6C1EsJF,+BACA,8BAEA,4BAGA,mJAGA,mCAtHI,gDA2HF,6CAEA,iBACA,qDACA,0BACA,uDAhIE,2BAqIF,aAEA,6CACA,4EAEA,6DA1IE,iB0EpCA,eACE,8XC/BN,UACE,qBAEA,CAFA,kBAEA,0BAEA,uBACA,4BACA,WACA,CAGF,8BAEE,wCACA,oDAIF,WACE,gCAEA,kCAEA,eACA,CAGE,WAOA,4CAVF,UAEA,oBACE,iBAIF,yCAEE,W3EiCA,C2EhCA,sBACA,qGAaJ,CACE,S3EiBE,2C2EPF,6BAGA,uDAKE,kEAMA,S3EPA,0B2EpFN,iBAgGI,WACA,2BAII,qCAEE,sDACA,iBACA,6BACA,iCAKF,uCAIA,mBAIF,CAIA,iBACE,CALF,gCAIA,CACE,eACA,CAFF,mBAEE,yBAIF,gCACE,kBACA,2BAEA,gCAEA,iBACA,iCAGF,4BACE,kBACA,gBAIJ,iBACE,gBAGF,kDAIA,yBACE,wCAOE,iCACE,yBACA,+BACA,cAIJ,2BACE,+BAIF,iEACE,gB3E1FF,mB2EpFN,gBAoLI,iBAKE,oBAKE,CATJ,uDAIE,CAKE,8BACE,iBACA,4CAIJ,0CAKF,8BACE,oB3EVJ,4CACA,OACA,kBACA,gBACA,iBAGA,oBACA,CAHA,uDAGA,6aApHI,C4EpFN,mB5EyNI,WAEA,0CACA,oCACA,c4EnNA,aAGF,C5EgNE,2CACA,yC4EtNF,uCAEE,CAGF,kBACE,oCAIA,uCACA,mCACA,sBACA,CAEA,YAFA,0BAEA,gBACA,aACA,6BAIF,sCAEE,OACA,sBAEA,kBACA,gBACA,CAHA,SAGA,sCAIF,kBACE,gBACA,cAEA,uCAeE,cAdA,4CAEA,0CAIA,wDAQA,uDACE,uCACA,4BAGF,+FACE,cACA,oBADA,aACA,2BAIJ,cACE,cADF,uDACE,mBACA,gB5EYA,oB4EPF,iBACE,sBACA,eAKA,qCACE,kBACA,gB5EFF,2C4ESA,mBAEA,iBACA,qBAEA,gBAIA,mBAEE,kBAKN,CAPI,wBAOJ,yBAEI,4BAIJ,qBAEI,2BAIA,0BCzHN,2BACE,wBACA,2BACA,SACA,mBACA,SAIA,uCACA,4EAEA,0CAGE,yCAIF,CAdA,4BAEA,eACA,iCACA,CAUA,iBACE,yBACA,QAEA,wCAGE,0CAEE,wCAMJ,0CAEA,0CACA,CAfA,mBAeA,mCAEA,wCAGA,0CAKA,wCAEE,yCAGF,2CAMF,CArBE,mBAqBF,uBACE,eACA,iCAEA,sCAGA,iBACE,UACA,wBAIA,kCAIF,0DAKA,yBAUA,uCAEE,CAGF,uCACE,wCAIF,wCAKF,CAzBE,iCAEE,kBAGF,qCACE,gBACA,yBAkBJ,+CAEE,SAEA,uCAKF,qCACE,wCAEA,yCAEA,CAZA,cACA,qBzEvGY,CyEmHZ,+C7EpCE,S6EpFN,sCA+HI,wCACE,uCAOE,0C7EnDF,iB6EmDE,0BAEE,qBAKN,0CAEE,oBAGF,iCAEE,eAEA,sCACE,sBACA,eAGF,YAEE,kBAGF,CALA,mCAKA,mCACE,qBAIF,uBAEE,CALA,qBAGF,CAEE,yBAIJ,0BACE,kBAGF,mCACE,QACA,uBACA,C7E/FA,e6EpFN,CAmLM,iB7E/FA,C6EpFN,8BAwLI,OAGE,uCAGF,CANA,sCAGE,CAIA,2BAGE,gCACE,aAKN,CANI,gBACE,CAKN,iBACE,WAGA,qEAME,CANF,qBAKA,+BACE,qCAIF,eACE,WACA,+BAGF,iBACE,6BAGF,gBACE,eACA,CAFF,UAKA,wCACE,wBAIF,+CACE,gBAIF,0CACE,gBACA,EACA,wBAIJ,eACE,mBACA,2BAEA,UACE,kBAIF,CALA,kBAKA,mCAME,oBAKJ,CAVI,YACA,+CAIA,CAKJ,wCACE,mBAGF,oBACE,+BC5QN,oCACE,UACA,kCACA,aACA,4DAEA,aACA,kBACA,gCACA,gBACA,gBACA,WAEA,yDACE,0OAEA,CAFA,aAEA,iCAEA,iCAEA,mFAKE,eACA,2BAEA,+CACE,oBACA,aACA,4BAIJ,uDAEE,kCACA,CACA,gCACA,eACA,uBAIF,2B9EyMF,uC8ErMI,wCAEA,wCAEA,wC9E6BA,wB8ErCF,kC9EqMF,uDACA,CACA,gB8EvME,oC9EwMF,wBACA,C8EzME,iB9E4MA,yBAvKE,S8ErCF,uC9EgNA,qC8EpMA,wC9EsQF,yCAEA,C8EpRE,mC9EoRF,2BAGA,+CACA,wCACA,uCACA,yCACA,CAJA,iBAIA,uCAtPI,4B8EzBF,yD9EuRA,oBACA,uBALA,uCAEA,6BACA,YACA,CADA,oBACA,kBACA,oBACA,qBAhQE,oB8EzBF,uB9EyRA,6BAhQE,a8EzBF,4B9E8RA,8BAEA,sCACA,oCACA,2BACA,sCADA,YACA,WACA,4C8E3RM,uJAON,wFAEE,mCAEA,6BACA,wFAEA,oBAGE,6F9E8iBN,WAzEA,oCACA,YACA,YACA,2BACA,6YACA,UACA,mCACA,4BAEA,kBACA,qGAEA,mB8E1eQ,8B9E4eR,qLAEE,8BACA,2BAEA,uBAzfE,iB8ECE,gG9E4fJ,gBACA,WACA,sCA/fE,S8ECE,6GAWA,wF9EiON,gCACA,WACA,uCAGA,4DACA,oBACA,yCACA,8DACA,uBAoRA,CApRA,UAoRA,yCAEA,WADA,UACA,yCAEA,6XACA,kBACA,qCACA,yBAEA,oBACA,4BACA,8BACA,6C8EngBQ,gE9EnBJ,e8EYE,oG9E8OJ,cAEA,uDACA,wBACA,CADA,UACA,yCACA,8DACA,YAhQE,oD8EYE,wE9EyPJ,+BAEA,kBACA,0DACA,8CACA,8BACA,6CA6QF,0GACE,eACA,eACA,wCA3hBE,mB8EYE,qG9EohBJ,UACA,UACA,wCAliBE,U8EYE,qG9E0hBJ,Y8E9gBI,4L9EqNN,sDAEA,SACA,kDAGA,WADA,SACA,wCACA,wDACA,eACA,iDAuIA,0DACA,2BACA,gDACA,YCjcU,CDmcV,6BACA,oDACA,QADA,UACA,e8EvWQ,mD9E+NN,e8E5NM,mO9E0NN,CAEA,8BACA,wEAEA,sBADA,cACA,yBACA,+E8ExOI,iH9E6OJ,6BAEA,C8E/OI,kB9E+OJ,0EA+HF,mBA3HE,+BA2HF,CA9HE,uCACA,mBACA,iBA4HF,6CA5HE,gBACA,CA2HF,iCA3HE,mBA2HF,qRAIE,uC8EvWI,4EACE,0CAKN,0C9E6VF,2CAEE,iCAEA,C8EjWA,iBACE,yBACA,8DAIE,kF9E+LN,wCAEA,0CAEA,CACA,yCACA,C8ErMM,mB9EqMN,yFAEA,wCAuIA,kFAEA,yCACA,2CAGA,CA7IA,mBA6IA,0I8EpVM,oG9E2MJ,kBAEA,C8E7MI,a9E6MJ,iCACA,iGAEA,wG8EhNI,gc9EuVN,C8EvVM,0B9EuVN,gFAEE,C8EjVI,mB9EiVJ,gDAEA,kB8EnVI,wQAaE,4LAUN,oCAEE,mBAEA,oHAIA,kBACE,yFASF,uCAIA,4EACE,0CAIJ,sE9EzGE,yB8EgHF,+YASI,CATJ,cAPA,eA6BE,qBAXE,4CA3BF,gBA2BE,eACE,CAUJ,mBAtCA,iBA4BI,gBAEA,gCAIJ,0EAIA,YACE,yBAEA,uFAOF,wCACE,0CC9NN,wCAEA,CACA,yCAKA,0CAEE,CDmNE,mBCnNF,2BAGA,uFAOE,wCAEA,0CAEA,wCAMJ,yCAEE,2CAKF,C9ExBgB,mB8EwBhB,+F/EoHA,4BAEA,qBAEA,gCACA,gHAEA,kB+ExHE,kBACA,mB/EoCE,2BAuFF,uFAGA,kBACA,wMAWA,uC+EvIF,qC/EgbA,uCAGA,0CCjdM,C8EmCJ,wCAEA,2D/E+aA,+BArZE,4BAyZF,yB+EhbF,C9ExCM,W8E6CJ,CALF,U/EsaA,gBA3SE,4BACA,gC+EvHA,kBACA,C/EsHA,gBACA,C+EvHA,U/EgaF,wE+EnaE,CAGA,iBACA,CAJA,qBAIA,yBAEA,sMAOA,wCACA,0CAGA,wCAKF,0CAIA,0C/EwOA,C+EtPE,mB/EsPF,2BAEA,sMAMA,wCAtPI,0CA0PF,wCAEA,yCACA,2CACA,CARF,mBAQE,sa+EjPE,6BAKF,oIAGA,4FAGA,0BAEA,kCAEA,8GAcF,uCAEE,qCAEA,uCAIE,0CAEA,yCAKA,CACE,0DAQJ,+BAEA,4BAEA,yBAKA,YAlBE,cACE,CAiBJ,cACE,CA5CA,4BAKF,eACA,gBACA,iBAGF,CAsCA,qBAJI,wEAIJ,4J/E2iBA,kFACA,wCAEA,0CAEA,0CAEA,C+EljBA,mB/EkjBA,2BACA,6GAOA,+MAFA,mBAEA,ubAEE,iEAGF,k2BAEE,6EACA,mP+ElkBF,+M/EhFI,wB+EgFJ,kM/E+kBE,C+E/kBF,kB/E+kBE,4E+E5jBA,iBACA,UACA,qBAPA,Y/EiGF,eACA,C+ElGE,yB/EiGF,kBACA,qBACA,qBACA,CAlMI,YAmMJ,QADA,iBACA,WAnMI,U+E8FF,qD/E9FE,yB+E8FF,C/E4GA,Q+ElGF,CAGE,eAKE,CAlBF,iB/E4GA,C+ElGF,oDAGE,CAbA,wBAkBE,0BACE,a3E1LU,C2E6LZ,4CACE,iBAUF,mCAIA,yCAIA,wCAEE,0C/EzIF,c+E9EN,CAmMQ,iCAGA,mBAEA,qCACE,gBAIJ,CA7MN,kBA8NI,CAjBE,wBAiBF,yBAGE,uDAEE,uCAKF,wCAEA,yCAGA,CAZA,gBAYA,2BACE,gBAIA,uCAKF,2CAIA,wCAIA,2CAII,CAlBJ,oBAkBI,qBAMF,6BALE,4BACA,kBACA,iBAGF,mBAEE,CALA,gBAGF,CAEE,kBAGF,2CAEE,mBAGF,yCAEE,mBAGF,oCAYF,iCAVI,4B/E5MJ,kB+E9EN,CAiSI,gBAGE,mBAEE,CALJ,gBAGE,CAGE,0DACA,kBAIJ,yBAEE,kCAGF,2BAIA,oBACE,eAGF,8CACE,aACA,mBACA,8BAGF,mBAEE,iDAKA,qCAGE,sBACE,uBAWJ,uCAIA,qCAEE,iFAKF,C/ElRA,wC+EwRA,C/ExRA,4B+EmQF,eAEA,gBACE,iBACA,CAiBA,2CAEE,sBCxWR,wCAEE,0CAEA,wCAEA,0CAMA,0CAGE,CDyVI,mBCzVJ,2BACA,sBAIA,wCAGA,0CAIE,wCAKA,yCACA,2CAKA,CAnBJ,mBAmBI,+BACA,SAGF,uCAEE,0DAKA,+BAIJ,4BhF+fA,yBACA,gCACA,wEAEA,+VACA,yCACA,WACA,gCAEA,cACA,4CACA,qBACA,gDgFxgBE,oDAEA,eACA,mQhFshBA,6DgFlhBE,8BAEA,CAEA,2BAIJ,CANI,wBAEA,CAIJ,WACE,eAEA,uCAKF,kBACG,CAAD,eACA,CATA,WAEA,CAOA,iBACA,mBAIA,cACE,8CACA,CAIJ,soB7E3EW,0B6EkHP,q5BAqEE,6DAMF,2BACE,CAEA,2BAGF,CALE,wBAEA,CAGF,+BACE,CAhBA,uDAgBA,C/EtNS,wB+EyNP,kQAiBJ,cAEA,2BAEA,kQAmBA,kBACE,oCAIA,sBACE,2BAOF,2DACE,gCAGF,kBACE,gBAGF,0CACE,6DAMJ,cACE,EAGF,6DACE,0CACA,WACA,8BhFrNF,gCgF4NA,gBACE,8DAIF,aACE,gBACA,aACA,iDAGF,YACE,CAGA,uEAGA,8CAEE,WAKN,mBACE,YAIA,iCACE,yBAGF,qDhF9PA,4CgFuQF,wBAKE,oBAIA,CAJA,eAIA,oBACE,mBAGF,iCAEE,4ChFtRF,YgF4RF,CAIA,uCAEE,oBACA,iBAIA,0BACE,gChFxSF,qBgF4SE,YhF5SF,gCgFoTF,kCAEE,wCACA,aAGF,WACE,oCACA,aAEA,YACE,iDACA,0CAGF,+BAEE,2BAIJ,4BAIA,qDAEE,YACA,yDAME,YACA,gBACA,4CAIA,eAGF,eACE,sBACA,iBACA,mCACA,kBACA,YAGE,kDACE,WACA,mBAIA,oDACE,gCAKF,+CACE,uBAMR,gBACE,4CACA,qDAGE,2CAIA,iBAIA,wBACE,2BAIJ,oDAMA,2CAGA,yBAEA,gCACE,sCACA,CACA,eAIA,CACE,MADF,kCACE,UAKF,kBAKA,mBALA,YACE,mBACA,8BAGF,eACE,cAKN,kBACE,aACA,UACA,oBACA,aACA,YACA,mBAGF,eAEE,kBACA,CAHF,8BAEE,CACA,YACA,sBACA,YACA,gCAEC,CAAD,wBACA,YAGF,gCAEE,uBACA,yCAEA,cACE,wBACA,yCAIJ,+XAiBE,CAjBF,WANE,cACE,CAsBF,oBACE,4CACA,gBACA,kBAWF,gBAPE,4CAOF,CAXE,8EAYA,uCAGF,wBAMA,gCACE,kBACA,yBAGF,aACE,kBACA,kBAKN,mBACE,2BAGA,+BACE,qBAEA,sCAGE,CAHF,4DAGE,qDAKF,oCAIA,6BACE,0FAEA,CAFA,uBAEA,cAEA,cAFA,cAEA,yBACE,yFAYR,gCACE,gBAQA,CArBM,sBAYR,CASE,+BACE,0BhFnjBF,+BgFyjBE,UACE,uBC5oBR,sBACA,CAFA,+BAEA,+BAEA,kBACA,CADA,SACA,UACA,4BAGA,4BACE,UAGF,oBjFoeA,+BACA,WACA,cACA,6BiFpeE,mBACA,gBjFqeF,iBACE,kBAEA,iCACE,eACA,yBAxaA,eiFtEJ,gBjFmfE,UA7aE,uDAibF,aiFhfF,sDArBF,sBA2BI,sBACA,wBACA,UAEA,0BAIA,UACE,CAJA,YAGF,CACE,8BAOJ,uBACE,YCxCF,mDAJF,sBAEE,8CAOA,sBAIA,UACE,oBAEA,WADA,aACA,+QAEA,aACA,iCAEA,mDACA,yBACA,0BAIF,aACE,yCAIA,yBACA,kCACA,UAGF,wBlF8OA,aChQU,gDiFwBR,ClFwOF,sBAEA,CkF1OE,MACA,CACA,iBACA,QlFsCE,UAiMJ,kEkF7OE,ClF4CE,iBAsMF,CAtME,UAsMF,mBAtME,sBkF/CJ,6BlFyPE,CkF7OF,aACE,ClF4OA,SkF7OF,CAQE,uBAIF,CAXE,wEAOA,CAIF,UACE,iCACA,8RAEA,+BACA,4CAUF,sBACE,CATA,iEAIE,mBAKF,yBACA,uBACA,iDAGA,WACE,mBAGF,6BAEE,CAFF,yBAEE,yBACA,cAEA,qDACE,4BACA,CACA,4BAON,sBACE,uBAEA,6CAKF,kDAEE,YACA,0BAIF,yBlF+JA,CAGA,uDACA,mCkF/JE,qBlFpCE,uBkFgCJ,2BlFsKE,gBAtME,ekFgCJ,kBlF0KE,gBkFpKA,kBlFyJF,CkF/JA,gBlF+JA,CACA,uCACA,eACA,2BACA,QAnMI,sDkFsCF,mBlFgKA,uBkFhKA,elFoKA,mBkF/JF,+BACE,oBACA,yCACA,+BAGE,0CAEE,+BAIJ,+BAEE,uBAIJ,iBACE,CALE,cAKF,sBACA,yBACA,+BAEA,eAGE,8CAEE,YAGF,sCAEE,CAMF,kBACE,CAPA,YACA,sBAEA,uBAIA,yCAGF,sBAEE,oBACA,uDAGA,0EACA,uBAMJ,oBAGE,2BACE,iCAOF,4BACA,8CAKA,kDAKJ,sCAEI,aAIA,sBAIA,kBAIA,CARA,cAIA,cAIA,uBAGF,0BASE,aAIJ,CARI,sBAIA,CARA,8BAYJ,gCAEI,aAIA,gCAIA,uCAIA,uCAIA,qCAGF,WACE,aAIA,kBAJA,8BAIA,mBAIA,mBAIA,mBlFzLA,CkFqLA,iCAIA,ClFzLA,sBkF8LF,gBACE,gCAIF,+BACE,mCAGF,uBACE,0CAEA,6BAKF,sBACE,0CAEA,sBAGF,qBAIA,6BAII,CAME,eACA,CAoBA,gBADF,eACE,CApBA,yBAGF,kBACE,CAeF,sBA1BA,mCAEE,4BAGF,CAME,sGAeF,CArBA,iBACE,CAqBA,iBC3UR,4BAEA,kDAIA,uBAGA,CASE,SACE,mCAGA,CAbJ,+FASE,CAIE,kBAGF,iCAEE,YAGF,qCAEE,aACA,CAMF,qDACA,0VAGA,qBAHA,yHAIA,oCACA,kHAWF,4CAEE,eACA,8BACA,QAEA,yBnF8FF,6BACA,qBAIA,sBAEA,CALA,gCAEA,aACA,uBAEA,sBACA,oCACA,yCACA,iBAIE,uBAEA,CANF,4BAnFI,kCmFrBF,iBnF4GA,CAEA,kCACA,0BACA,yCACA,yDA5FE,cmFrBF,2BnFsHA,gBAEA,+CACA,mCACA,kBACA,CADA,mBACA,oDmFvHA,enFgOF,iCACA,mCAEA,sBACA,kDAnNI,2BmFjBF,QnFwOA,4BACA,+BAxNE,YmFjBF,6BnF6OA,wBA5NE,SmFjBF,mQnFoIF,4BACA,2BAEA,8BAFA,iDAEA,YAtHI,0BmFbF,anFuIA,aACA,CAEA,kBAFA,UAEA,CAFA,UAEA,8BACA,4BmF3IA,WnF4IA,kDACA,iBmF7IA,WnFkJA,CALA,gBAhIE,CAqIF,sBAEA,mBAvIE,wBmFbF,CnFoJA,iBACA,mCACA,wDACA,6BmFnJA,kBnFyJF,qCG/MsB,CHgNtB,qBACA,cACA,sBACA,kBApJI,wBmFTF,mQnFgLA,aAvKE,cmFAJ,mBACE,8BACA,CnFFE,iDmFAJ,CAEE,WACA,oBACA,eAEA,yBAQA,oBAEA,CARE,oBACA,aAIJ,2BAGE,gCAEA,YACA,iDAIA,gBAIE,uFAMJ,yBACE,oBACA,SACA,YACA,2DAGA,aACE,uBAKF,aACA,mCACA,CADA,iBACA,uBnFubF,cmFtbE,gCnFqbF,mDACA,gBACA,CADA,wBACA,yBACA,sBACA,oWACA,0BACA,6BACA,CAHA,gCAGA,+BAEA,yBACA,6BACA,2CACA,qCAEA,2CAEE,8BAFF,YAGE,YADA,SACA,0CACA,2CACA,cAzfE,amFoDJ,uBnFycE,WACA,8BACA,YA/fE,iCmFoDJ,anF+cE,iBmFrcF,+BnFgWA,cACA,kBACA,CmF9VE,kBACA,CnF6VF,+DmF7VE,CAEA,+CnF8VF,cACE,iBAEA,GAEE,aAxaA,CAsaF,sBACE,6BAvaA,sBmF8DJ,8BnF9DI,gDAibF,gCmFxWA,gCAGA,gCACE,gCAEA,KAKE,oDACE,kBACA,uBAEA,mEASN,2CAKF,2CAEE,2CAIA,4CAOE,uCAIA,2BACE,0BAGA,uCACE,YAKF,0DAKF,sBACE,oBAGF,uCAIA,2CAIA,iCACE,gBnFnJF,sCmF0JA,2BnF1JA,kCmF+JF,QAEA,YACE,iDACA,kBAGF,+BAIA,YACE,wBAEA,gDAGE,iBACA,iCAGF,6BAKA,6BACE,iBAKJ,cACE,4BAGF,CACE,WADF,UACE,aAEA,aACE,cAIJ,8BACE,YACA,uBAIA,4BACE,iBACA,YAMA,sCAIA,uCACE,aAGF,uBACE,iBACA,kBAIF,yCACE,+ZA0CF,aAIJ,sBACE,qFAMA,6BACA,iBACA,sBACA,WAGF,uCACE,mBAEA,iBACA,qCAcI,mCACE,yCAGA,kFAEE,CApBR,iCAEA,mCACE,qBAIJ,gBAOM,yBAMI,CACA,wBAEA,kBAMR,uCAKE,uCACE,iFAIA,CAVJ,gBAUI,2BAIF,kBACE,uCAGF,2CAOA,wCAIA,2CAEE,CAjBF,oBAiBE,sBAGF,iCACE,mBAGF,sCACE,CAKF,wCACE,yBAGF,oBACE,mBAGF,gDAIA,qCACE,2BAIF,mCACE,2BACA,oBAMJ,kBAKE,oBAWA,uCAIA,wCAIA,wCACE,wCAEA,CAtBF,iCAEE,kBACA,gBACA,qBAGF,gBACE,yBAcA,yBAEA,kBAEA,uCAKF,qCAKE,wCAIA,yCACE,CAfF,mCAeE,EACA,yBAKF,kBAIA,sCACE,wCAMN,uCnFxcA,yCmFgdA,CAnBI,iBAmBJ,oBAKE,uCAIA,mCACE,CACA,gBACA,oBACA,yBnF7dJ,kBmFoeF,iBACE,2BACA,kB/E/iBY,c+EqjBV,oBACE,0DAIF,iCACE,oBCnkBR,2HACE,gBAGF,yGACE,CADF,cACE,kBACA,eAKI,mqCAEE,kBAIJ,qUACE,kBACA,mBACA,2BAGF,+vBAOA,oNhFxBY,iBgF0BV,CACA,WAGF,kXAEE,8BAKF,iCAHE,sBAGF,0TAEE,yCACA,mCACA,eAGF,ofAUE,gBAMJ,uLAKI,4WAEE,eACA,+EAGF,0TAEE,gDACA,aACA,8CAGF,khBAgBE,WAhBF,YAWE,8BAKA,uoCAEE,iBpFrBN,6HoF6BF,iBACE,mBACA,2rCAgBE,mVAgBE,+7CAYF,mbAeE,2qCxFjDA,yCACE,gYAmCN,kBAXM,4BAWN,yBACE,mjBAgCF", "sources": ["webpack://votesaveamerica/./src/scss/style.scss", "webpack://votesaveamerica/./src/scss/base/_fonts.scss", "webpack://votesaveamerica/./src/scss/base/_reset.scss", "webpack://votesaveamerica/./src/scss/base/_form.scss", "webpack://votesaveamerica/./src/scss/base/mixins/_index.scss", "webpack://votesaveamerica/./src/scss/base/constants/_colors.scss", "webpack://votesaveamerica/./src/scss/base/_global.scss", "webpack://votesaveamerica/./src/scss/base/constants/_typographic.scss", "webpack://votesaveamerica/./src/scss/base/constants/_spacing.scss", "webpack://votesaveamerica/./src/scss/blocks/_actioncardlist.scss", "webpack://votesaveamerica/./src/scss/blocks/_breadcrumb.scss", "webpack://votesaveamerica/./src/scss/blocks/_button.scss", "webpack://votesaveamerica/./src/scss/blocks/_candidatecomparison.scss", "webpack://votesaveamerica/./src/scss/blocks/_cdformpart.scss", "webpack://votesaveamerica/./src/scss/blocks/_cdtargetgroups.scss", "webpack://votesaveamerica/./src/scss/blocks/_commentary.scss", "webpack://votesaveamerica/./src/scss/blocks/_detailitem.scss", "webpack://votesaveamerica/./src/scss/blocks/_detailitemwrap.scss", "webpack://votesaveamerica/./src/scss/blocks/_detaillist.scss", "webpack://votesaveamerica/./src/scss/blocks/_donategoaltracker.scss", "webpack://votesaveamerica/./src/scss/blocks/_donationcta.scss", "webpack://votesaveamerica/./src/scss/blocks/_donationsection.scss", "webpack://votesaveamerica/./src/scss/blocks/_emphasizedlink.scss", "webpack://votesaveamerica/./src/scss/blocks/_emphasizedlinklist.scss", "webpack://votesaveamerica/./src/scss/blocks/_faq-child.scss", "webpack://votesaveamerica/./src/scss/blocks/_faqs.scss", "webpack://votesaveamerica/./src/scss/blocks/_featuredcountdown.scss", "webpack://votesaveamerica/./src/scss/blocks/_featuredtext.scss", "webpack://votesaveamerica/./src/scss/blocks/_findyourteam.scss", "webpack://votesaveamerica/./src/scss/blocks/_formsection.scss", "webpack://votesaveamerica/./src/scss/blocks/_gallery.scss", "webpack://votesaveamerica/./src/scss/blocks/_generichero.scss", "webpack://votesaveamerica/./src/scss/blocks/_homepagecd.scss", "webpack://votesaveamerica/./src/scss/blocks/_homepageform.scss", "webpack://votesaveamerica/./src/scss/blocks/_image.scss", "webpack://votesaveamerica/./src/scss/blocks/_imagecaption.scss", "webpack://votesaveamerica/./src/scss/blocks/_homepagemidsection.scss", "webpack://votesaveamerica/./src/scss/base/constants/_theme.scss", "webpack://votesaveamerica/./src/scss/blocks/_mediatext.scss", "webpack://votesaveamerica/./src/scss/blocks/_mobilizeevent.scss", "webpack://votesaveamerica/./src/scss/blocks/_pagination.scss", "webpack://votesaveamerica/./src/scss/blocks/_priorityaction.scss", "webpack://votesaveamerica/./src/scss/blocks/_programhero.scss", "webpack://votesaveamerica/./src/scss/blocks/_programmap.scss", "webpack://votesaveamerica/./src/scss/blocks/_programtargets.scss", "webpack://votesaveamerica/./src/scss/blocks/_pullquote.scss", "webpack://votesaveamerica/./src/scss/blocks/_resource.scss", "webpack://votesaveamerica/./src/scss/blocks/_resourcelisting.scss", "webpack://votesaveamerica/./src/scss/blocks/_rotatingtext.scss", "webpack://votesaveamerica/./src/scss/blocks/_searchform.scss", "webpack://votesaveamerica/./src/scss/blocks/_searchresults.scss", "webpack://votesaveamerica/./src/scss/blocks/_sectionbackground.scss", "webpack://votesaveamerica/./src/scss/blocks/_sectiontitle.scss", "webpack://votesaveamerica/./src/scss/blocks/_schema-faq.scss", "webpack://votesaveamerica/./src/scss/blocks/_signupcta.scss", "webpack://votesaveamerica/./src/scss/blocks/_simplecta.scss", "webpack://votesaveamerica/./src/scss/blocks/_simplectalist.scss", "webpack://votesaveamerica/./src/scss/blocks/_simpleform.scss", "webpack://votesaveamerica/./src/scss/blocks/_stateselector.scss", "webpack://votesaveamerica/./src/scss/blocks/_statlist.scss", "webpack://votesaveamerica/./src/scss/blocks/_statlistsection.scss", "webpack://votesaveamerica/./src/scss/blocks/_table.scss", "webpack://votesaveamerica/./src/scss/blocks/_tableofcontents.scss", "webpack://votesaveamerica/./src/scss/blocks/_takeactionstepone.scss", "webpack://votesaveamerica/./src/scss/blocks/_takeactionsteptwo.scss", "webpack://votesaveamerica/./src/scss/blocks/_targetstatlist.scss", "webpack://votesaveamerica/./src/scss/blocks/_teamleaderboard.scss", "webpack://votesaveamerica/./src/scss/blocks/_testimonial.scss", "webpack://votesaveamerica/./src/scss/blocks/_teamtarget.scss", "webpack://votesaveamerica/./src/scss/blocks/_teamtargetlist.scss", "webpack://votesaveamerica/./src/scss/blocks/_testimoniallist.scss", "webpack://votesaveamerica/./src/scss/blocks/_tickertape.scss", "webpack://votesaveamerica/./src/scss/blocks/_tooltip.scss", "webpack://votesaveamerica/./src/scss/blocks/_usvote.scss", "webpack://votesaveamerica/./src/scss/blocks/_usvotestatevotinginformation.scss", "webpack://votesaveamerica/./src/scss/blocks/_verticalvideo.scss", "webpack://votesaveamerica/./src/scss/blocks/_video.scss", "webpack://votesaveamerica/./src/scss/blocks/_videolisting.scss", "webpack://votesaveamerica/./src/scss/blocks/_vote.scss", "webpack://votesaveamerica/./src/scss/blocks/_votehero.scss", "webpack://votesaveamerica/./src/scss/blocks/_winstat.scss", "webpack://votesaveamerica/./src/scss/blocks/_winstatlist.scss", "webpack://votesaveamerica/./src/scss/components/_cmplz.scss", "webpack://votesaveamerica/./src/scss/components/_footer.scss", "webpack://votesaveamerica/./src/scss/components/_header.scss", "webpack://votesaveamerica/./src/scss/components/_geoalert.scss", "webpack://votesaveamerica/./src/scss/components/_meerkat.scss", "webpack://votesaveamerica/./src/scss/components/_popup.scss", "webpack://votesaveamerica/./src/scss/templates/_default.scss"], "sourcesContent": ["/*!\nTheme Name: Vote Save America 2024 WordPress Theme\nTheme URI: http://www.votesaveamerica.com\nAuthor: BlueState for VSA\nAuthor URI: http://www.bluestate.co\nDescription: A theme for Vote Save America's 2024 work\nVersion: 1.2.81\n*/\n\n/*\nSASS entry point\n*/\n\n@import 'base/fonts';\n@import 'base/constants';\n@import 'base/functions';\n@import 'base/mixins';\n@import 'base/reset';\n@import 'base/form';\n@import 'base/global';\n\n// Blocks\n@import 'blocks/actioncardlist';\n@import 'blocks/breadcrumb';\n@import 'blocks/button';\n@import 'blocks/buttonlist';\n@import 'blocks/candidatecomparison';\n@import 'blocks/caption';\n@import 'blocks/cdformpart';\n@import 'blocks/cdtargetgroups';\n@import 'blocks/commentary';\n@import 'blocks/detailitem';\n@import 'blocks/detailitemwrap';\n@import 'blocks/detaillist';\n@import 'blocks/donategoaltracker';\n@import 'blocks/donationcta';\n@import 'blocks/donationsection';\n@import 'blocks/emphasizedlink';\n@import 'blocks/emphasizedlinklist';\n@import 'blocks/faq-child';\n@import 'blocks/faqs';\n@import 'blocks/featuredcountdown';\n@import 'blocks/featuredtext';\n@import 'blocks/findyourteam';\n@import 'blocks/formsection';\n@import 'blocks/gallery';\n@import 'blocks/generichero';\n@import 'blocks/homepagecd';\n@import 'blocks/homepageform';\n@import 'blocks/image';\n@import 'blocks/imagecaption';\n@import 'blocks/homepagemidsection';\n@import 'blocks/mediatext';\n@import 'blocks/mobilizeevent';\n@import 'blocks/pagination';\n@import 'blocks/priorityaction';\n@import 'blocks/programhero';\n@import 'blocks/programmap';\n@import 'blocks/programtargets';\n@import 'blocks/pullquote';\n@import 'blocks/resource';\n@import 'blocks/resourcelisting';\n@import 'blocks/rotatingtext';\n@import 'blocks/searchform';\n@import 'blocks/searchresults';\n@import 'blocks/sectionbackground';\n@import 'blocks/sectiontitle';\n@import 'blocks/schema-faq';\n@import 'blocks/signupcta';\n@import 'blocks/simplecta';\n@import 'blocks/simplectalist';\n@import 'blocks/simpleform';\n@import 'blocks/stateselector';\n@import 'blocks/statlist';\n@import 'blocks/statlistsection';\n@import 'blocks/table';\n@import 'blocks/tableofcontents';\n@import 'blocks/takeactionstepone';\n@import 'blocks/takeactionsteptwo';\n@import 'blocks/targetstatlist';\n@import 'blocks/teamleaderboard';\n@import 'blocks/testimonial';\n@import 'blocks/teamtarget';\n@import 'blocks/teamtargetlist';\n@import 'blocks/testimoniallist';\n@import 'blocks/tickertape';\n@import 'blocks/tooltip';\n@import 'blocks/usvote';\n@import 'blocks/usvotestatevotinginformation';\n@import 'blocks/verticalvideo';\n@import 'blocks/video';\n@import 'blocks/videolisting';\n@import 'blocks/vote';\n@import 'blocks/votehero';\n@import 'blocks/winstat';\n@import 'blocks/winstatlist';\n\n// Components\n@import 'components/cmplz';\n@import 'components/footer';\n@import 'components/header';\n@import 'components/geoalert';\n@import 'components/meerkat';\n@import 'components/popup';\n\n// Templates\n@import 'templates/default';\n\n.winstatlist {\n  &[data-handdrawn-color='#160FCC'] {\n    .winstat {\n      &[data-visible='true']:not(.hide) {\n        .handdrawnunderline {\n          &::after {\n            background-image: url(\"data:image/svg+xml;charset=UTF-8,%3csvg class='underline-svg' width='241' height='29' viewBox='0 0 241 29' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cstyle type='text/css'%3e .underline-svg %7b color: var(--handdrawn-color); stroke-dasharray: 1500; stroke-dashoffset: 1500; animation: dashing 1.5s ease-in 0.3s; animation-fill-mode: forwards;%7d %40keyframes dashing %7b to %7b stroke-dashoffset: 0;%7d %7d %3c/style%3e%3cpath d='M41.6569 6.73385C105.333 2.68513 168.772 0.705006 232.035 4.64298C202.832 10.8993 171.636 6.44779 142.629 7.48694C96.2369 9.15185 49.7258 10.9778 3.15643 15.0675C15.7515 12.3205 31.3488 13.2362 43.3888 12.696C73.8265 11.4717 104.225 10.5921 134.643 10.4134C168.996 10.162 203.507 10.2781 237.583 13.3134C196.713 12.468 155.249 15.0474 114.242 17.1553C77.3872 19.0408 39.9945 18.5942 3.08268 25.3635C45.8788 24.3341 88.8118 19.478 131.43 20.8733C154.299 21.5752 180.047 18.2411 201.986 26.1717' stroke='%23160FCC' stroke-width='5' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e \");\n          }\n        }\n      }\n    }\n  }\n\n  &[data-handdrawn-color='#F65137'] {\n    .winstat {\n      &[data-visible='true']:not(.hide) {\n        .handdrawnunderline {\n          &::after {\n            background-image: url(\"data:image/svg+xml;charset=UTF-8,%3csvg class='underline-svg' width='241' height='29' viewBox='0 0 241 29' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cstyle type='text/css'%3e .underline-svg %7b color: var(--handdrawn-color); stroke-dasharray: 1500; stroke-dashoffset: 1500; animation: dashing 1.5s ease-in 0.3s; animation-fill-mode: forwards;%7d %40keyframes dashing %7b to %7b stroke-dashoffset: 0;%7d %7d %3c/style%3e%3cpath d='M41.6569 6.73385C105.333 2.68513 168.772 0.705006 232.035 4.64298C202.832 10.8993 171.636 6.44779 142.629 7.48694C96.2369 9.15185 49.7258 10.9778 3.15643 15.0675C15.7515 12.3205 31.3488 13.2362 43.3888 12.696C73.8265 11.4717 104.225 10.5921 134.643 10.4134C168.996 10.162 203.507 10.2781 237.583 13.3134C196.713 12.468 155.249 15.0474 114.242 17.1553C77.3872 19.0408 39.9945 18.5942 3.08268 25.3635C45.8788 24.3341 88.8118 19.478 131.43 20.8733C154.299 21.5752 180.047 18.2411 201.986 26.1717' stroke='%23F65137' stroke-width='5' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e \");\n          }\n        }\n      }\n    }\n  }\n\n  &[data-handdrawn-color='#FFBF10'] {\n    .winstat {\n      &[data-visible='true']:not(.hide) {\n        .handdrawnunderline {\n          &::after {\n            background-image: url(\"data:image/svg+xml;charset=UTF-8,%3csvg class='underline-svg' width='241' height='29' viewBox='0 0 241 29' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cstyle type='text/css'%3e .underline-svg %7b color: var(--handdrawn-color); stroke-dasharray: 1500; stroke-dashoffset: 1500; animation: dashing 1.5s ease-in 0.3s; animation-fill-mode: forwards;%7d %40keyframes dashing %7b to %7b stroke-dashoffset: 0;%7d %7d %3c/style%3e%3cpath d='M41.6569 6.73385C105.333 2.68513 168.772 0.705006 232.035 4.64298C202.832 10.8993 171.636 6.44779 142.629 7.48694C96.2369 9.15185 49.7258 10.9778 3.15643 15.0675C15.7515 12.3205 31.3488 13.2362 43.3888 12.696C73.8265 11.4717 104.225 10.5921 134.643 10.4134C168.996 10.162 203.507 10.2781 237.583 13.3134C196.713 12.468 155.249 15.0474 114.242 17.1553C77.3872 19.0408 39.9945 18.5942 3.08268 25.3635C45.8788 24.3341 88.8118 19.478 131.43 20.8733C154.299 21.5752 180.047 18.2411 201.986 26.1717' stroke='%23FFBF10' stroke-width='5' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e \");\n          }\n        }\n      }\n    }\n  }\n\n  &[data-handdrawn-color='#00A551'] {\n    .winstat {\n      &[data-visible='true']:not(.hide) {\n        .handdrawnunderline {\n          &::after {\n            background-image: url(\"data:image/svg+xml;charset=UTF-8,%3csvg class='underline-svg' width='241' height='29' viewBox='0 0 241 29' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cstyle type='text/css'%3e .underline-svg %7b color: var(--handdrawn-color); stroke-dasharray: 1500; stroke-dashoffset: 1500; animation: dashing 1.5s ease-in 0.3s; animation-fill-mode: forwards;%7d %40keyframes dashing %7b to %7b stroke-dashoffset: 0;%7d %7d %3c/style%3e%3cpath d='M41.6569 6.73385C105.333 2.68513 168.772 0.705006 232.035 4.64298C202.832 10.8993 171.636 6.44779 142.629 7.48694C96.2369 9.15185 49.7258 10.9778 3.15643 15.0675C15.7515 12.3205 31.3488 13.2362 43.3888 12.696C73.8265 11.4717 104.225 10.5921 134.643 10.4134C168.996 10.162 203.507 10.2781 237.583 13.3134C196.713 12.468 155.249 15.0474 114.242 17.1553C77.3872 19.0408 39.9945 18.5942 3.08268 25.3635C45.8788 24.3341 88.8118 19.478 131.43 20.8733C154.299 21.5752 180.047 18.2411 201.986 26.1717' stroke='%2300A551' stroke-width='5' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e \");\n          }\n        }\n      }\n    }\n  }\n}\n\n.teamleaderboard__heading {\n  &[data-visible='true'] {\n    .handdrawnunderline {\n      &::after {\n        background-image: url(\"data:image/svg+xml;charset=UTF-8,%3csvg class='underline-svg' width='241' height='29' viewBox='0 0 241 29' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cstyle type='text/css'%3e .underline-svg %7b color: var(--handdrawn-color); stroke-dasharray: 1500; stroke-dashoffset: 1500; animation: dashing 1.5s ease-in 0.66s; animation-fill-mode: forwards;%7d %40keyframes dashing %7b to %7b stroke-dashoffset: 0;%7d %7d %3c/style%3e%3cpath d='M41.6569 6.73385C105.333 2.68513 168.772 0.705006 232.035 4.64298C202.832 10.8993 171.636 6.44779 142.629 7.48694C96.2369 9.15185 49.7258 10.9778 3.15643 15.0675C15.7515 12.3205 31.3488 13.2362 43.3888 12.696C73.8265 11.4717 104.225 10.5921 134.643 10.4134C168.996 10.162 203.507 10.2781 237.583 13.3134C196.713 12.468 155.249 15.0474 114.242 17.1553C77.3872 19.0408 39.9945 18.5942 3.08268 25.3635C45.8788 24.3341 88.8118 19.478 131.43 20.8733C154.299 21.5752 180.047 18.2411 201.986 26.1717' stroke='%23FFBF10' stroke-width='5' stroke-miterlimit='10' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e \");\n      }\n    }\n\n    .handdrawnunderline2 {\n      &::after {\n        background-image: url(\"data:image/svg+xml;charset=UTF-8,%3csvg class='underline-svg' width='192' height='23' viewBox='0 0 192 23' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cstyle type='text/css'%3e .underline-svg %7b color: var(--handdrawn-color); stroke-dasharray: 1500; stroke-dashoffset: 1500; animation: dashing 1.5s ease-in 0.66s; animation-fill-mode: forwards;%7d %40keyframes dashing %7b to %7b stroke-dashoffset: 0;%7d %7d %3c/style%3e%3cpath d='M3 3.93659C60.6962 3.58216 118.576 2.74434 176.288 3.15845C180.386 3.18785 191.443 2.53133 188.512 3.69342C187.11 4.24892 184.793 4.20521 182.879 4.37428C177.995 4.80569 173.065 5.14829 168.139 5.49285C130.953 8.09394 93.6888 10.4641 56.5683 13.2255C48.1759 13.8499 39.7534 14.4133 31.402 15.1222C27.3839 15.4633 28.9319 15.5006 32.2409 15.4627C57.9224 15.1685 83.5766 14.2151 109.298 14.5873C117.458 14.7054 165.998 14.5588 158.323 20' stroke='%23FFBF10' stroke-width='5' stroke-linecap='round'/%3e%3c/svg%3e \");\n      }\n    }\n  }\n}\n\n/**\n* Climate page styles\n*/\n\n.mt-80 {\n  margin-top: -80px;\n}\n\n.pt100 {\n  padding-top: px-to-rem(100);\n}\n\n.partner-logos {\n  max-width: 965px !important;\n\n  .wp-block-image {\n    align-items: center;\n\n    img {\n      max-height: 70px !important;\n      width: fit-content !important;\n    }\n  }\n}\n", "/**\n  IMPORT WEBFONTS\n**/\n\n@font-face {\n  font-family: Obviously;\n  src: url('../fonts/Obviously-Regular.woff2') format('woff2'),\n    url('../fonts/Obviously-Regular.woff') format('woff');\n  font-weight: 400;\n  font-style: normal;\n  font-display: swap;\n}\n\n@font-face {\n  font-family: Obviously;\n  src: url('../fonts/Obviously-Italic.woff2') format('woff2'),\n    url('../fonts/Obviously-Italic.woff') format('woff');\n  font-weight: 400;\n  font-style: italic;\n  font-display: swap;\n}\n\n@font-face {\n  font-family: Obviously;\n  src: url('../fonts/Obviously-Bold.woff2') format('woff2'),\n    url('../fonts/Obviously-Bold.woff') format('woff');\n  font-weight: 700;\n  font-style: normal;\n  font-display: swap;\n}\n\n@font-face {\n  font-family: Obviously;\n  src: url('../fonts/Obviously-Bold_Italic.woff2') format('woff2'),\n    url('../fonts/Obviously-Bold_Italic.woff') format('woff');\n  font-weight: 700;\n  font-style: italic;\n  font-display: swap;\n}\n\n@font-face {\n  font-family: Obviously;\n  src: url('../fonts/Obviously-Black.woff2') format('woff2'),\n    url('../fonts/Obviously-Black.woff') format('woff');\n  font-weight: 900;\n  font-style: normal;\n  font-display: swap;\n}\n\n@font-face {\n  font-family: Obviously;\n  src: url('../fonts/Obviously-Black_Italic.woff2') format('woff2'),\n    url('../fonts/Obviously-Black_Italic.woff') format('woff');\n  font-weight: 900;\n  font-style: italic;\n  font-display: swap;\n}\n\n@font-face {\n  font-family: 'Obviously Narrow';\n  src: url('../fonts/Obviously-NarrowBold.woff2') format('woff2-variations'),\n    url('../fonts/Obviously-NarrowBold.woff') format('woff');\n  font-weight: 700;\n  font-style: normal;\n  font-display: swap;\n}\n\n@font-face {\n  font-family: 'Obviously Narrow';\n  src: url('../fonts/Obviously-NarrowBoldItalic.woff2')\n      format('woff2-variations'),\n    url('../fonts/Obviously-NarrowBoldItalic.woff') format('woff');\n  font-weight: 700;\n  font-style: italic;\n  font-display: swap;\n}\n\n@font-face {\n  font-family: 'Obviously Narrow';\n  src: url('../fonts/Obviously-NarrowSemibold.woff2') format('woff2-variations'),\n    url('../fonts/Obviously-NarrowSemibold.woff') format('woff');\n  font-weight: 600;\n  font-style: normal;\n  font-display: swap;\n}\n\n@font-face {\n  font-family: 'Obviously Narrow';\n  src: url('../fonts/Obviously-NarrowSemiboldItalic.woff2')\n      format('woff2-variations'),\n    url('../fonts/Obviously-NarrowSemiboldItalic.woff') format('woff');\n  font-weight: 600;\n  font-style: italic;\n  font-display: swap;\n}\n\n@font-face {\n  font-family: 'Obviously Narrow';\n  src: url('../fonts/Obviously-NarrowRegular.woff2') format('woff2-variations'),\n    url('../fonts/Obviously-NarrowRegular.woff') format('woff');\n  font-weight: 400;\n  font-style: normal;\n  font-display: swap;\n}\n\n@font-face {\n  font-family: 'Obviously Narrow';\n  src: url('../fonts/Obviously-NarrowItalic.woff2') format('woff2-variations'),\n    url('../fonts/Obviously-NarrowItalic.woff') format('woff');\n  font-weight: 400;\n  font-style: italic;\n  font-display: swap;\n}\n\n@font-face {\n  font-display: swap;\n  font-family: 'Brown STD';\n  src: url('../fonts/BrownStd-Bold.woff2') format('woff2'),\n    url('../fonts/BrownStd-Bold.woff') format('woff');\n  font-weight: 700;\n  font-style: normal;\n}\n\n@font-face {\n  font-display: swap;\n  font-family: Inter;\n  src: url('../fonts/Inter-Variable.woff2') format('woff2'),\n    url('../fonts/Inter-Variable.woff') format('woff');\n  font-style: normal;\n}\n\n@font-face {\n  font-display: swap;\n  font-family: Inter;\n  src: url('../fonts/Inter-Italic-Variable.woff2') format('woff2'),\n    url('../fonts/Inter-Italic-Variable.woff') format('woff');\n  font-style: italic;\n}\n\n@font-face {\n  font-display: swap;\n  font-family: 'Rainer Web';\n  font-style: normal;\n  font-weight: 600;\n  src: url('../fonts/Rainer-Bold.woff2') format('woff2'),\n    url('../fonts/Rainer-Bold.woff') format('woff');\n}\n\n@font-face {\n  font-display: swap;\n  font-family: 'Rainer Web';\n  font-style: italic;\n  font-weight: 600;\n  src: url('../fonts/Rainer-BoldSlanted.woff2') format('woff2'),\n    url('../fonts/Rainer-BoldSlanted.woff') format('woff');\n}\n\n@font-face {\n  font-display: swap;\n  font-family: Renika;\n  src: url('../fonts/Renika.woff2') format('woff2'),\n    url('../fonts/Renika.ttf') format('truetype');\n  font-weight: 400;\n  font-style: normal;\n}\n", "/**\n  CSS RESET\n**/\n\nhtml {\n  text-size-adjust: 100%;\n  box-sizing: border-box;\n}\n\n*,\n*::before,\n*::after {\n  box-sizing: inherit;\n}\n\nbody {\n  margin: 0;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\nh1,\nh2,\nh3,\nh4,\np,\nblockquote,\nfigure,\nol,\nul {\n  margin: 0;\n  padding: 0;\n}\n\nmain,\nli {\n  display: block;\n}\n\nol,\nul {\n  list-style: none;\n}\n\nstrong {\n  font-weight: bold;\n}\n\na,\n[role='button'],\n.ajax,\n.cta {\n  color: inherit;\n}\n\na {\n  @include click-cursor;\n\n  text-decoration: none;\n}\n\nbutton {\n  @include click-cursor;\n\n  overflow: visible;\n  border: 0;\n  font: inherit;\n  -webkit-font-smoothing: inherit;\n  letter-spacing: inherit;\n  background: none;\n}\n\n::-moz-focus-inner {\n  padding: 0;\n  border: 0;\n}\n\n:focus {\n  outline: 0;\n}\n\nimg {\n  max-width: 100%;\n  height: auto;\n  border: 0;\n}\n\nblockquote,\nq {\n  quotes: none;\n}\n", "/**\n  GLOBAL FORM STYLES\n**/\n\ninput[type='email'],\ninput[type='month'],\ninput[type='number'],\ninput[type='password'],\ninput[type='search'],\ninput[type='tel'],\ninput[type='text'],\ninput[type='time'],\ninput[type='url'],\ninput[type='week'] {\n  @include input($color: $golden-rod, $active-color: $patriotic-red);\n\n  text-overflow: ellipsis;\n}\n\ninput[type='submit'] {\n  /* stylelint-disable */\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  /* stylelint-enable */\n\n  &:not(.button-secondary) {\n    @include button;\n    @include click-cursor;\n  }\n}\n\ntextarea {\n  @include textarea;\n}\n\ninput[type='checkbox'] {\n  @include click-cursor;\n\n  /* stylelint-disable */\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  /* stylelint-enable */\n  background-color: transparent;\n  border: 3px solid $golden-rod;\n  border-radius: 0;\n  color: inherit;\n  display: inline-grid;\n  font-family: $inter-font;\n  height: px-to-rem(22px);\n  margin: 0;\n  position: relative;\n  width: px-to-rem(22px);\n  z-index: 1;\n\n  & + .icon-checkmark {\n    stroke-dasharray: 30;\n    stroke-dashoffset: 30;\n    transition: all 300ms steps(5, end);\n    position: absolute;\n    left: 6px;\n    top: -2px;\n    z-index: 0;\n  }\n\n  &:checked + .icon-checkmark {\n    stroke-dashoffset: 0;\n    transition: all 0.2s cubic-bezier(0.65, 0, 0.45, 1);\n  }\n}\n\ninput[type='radio'] {\n  @include click-cursor;\n\n  /* stylelint-disable */\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  /* stylelint-enable */\n  background-color: transparent;\n  border: 3px solid $golden-rod;\n  border-radius: 50%;\n  color: inherit;\n  display: inline-grid;\n  font-family: $inter-font;\n  height: px-to-rem(22px);\n  position: relative;\n  width: px-to-rem(22px);\n  z-index: 1;\n\n  & + .icon-scribble {\n    left: 4px;\n    position: absolute;\n    stroke-dasharray: 75;\n    stroke-dashoffset: 75;\n    transition: all 300ms steps(5, end);\n    top: 4px;\n    z-index: 0;\n  }\n\n  &:checked + .icon-scribble {\n    stroke-dashoffset: 0;\n  }\n}\n\nlabel {\n  @include h6;\n\n  color: $white;\n  margin-top: 15px;\n  position: relative;\n\n  &.checkbox,\n  &.radio {\n    @include click-cursor;\n\n    display: grid;\n    grid-template-columns: 22px 1fr;\n    grid-column-gap: 15px;\n  }\n}\n\n@keyframes bouncehand {\n  from {\n    transform: translateX(-10px);\n  }\n\n  to {\n    transform: translateX(0);\n  }\n}\n\n.input-group {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n\n  &__error-msg {\n    @include error-message($color: $crt-black, $background: $patriotic-red);\n\n    display: none;\n  }\n\n  &__legal-disclaimer {\n    @include caption-small;\n  }\n\n  &.error {\n    .input-group__error-msg {\n      display: inline;\n\n      &::after {\n        animation-duration: 0.6s;\n        animation-name: bouncehand;\n        animation-iteration-count: infinite;\n        animation-direction: alternate;\n      }\n    }\n\n    .input-group__input {\n      background-position: 0 100%;\n      color: $patriotic-red;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    &.error {\n      margin-right: 0;\n    }\n  }\n}\n\n.form {\n  &__post-submit {\n    @include h6;\n\n    p {\n      @include h6;\n    }\n  }\n\n  &__post-submit-icon {\n    height: 45px;\n    margin-bottom: 20px;\n    width: 45px;\n  }\n\n  &__fieldset {\n    border: none;\n    margin: 40px 0 0;\n    padding: 0;\n  }\n\n  &__legend {\n    @include h6;\n  }\n\n  &__form-ooe-team-text {\n    margin-top: 5px;\n    line-height: 1;\n  }\n\n  &--golden {\n    input[type='email'],\n    input[type='month'],\n    input[type='number'],\n    input[type='password'],\n    input[type='search'],\n    input[type='tel'],\n    input[type='text'],\n    input[type='time'],\n    input[type='url'],\n    input[type='week'] {\n      @include input(\n        $color: $electoral-blue,\n        $active-color: $patriotic-red-dark\n      );\n    }\n\n    input[type='checkbox'] {\n      border: 3px solid $electoral-blue;\n\n      & + .icon-checkmark {\n        stroke: $electoral-blue;\n      }\n    }\n\n    input[type='radio'] {\n      border: 3px solid $electoral-blue;\n    }\n\n    input[type='submit']:not(.button-secondary) {\n      @include button;\n    }\n\n    label {\n      color: $crt-black;\n    }\n\n    .input-group {\n      &__error-msg {\n        @include error-message(\n          $color: $white,\n          $background: $patriotic-red-dark\n        );\n\n        display: none;\n      }\n\n      &.error {\n        .input-group__input {\n          color: $patriotic-red-dark;\n        }\n      }\n    }\n  }\n\n  &--crt-black {\n    label {\n      color: $white;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    &__post-submit-icon {\n      height: 60px;\n      margin-bottom: 30px;\n      width: 60px;\n    }\n  }\n}\n", "@use 'sass:map';\n\n/**\n  MIXINS\n**/\n\n// ======================================\n// Vertical Align\n// ======================================\n\n@mixin vertical-align {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n}\n\n// ======================================\n// Horizontal Align\n// ======================================\n\n@mixin horizontal-align {\n  left: 50%;\n  position: absolute;\n  transform: translateX(-50%);\n}\n\n@mixin vertical-horizontal-align {\n  left: 50%;\n  position: absolute;\n  top: 50%;\n  transform: translate(-50%, -50%);\n}\n\n// ======================================\n// Visually Hidden\n// Visually hide the element from the\n// screen but still have it accessible\n// via screenreaders\n// ======================================\n\n@mixin is-visually-hidden() {\n  border: 0;\n  clip: rect(0 0 0 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n\n// ======================================\n// Ellipsis\n// ======================================\n\n@mixin ellipsis {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n// ======================================\n// IE MEDIA QUERY\n// ======================================\n\n@mixin ieonly() {\n  @media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {\n    @content;\n  }\n}\n\n// ======================================\n// Breakpoint Mixin\n// ======================================\n\n@mixin breakpoint($breakpoint, $direction) {\n  @if map-has-key($breakpoints, $breakpoint) {\n    $breakpoint-value: map.get($breakpoints, $breakpoint);\n\n    @if $direction == max {\n      @media (max-width: #{($breakpoint-value - 1)}px) {\n        @content;\n      }\n    } @else if $direction == min {\n      @media (min-width: #{$breakpoint-value}px) {\n        @content;\n      }\n    } @else {\n      @media ($direction: #{$breakpoint-value}px) {\n        @content;\n      }\n    }\n  } @else {\n    @if $direction == max {\n      @media (max-width: #{$breakpoint}px) {\n        @content;\n      }\n    } @else if $direction == min {\n      @media (min-width: #{$breakpoint}px) {\n        @content;\n      }\n    } @else {\n      @media ($direction: #{$breakpoint}px) {\n        @content;\n      }\n    }\n  }\n}\n\n// ======================================\n// Mobile First\n// ======================================\n\n@mixin break-min($media) {\n  @if type-of($media) == 'number' {\n    @if unit($media) == 'px' {\n      @media screen and (min-width: #{$media}) {\n        @content;\n      }\n    } @else {\n      @media screen and (min-width: #{$media}em) {\n        @content;\n      }\n    }\n  } @else {\n    @media screen and (#{$media}) {\n      @content;\n    }\n  }\n}\n\n// Desktop First\n@mixin break-max($media) {\n  @if type-of($media) == 'number' {\n    @if unit($media) == 'px' {\n      // -1 px\n      @media screen and (max-width: #{$media - 0.063}) {\n        @content;\n      }\n    } @else {\n      // -1 px\n      @media screen and (max-width: #{$media - 0.063}em) {\n        @content;\n      }\n    }\n  } @else {\n    @media screen and (#{$media}) {\n      @content;\n    }\n  }\n}\n\n// ======================================\n// Headings\n// ======================================\n\n@mixin h1 {\n  font-family: $rainer-font;\n  font-size: px-to-rem(70px);\n  font-weight: map.get($font-weights, bold);\n  letter-spacing: 0.02em;\n  line-height: 90%;\n  text-transform: uppercase;\n  /* stylelint-disable */\n  --wp--preset--font-size--small: #{px-to-rem(64px)};\n  --wp--preset--font-size--medium: #{px-to-rem(70px)};\n  --wp--preset--font-size--large: #{px-to-rem(74px)};\n  --wp--preset--font-size--x-large: #{px-to-rem(78px)};\n  /* stylelint-enable */\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(120px);\n    /* stylelint-disable */\n    --wp--preset--font-size--small: #{px-to-rem(110px)};\n    --wp--preset--font-size--medium: #{px-to-rem(120px)};\n    --wp--preset--font-size--large: #{px-to-rem(130px)};\n    --wp--preset--font-size--x-large: #{px-to-rem(140px)};\n    /* stylelint-enable */\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(165px);\n    /* stylelint-disable */\n    --wp--preset--font-size--small: #{px-to-rem(158px)};\n    --wp--preset--font-size--medium: #{px-to-rem(165px)};\n    --wp--preset--font-size--large: #{px-to-rem(172px)};\n    --wp--preset--font-size--x-large: #{px-to-rem(186px)};\n    /* stylelint-enable */\n  }\n}\n\n@mixin h2 {\n  font-family: $rainer-font;\n  font-size: px-to-rem(60px);\n  font-weight: map.get($font-weights, bold);\n  letter-spacing: 0.02em;\n  line-height: 90%;\n  text-transform: uppercase;\n  /* stylelint-disable */\n  --wp--preset--font-size--small: #{px-to-rem(54px)};\n  --wp--preset--font-size--medium: #{px-to-rem(60px)};\n  --wp--preset--font-size--large: #{px-to-rem(66px)};\n  --wp--preset--font-size--x-large: #{px-to-rem(72px)};\n  /* stylelint-enable */\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(80px);\n    letter-spacing: 0.02em;\n    /* stylelint-disable */\n    --wp--preset--font-size--small: #{px-to-rem(74px)};\n    --wp--preset--font-size--medium: #{px-to-rem(80px)};\n    --wp--preset--font-size--large: #{px-to-rem(86px)};\n    --wp--preset--font-size--x-large: #{px-to-rem(92px)};\n    /* stylelint-enable */\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(92px);\n    /* stylelint-disable */\n    --wp--preset--font-size--small: #{px-to-rem(84px)};\n    --wp--preset--font-size--medium: #{px-to-rem(92px)};\n    --wp--preset--font-size--large: #{px-to-rem(100px)};\n    --wp--preset--font-size--x-large: #{px-to-rem(108px)};\n    /* stylelint-enable */\n  }\n}\n\n@mixin h3 {\n  font-family: $obviously-narrow-font;\n  font-size: px-to-rem(30px);\n  font-weight: 700;\n  line-height: 125%;\n  text-transform: none;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(36px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(48px);\n  }\n}\n\n@mixin h4 {\n  font-family: $obviously-narrow-font;\n  font-size: px-to-rem(22px);\n  font-weight: 700;\n  line-height: 125%;\n  text-transform: none;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(26px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(36px);\n  }\n}\n\n@mixin h5 {\n  font-family: $obviously-narrow-font;\n  font-size: px-to-rem(18px);\n  font-weight: 700;\n  line-height: 125%;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(20px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(20px);\n  }\n}\n\n@mixin h6 {\n  font-family: $brown-std-font;\n  font-size: px-to-rem(14px);\n  font-weight: 700;\n  line-height: 140%;\n  text-transform: uppercase;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(16px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(16px);\n  }\n}\n\n@mixin community-stats {\n  font-family: $rainer-font;\n  font-size: px-to-rem(70px);\n  font-weight: map.get($font-weights, bold);\n  letter-spacing: 0.015em;\n  line-height: 90%;\n  text-transform: uppercase;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(125px);\n    letter-spacing: 0.02em;\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(140px);\n  }\n\n  @include breakpoint(l-desktop, min) {\n    font-size: px-to-rem(160px);\n  }\n\n  @include breakpoint(xl-desktop, min) {\n    font-size: px-to-rem(180px);\n  }\n}\n\n// ======================================\n// Typography\n// ======================================\n\n@mixin body {\n  font-family: $inter-font;\n  font-size: px-to-rem(16px);\n  font-weight: 400;\n  line-height: 155%;\n  /* stylelint-disable */\n  --wp--preset--font-size--small: #{px-to-rem(14px)};\n  --wp--preset--font-size--medium: #{px-to-rem(16px)};\n  --wp--preset--font-size--large: #{px-to-rem(20px)};\n  --wp--preset--font-size--x-large: #{px-to-rem(22px)};\n  --wp--preset--font-size--xx-large: #{px-to-rem(24px)};\n  /* stylelint-enable */\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(17px);\n    /* stylelint-disable */\n    --wp--preset--font-size--small: #{px-to-rem(15px)};\n    --wp--preset--font-size--medium: #{px-to-rem(17px)};\n    --wp--preset--font-size--large: #{px-to-rem(22px)};\n    --wp--preset--font-size--x-large: #{px-to-rem(26px)};\n    --wp--preset--font-size--xx-large: #{px-to-rem(28px)};\n    /* stylelint-enable */\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(17px);\n    /* stylelint-disable */\n    --wp--preset--font-size--small: #{px-to-rem(15px)};\n    --wp--preset--font-size--medium: #{px-to-rem(17px)};\n    --wp--preset--font-size--large: #{px-to-rem(22px)};\n    --wp--preset--font-size--x-large: #{px-to-rem(28px)};\n    --wp--preset--font-size--xx-large: #{px-to-rem(30px)};\n    /* stylelint-enable */\n  }\n}\n\n@mixin bodysmall {\n  font-family: $inter-font;\n  font-size: px-to-rem(14px);\n  font-weight: 400;\n  line-height: 155%;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(15px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(15px);\n  }\n}\n\n@mixin bodylarge {\n  font-family: $inter-font;\n  font-size: px-to-rem(20px);\n  font-weight: 400;\n  line-height: 140%;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(22px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(22px);\n  }\n}\n\n@mixin bodyxlarge {\n  font-family: $inter-font;\n  font-size: px-to-rem(22px);\n  font-weight: 400;\n  line-height: 150%;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(26px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(28px);\n  }\n}\n\n@mixin bodyxxlarge {\n  font-family: $inter-font;\n  font-size: px-to-rem(24px);\n  font-weight: 400;\n  line-height: 150%;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(28px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(30px);\n  }\n}\n\n@mixin pullquote {\n  font-family: $inter-font;\n  font-size: px-to-rem(18px);\n  font-style: italic;\n  font-weight: 400;\n  line-height: 150%;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(20px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(22px);\n  }\n}\n\n@mixin featured-text {\n  font-family: $inter-font;\n  font-size: px-to-rem(20px);\n  font-weight: 400;\n  line-height: 150%;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(24px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(24px);\n  }\n}\n\n@mixin cite {\n  font-family: $rainer-font;\n  font-size: px-to-rem(70px);\n  font-style: normal;\n  font-weight: 400;\n  line-height: 150%;\n  text-transform: uppercase;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(85px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(100px);\n  }\n}\n\n@mixin inline-link($color: $electoral-blue, $hover: $patriotic-red) {\n  background: linear-gradient(to left, $color 50%, $hover 50%) right;\n  background-position: right 100%;\n  background-repeat: no-repeat;\n  background-size: 200% 1px;\n  color: $color;\n  display: inline;\n  transition: background 0.3s ease, color 0.3s ease-in-out, font-style 0.3s ease;\n  word-break: break-word;\n\n  &:hover,\n  &:focus {\n    background-position: left 100%;\n    color: $hover;\n    font-style: italic;\n  }\n}\n\n@mixin small {\n  font-family: $inter-font;\n  font-size: px-to-rem(14px);\n  font-weight: 400;\n  line-height: 155%;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(16px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(16px);\n  }\n}\n\n@mixin caption {\n  font-family: $inter-font;\n  font-size: px-to-rem(14px);\n  font-weight: 400;\n  line-height: 155%;\n\n  a {\n    text-decoration: underline;\n\n    &:hover {\n      font-style: normal;\n      font-weight: 400;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(16px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(16px);\n  }\n}\n\n@mixin caption-small {\n  font-family: $inter-font;\n  font-size: px-to-rem(10px);\n  font-weight: 400;\n  line-height: 155%;\n\n  a {\n    text-decoration: underline;\n\n    &:hover {\n      font-weight: 900;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(11px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(12px);\n  }\n}\n\n@mixin commentary {\n  font-family: $renika-font;\n  font-size: px-to-rem(30px);\n  font-weight: 400;\n  line-height: 30px;\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(35px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(35px);\n  }\n}\n\n// ======================================\n// Buttons\n// ======================================\n\n@mixin button(\n  $color: $patriotic-red,\n  $hover: $patriotic-red-dark,\n  $text-color: $white\n) {\n  @include click-cursor;\n\n  background-color: $color;\n  border: 5px solid $color;\n  border-radius: 0;\n  color: $text-color;\n  clip-path: $pixel-tips-button;\n  display: inline-block;\n  font-family: $inter-font;\n  font-size: px-to-rem(16px);\n  font-weight: 800;\n  line-height: 130%;\n  padding: 17px min(3vw, 40px) 20px;\n  text-align: center;\n  transition: background 0.3s ease-in-out, border 0.3s ease-in-out;\n\n  &:hover,\n  &:focus {\n    background-color: $hover;\n    border-color: $hover;\n    color: $text-color;\n    font-style: italic;\n  }\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(18px);\n    padding-left: 30px;\n    padding-right: 30px;\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(18px);\n  }\n}\n\n@mixin secondary-button($color: $golden-rod, $hover: $golden-rod-dark) {\n  @include click-cursor;\n\n  background-color: transparent;\n  border: 5px solid $color;\n  border-radius: 0;\n  color: $color;\n  clip-path: $pixel-tips-button;\n  display: inline-block;\n  font-family: $inter-font;\n  font-size: px-to-rem(16px);\n  font-weight: 800;\n  line-height: 130%;\n  padding: 17px min(3vw, 40px) 20px;\n  text-align: center;\n  transition: color 0.3s ease-in-out, border 0.3s ease-in-out;\n\n  &:hover {\n    background-color: transparent;\n    border-color: $hover;\n    color: $hover;\n    font-style: italic;\n  }\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(18px);\n    padding-left: 30px;\n    padding-right: 30px;\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(18px);\n  }\n}\n\n// ======================================\n// Cursors\n// ======================================\n\n@mixin click-cursor {\n  cursor: pointer;\n}\n\n@mixin pointer-cursor {\n  cursor: auto;\n}\n\n// ======================================\n// Forms\n// ======================================\n\n@mixin input($color: $golden-rod, $active-color: $patriotic-red) {\n  @include h4;\n\n  background: linear-gradient(to left, $color 50%, $active-color 50%) right;\n  background-position: 100% 100%;\n  background-size: 200% 6px;\n  background-repeat: no-repeat;\n  border: none;\n  border-radius: 0;\n  color: $color;\n  height: 60px;\n  padding-bottom: 10px;\n  padding-top: 20px;\n  transition: background 0.3s ease, color 0.3s ease;\n\n  &:active,\n  &:focus {\n    background-position: 0 100%;\n  }\n\n  &:-webkit-autofill,\n  &:-webkit-autofill:focus {\n    transition: background-color 0s 600000s, color 0s 600000s;\n    background: linear-gradient(to left, $golden-rod 50%, $active-color 50%)\n      right;\n    background-position: 0 100%;\n    background-size: 200% 6px;\n    background-repeat: no-repeat;\n    border-bottom: 6px solid $active-color;\n  }\n}\n\n@mixin input-med($color: $golden-rod, $active-color: $patriotic-red) {\n  @include h4;\n\n  background: linear-gradient(to left, $color 50%, $active-color 50%) right;\n  background-position: 100% 100%;\n  background-size: 200% 6px;\n  background-repeat: no-repeat;\n  border: none;\n  border-radius: 0;\n  color: $color;\n  height: 60px;\n  padding-bottom: 20px;\n  padding-top: 16px;\n  transition: background 0.3s ease, color 0.3s ease;\n\n  &:active,\n  &:focus {\n    background-position: 0 100%;\n  }\n\n  &:-webkit-autofill,\n  &:-webkit-autofill:focus {\n    transition: background-color 0s 600000s, color 0s 600000s;\n    background: linear-gradient(to left, $golden-rod 50%, $active-color 50%)\n      right;\n    background-position: 0 100%;\n    background-size: 200% 6px;\n    background-repeat: no-repeat;\n    border-bottom: 6px solid $active-color;\n  }\n}\n\n@mixin input-small($color: $golden-rod, $active-color: $patriotic-red) {\n  background: linear-gradient(to left, $golden-rod 50%, $active-color 50%) right;\n  background-position: 100% 100%;\n  background-size: 200% 6px;\n  background-repeat: no-repeat;\n  border: none;\n  color: $color;\n  height: 44px;\n  font-family: $obviously-narrow-font;\n  font-size: px-to-rem(14px);\n  font-weight: 700;\n  line-height: 125%;\n  padding-bottom: 16px;\n  padding-top: 0;\n  transition: background 0.3s ease, color 0.3s ease;\n\n  &:active,\n  &:focus {\n    background-position: 0 100%;\n  }\n\n  &:-webkit-autofill,\n  &:-webkit-autofill:focus {\n    transition: background-color 0s 600000s, color 0s 600000s;\n    background: linear-gradient(to left, $golden-rod 50%, $active-color 50%)\n      right;\n    background-position: 0 100%;\n    background-size: 200% 6px;\n    background-repeat: no-repeat;\n    border-bottom: 6px solid $active-color;\n  }\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(16px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(18px);\n  }\n}\n\n@mixin textarea(\n  $color: $golden-rod,\n  $active-color: $patriotic-red,\n  $border-color: $golden-rod\n) {\n  @include body;\n\n  background: linear-gradient(to left, $border-color 50%, $active-color 50%)\n    right;\n  background-position: 100% 100%;\n  background-size: 200% 6px;\n  background-repeat: no-repeat;\n  border-left: 6px solid $border-color;\n  border-right: 6px solid $border-color;\n  border-top: 6px solid $border-color;\n  border-radius: 0;\n  color: $color;\n  height: 140px;\n  font-weight: 700;\n  padding: 20px;\n  transition: background 0.3s ease, color 0.3s ease;\n\n  &:active,\n  &:focus {\n    background-position: 0 100%;\n  }\n\n  &:-webkit-autofill,\n  &:-webkit-autofill:focus {\n    transition: background-color 0s 600000s, color 0s 600000s;\n    background: linear-gradient(to left, $border-color 50%, $active-color 50%)\n      right;\n    background-position: 0 100%;\n    background-size: 200% 6px;\n    background-repeat: no-repeat;\n    border-bottom: 6px solid $active-color;\n  }\n}\n\n@mixin error-message($color: $crt-black, $background: $patriotic-red) {\n  @include h6;\n\n  color: $color;\n  line-height: 170%;\n  margin-bottom: 16px;\n  margin-right: 70px;\n  margin-top: 0;\n  width: fit-content;\n\n  span {\n    background-image: linear-gradient(\n      180deg,\n      $background 0%,\n      $background 0%,\n      $background 100%,\n      $background 100%\n    );\n    line-height: 100%;\n    padding-bottom: 5px;\n    padding-inline-start: 5px;\n    padding-inline-end: 5px;\n    padding-top: 5px;\n    /* stylelint-disable */\n    box-decoration-break: clone;\n    -webkit-box-decoration-break: clone;\n    /* stylelint-enaable */\n    box-sizing: border-box;\n  }\n\n  &::after {\n    background-repeat: no-repeat;\n    background-size: 48px 33px;\n    content: '';\n    display: block;\n    height: 33px;\n    position: absolute;\n    right: 0;\n    top: 0;\n    width: 48px;\n\n    @if $background == $patriotic-red-dark {\n      background-image: url('data:image/svg+xml,<svg width=\"86\" height=\"56\" viewBox=\"0 0 86 56\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M43.051 4.934v9.256h9.456V9.562h4.728v4.628h9.456v4.629H19.412v4.628H43.05v4.629H28.868v4.628H43.05v4.629H28.868v4.628H24.14v-9.257h-4.728v-4.628h-4.728v4.628H9.956v-4.628H.5V14.19h9.456V9.562h4.728v4.628h4.728V9.562h4.728v4.628h14.183V.305h9.456v4.629h-4.728ZM10.06 18.769H5.33v4.628h9.456v-4.628h-4.728Z\" fill=\"%23D03620\"/><path d=\"M47.78 9.562V4.934h4.727v4.628H47.78ZM71.367 41.86h9.404V23.397H66.64v-4.628h18.86v27.669h-18.86V41.86h4.728ZM57.182 46.438h9.456v4.629H52.506v-4.629h4.676ZM43.051 51.067h9.456v4.628h-18.86v-4.628h9.404ZM38.322 41.86h4.728v4.578h-9.404v4.628h-4.728V41.86h9.404Z\" fill=\"%23D03620\"/></svg>');\n    } @else {\n      background-image: url('data:image/svg+xml,<svg width=\"86\" height=\"56\" viewBox=\"0 0 86 56\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M43.051 4.934v9.256h9.456V9.562h4.728v4.628h9.456v4.629H19.412v4.628H43.05v4.629H28.868v4.628H43.05v4.629H28.868v4.628H24.14v-9.257h-4.728v-4.628h-4.728v4.628H9.956v-4.628H.5V14.19h9.456V9.562h4.728v4.628h4.728V9.562h4.728v4.628h14.183V.305h9.456v4.629h-4.728ZM10.06 18.769H5.33v4.628h9.456v-4.628h-4.728Z\" fill=\"%23F65137\"/><path d=\"M47.78 9.562V4.934h4.727v4.628H47.78ZM71.367 41.86h9.404V23.397H66.64v-4.628h18.86v27.669h-18.86V41.86h4.728ZM57.182 46.438h9.456v4.629H52.506v-4.629h4.676ZM43.051 51.067h9.456v4.628h-18.86v-4.628h9.404ZM38.322 41.86h4.728v4.578h-9.404v4.628h-4.728V41.86h9.404Z\" fill=\"%23F65137\"/></svg>');\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    &::after {\n      background-size: 85px 53px;\n      height: 53px;\n      right: -85px;\n      width: 85px;\n    }\n  }\n}\n", "/**\n  COLORS\n  color palette\n**/\n\n$electoral-blue: #160fcc;\n$electoral-blue-mid: #120bba;\n$electoral-blue-dark: #1b1698;\n$electoral-blue-light: #1b1698;\n$golden-rod: #ffbf10;\n$golden-rod-light: #ffc934;\n$golden-rod-dark: #ad7c00;\n$grey: #b3b3b3;\n$patriotic-red: #f65137;\n$patriotic-red-mid: #ef422a;\n$patriotic-red-dark: #d03620;\n$patriotic-red-light: #fee7e3;\n$team-green: #00a551;\n$team-green-very-light: #cceddc;\n$crt-black: #000;\n$crt-black-light: #171d23;\n$white: #fff;\n$grey: #dcdcdc;\n", "html {\n  @include pointer-cursor;\n\n  overflow-x: hidden;\n  scroll-behavior: smooth;\n  text-rendering: optimizelegibility;\n\n  &.popup-open {\n    overflow: hidden;\n  }\n\n  &:has(.nav-open) {\n    overflow: hidden;\n  }\n}\n\n/* Firefox */\n@media (prefers-color-scheme: dark) {\n  :root {\n    color-scheme: light;\n  }\n}\n\n/* Chrome */\n@media (forced-colors: active) {\n  :root {\n    color-scheme: light;\n  }\n}\n\nbody {\n  display: block;\n\n  .overlay {\n    display: none;\n  }\n\n  &.popup-open,\n  &.nav-open {\n    .overlay {\n      background-color: rgba($crt-black, 0.5);\n      display: block;\n      inset: 0;\n      height: 100%;\n      position: fixed;\n      width: 100%;\n    }\n  }\n\n  &.popup-open {\n    overflow: hidden;\n\n    .overlay {\n      z-index: 6;\n\n      &.popup-black {\n        background-color: rgba($white, 0.125);\n        z-index: 6;\n      }\n    }\n  }\n\n  &.nav-open {\n    .overlay {\n      z-index: 5;\n    }\n  }\n}\n\ninput {\n  @include h4;\n}\n\nh1 {\n  @include h1;\n\n  margin-bottom: $heading-margin-mobile;\n\n  @include breakpoint(tablet, min) {\n    margin-bottom: $heading-margin-tablet;\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-bottom: $heading-margin-desktop;\n  }\n\n  &.is-style-heading-2 {\n    @include h2;\n  }\n\n  &.is-style-heading-3 {\n    @include h3;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-4 {\n    @include h4;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-5 {\n    @include h5;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-6 {\n    @include h6;\n  }\n}\n\nh2 {\n  @include h2;\n\n  &.heading {\n    margin-bottom: 26px;\n    margin-top: 60px;\n\n    @include breakpoint(tablet, min) {\n      margin-bottom: 30px;\n      margin-top: 70px;\n    }\n\n    @include breakpoint(desktop, min) {\n      margin-bottom: 36px;\n      margin-top: 80px;\n    }\n  }\n\n  &.is-style-heading-1 {\n    @include h1;\n  }\n\n  &.is-style-heading-3 {\n    @include h3;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-4 {\n    @include h4;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-5 {\n    @include h5;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-6 {\n    @include h6;\n  }\n}\n\nh3 {\n  @include h3;\n\n  &.heading {\n    margin-bottom: 26px;\n    margin-top: 50px;\n\n    @include breakpoint(tablet, min) {\n      margin-bottom: 28px;\n      margin-top: 54px;\n    }\n\n    @include breakpoint(desktop, min) {\n      margin-bottom: 30px;\n      margin-top: 60px;\n    }\n  }\n\n  &.is-style-heading-1 {\n    @include h1;\n  }\n\n  &.is-style-heading-2 {\n    @include h2;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-4 {\n    @include h4;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-5 {\n    @include h5;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-6 {\n    @include h6;\n  }\n}\n\nh4 {\n  @include h4;\n\n  &.heading {\n    margin-bottom: 26px;\n    margin-top: 50px;\n\n    @include breakpoint(tablet, min) {\n      margin-bottom: 28px;\n      margin-top: 54px;\n    }\n\n    @include breakpoint(desktop, min) {\n      margin-bottom: 30px;\n      margin-top: 60px;\n    }\n  }\n\n  &.is-style-heading-1 {\n    @include h1;\n  }\n\n  &.is-style-heading-2 {\n    @include h2;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-3 {\n    @include h3;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-5 {\n    @include h5;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-6 {\n    @include h6;\n  }\n}\n\nh5 {\n  @include h5;\n\n  &.heading {\n    margin-bottom: 26px;\n    margin-top: 50px;\n\n    @include breakpoint(tablet, min) {\n      margin-bottom: 28px;\n      margin-top: 54px;\n    }\n\n    @include breakpoint(desktop, min) {\n      margin-bottom: 30px;\n      margin-top: 60px;\n    }\n  }\n\n  &.is-style-heading-1 {\n    @include h1;\n  }\n\n  &.is-style-heading-2 {\n    @include h2;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-3 {\n    @include h3;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-4 {\n    @include h4;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-6 {\n    @include h6;\n  }\n}\n\nh6 {\n  @include h6;\n\n  &.heading {\n    margin-bottom: 26px;\n    margin-top: 50px;\n\n    @include breakpoint(tablet, min) {\n      margin-bottom: 28px;\n      margin-top: 54px;\n    }\n\n    @include breakpoint(desktop, min) {\n      margin-bottom: 30px;\n      margin-top: 60px;\n    }\n  }\n\n  &.is-style-heading-1 {\n    @include h1;\n  }\n\n  &.is-style-heading-2 {\n    @include h2;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-3 {\n    @include h3;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-4 {\n    @include h4;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-5 {\n    @include h5;\n\n    text-transform: none;\n  }\n\n  &.is-style-heading-6 {\n    @include h6;\n  }\n}\n\np {\n  @include body;\n\n  &.paragraph,\n  &.wp-block-paragraph {\n    a {\n      @include inline-link($color: $electoral-blue, $hover: $patriotic-red);\n    }\n\n    &--small,\n    &.has-small-font-size {\n      @include bodysmall;\n    }\n\n    &--large,\n    &.has-large-font-size {\n      @include bodylarge;\n    }\n\n    &--x-large,\n    &.has-x-large-font-size {\n      @include bodyxlarge;\n    }\n\n    &--xx-large,\n    &.has-xx-large-font-size {\n      @include bodyxxlarge;\n    }\n  }\n\n  margin-bottom: $body-margin-mobile;\n\n  @include breakpoint(tablet, min) {\n    margin-bottom: $body-margin-tablet;\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-bottom: $body-margin-desktop;\n  }\n}\n\nul {\n  &.list,\n  &.wp-block-list {\n    @include body;\n\n    display: grid;\n    list-style-position: outside;\n    list-style-type: square;\n    margin-bottom: $body-margin-mobile;\n\n    li {\n      display: list-item;\n      margin-bottom: $body-margin-mobile;\n      margin-inline-start: px-to-rem(28px);\n      padding-inline-start: px-to-rem(0);\n      padding-left: px-to-rem(6);\n\n      a {\n        @include inline-link($color: $electoral-blue, $hover: $patriotic-red);\n      }\n\n      &::marker {\n        color: $electoral-blue;\n        content: '▪   ';\n      }\n\n      &:last-of-type {\n        margin-bottom: 0;\n      }\n\n      & ol,\n      & ul {\n        margin-top: $body-margin-mobile;\n        margin-bottom: 0;\n      }\n\n      li {\n        &::marker {\n          color: $patriotic-red;\n        }\n\n        li {\n          &::marker {\n            color: $golden-rod;\n          }\n\n          li {\n            &::marker {\n              color: $electoral-blue;\n            }\n\n            li {\n              &::marker {\n                color: $patriotic-red;\n              }\n\n              li {\n                &::marker {\n                  color: $golden-rod;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    margin-bottom: $body-margin-tablet;\n\n    &.list {\n      li {\n        margin-bottom: $body-margin-tablet;\n\n        & ul,\n        & ol {\n          margin-top: $body-margin-tablet;\n          margin-bottom: 0;\n        }\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-bottom: $body-margin-desktop;\n\n    &.list {\n      li {\n        margin-bottom: $body-margin-desktop;\n\n        & ul,\n        & ol {\n          margin-top: $body-margin-desktop;\n          margin-bottom: 0;\n        }\n      }\n    }\n  }\n}\n\nol {\n  &.list,\n  &.wp-block-list {\n    @include body;\n\n    counter-reset: list-number;\n    display: grid;\n    list-style-position: outside;\n    list-style-type: decimal;\n    margin-bottom: $body-margin-mobile;\n\n    li {\n      display: list-item;\n      margin-bottom: $body-margin-mobile;\n      margin-inline-start: px-to-rem(16px);\n      padding-inline-start: px-to-rem(10px);\n      padding-left: px-to-rem(10px);\n\n      a {\n        @include inline-link($color: $electoral-blue, $hover: $patriotic-red);\n      }\n\n      &:last-of-type {\n        margin-bottom: 0;\n      }\n\n      ol {\n        list-style-type: lower-roman;\n\n        ol {\n          list-style-type: lower-alpha;\n        }\n      }\n\n      & ul,\n      & ol {\n        margin-top: $body-margin-mobile;\n        margin-bottom: 0;\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    margin-bottom: $body-margin-tablet;\n\n    &.list {\n      li {\n        margin-bottom: $body-margin-tablet;\n\n        & ul,\n        & ol {\n          margin-top: $body-margin-tablet;\n          margin-bottom: 0;\n        }\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-bottom: $body-margin-desktop;\n\n    &.list {\n      li {\n        margin-bottom: $body-margin-desktop;\n\n        & ul,\n        & ol {\n          margin-top: $body-margin-desktop;\n          margin-bottom: 0;\n        }\n      }\n    }\n  }\n}\n\n.ally-only {\n  @include is-visually-hidden;\n}\n\n.hidden {\n  display: none;\n}\n", "/**\n  TYPOGRAPHIC\n  global typocraphic constants\n**/\n\n// Defaults\n$font-size-default: 16px;\n$line-height-default: 1.6;\n\n/* Font weights */\n\n$font-weights: (\n  regular: 400,\n  medium: 500,\n  bold: 600,\n  bolder: 700\n);\n\n/* Fonts */\n$rainer-font: 'Rainer Web', sans-serif;\n$obviously-font: 'Obviously', sans-serif;\n$obviously-narrow-font: 'Obviously Narrow', sans-serif;\n$brown-std-font: 'Brown STD', sans-serif;\n$inter-font: 'Inter', sans-serif;\n$renika-font: 'Ren<PERSON>', sans-serif;\n$copy-font: $inter-font;\n$display-font: $rainer-font;\n$display-font-secondary: $inter-font;\n$handwritten-font: $renika-font;\n", "/**\n  PADDING, MARGI<PERSON>, AND SPACING CONSTANTS\n  global padding constants\n**/\n\n$mobile-gutter: 22px;\n$tablet-gutter: 50px;\n$desktop-gutter: 50px;\n$max-text-width: 775px;\n$max-title-width: 1000px;\n$max-wide-width: 1225px;\n$max-xwide-width: 1440px;\n$body-margin-mobile: 18px;\n$body-margin-tablet: 20px;\n$body-margin-desktop: 22px;\n$heading-margin-mobile: 42px;\n$heading-margin-tablet: 44px;\n$heading-margin-desktop: 48px;\n$mobile-nav-height: 105px;\n$mobile-nav-width: 100%;\n$tablet-nav-height: 175px;\n$tablet-nav-width: 204px;\n$desktop-nav-height: 175px;\n$desktop-nav-width: 204px;\n\n// z-index\n$meerkat-z-index: 4;\n$header-z-index: 5;\n$popup-z-index: 10;\n$cookie-z-index: 999999;\n", ".actioncardlist {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 45px;\n  justify-content: center;\n  margin-top: 30px;\n  margin-left: auto;\n  margin-right: auto;\n\n  &__item {\n    width: 325px;\n  }\n\n  @include breakpoint(tablet, min) {\n    gap: 55px;\n    justify-content: center;\n    margin-top: 40px;\n    max-width: 705px;\n  }\n\n  @include breakpoint(desktop, min) {\n    gap: 65px;\n    margin-top: 50px;\n    max-width: 1115px;\n  }\n\n  @include breakpoint(l-desktop, min) {\n    gap: 70px;\n    justify-content: center;\n    margin-top: 60px;\n\n    &:has(> :nth-child(2)) {\n      justify-content: center;\n    }\n  }\n}\n\n.actioncard {\n  position: relative;\n  text-align: left;\n\n  &__title {\n    @include h4;\n  }\n\n  &__desc {\n    @include body;\n  }\n\n  &__title + &__desc {\n    margin-top: 26px;\n  }\n\n  &__fig {\n    background-color: $white;\n    border-top: 6px solid $golden-rod;\n    display: inline-flex;\n    height: 0;\n    justify-content: center;\n    margin-bottom: 30px;\n    overflow: hidden;\n    padding-top: 250px / 475px * 100%;\n    position: relative;\n    transition: border 0.3s ease-in-out;\n    width: 100%;\n  }\n\n  &__img {\n    display: block;\n    filter: grayscale(100%);\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    object-position: center;\n    transition: filter 0.3s ease-in-out;\n  }\n\n  &__link {\n    display: block;\n    margin-top: 26px;\n\n    .emphasizedlink {\n      display: inline-block;\n    }\n\n    &::after {\n      content: '';\n      inset: 0;\n      position: absolute;\n      z-index: 1;\n    }\n  }\n\n  &:hover,\n  &:focus {\n    .actioncard__fig {\n      border-top-color: $patriotic-red;\n    }\n\n    .actioncard__img {\n      filter: grayscale(0%);\n    }\n\n    .emphasizedlink {\n      color: var(--text-hover-color);\n      font-style: italic;\n\n      .emphasizedlink__svg {\n        &--two {\n          transform: translateX(10px);\n        }\n      }\n    }\n  }\n}\n", "/* stylelint-disable */\n.yoast-breadcrumbs {\n  margin-bottom: 11px;\n  font-family: $inter-font;\n  font-size: px-to-rem(16px);\n  font-weight: 800;\n  line-height: 125%;\n\n  .breadcrumb_last {\n    @include is-visually-hidden;\n  }\n\n  ol {\n    margin-bottom: 0;\n  }\n\n  li {\n    display: inline-block;\n\n    &:first-of-type {\n      @include is-visually-hidden;\n    }\n  }\n\n  a {\n    display: inline-block;\n    margin-bottom: 5px;\n    transition: all 0.15s ease-in-out;\n    width: fit-content;\n\n    .breadcrumb-seperator {\n      padding-left: 5px;\n      padding-right: 13px;\n      transition: padding 0.3s ease-in-out;\n\n      &__svg {\n        position: relative;\n        top: 1px;\n        transition: transform 0.3s ease-in-out;\n\n        &--two {\n          margin-left: -10px;\n        }\n      }\n    }\n\n    &:hover,\n    &:focus {\n      font-style: italic;\n\n      .breadcrumb-seperator {\n        &__svg {\n          &--two {\n            transform: translateX(10px);\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(18px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(18px);\n\n    ol {\n      margin-bottom: 0;\n    }\n  }\n}\n/* stylelint-enable */\n", "/**\n  BUTTON\n**/\n\n.button {\n  @include button(\n    $color: var(--bg-color),\n    $hover: var(--bg-hover-color),\n    $text-color: var(--text-color)\n  );\n\n  &--red {\n    @include button(\n      $color: $patriotic-red,\n      $hover: $patriotic-red-dark,\n      $text-color: $white\n    );\n  }\n}\n", "/**\n  CANDIDATECOMPARISON\n**/\n.candidatecomparisonsection {\n  /* margin-top: px-to-rem(-100); */\n  margin-bottom: 0 !important;\n  padding-top: px-to-rem(100);\n  padding-bottom: px-to-rem(100);\n\n  .candidatecomparison {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    margin-left: auto;\n    margin-right: auto;\n    max-width: 1022px;\n    position: relative;\n\n    h3,\n    h6 {\n      color: $white;\n    }\n\n    .accordion-heading {\n      width: 100%;\n      text-align: center;\n      padding-left: 20px;\n      padding-right: 20px;\n    }\n\n    .headshots-wrapper {\n      text-align: center;\n      margin-bottom: px-to-rem(90);\n\n      h6 {\n        margin: 0;\n        display: table-caption;\n        text-align: center;\n\n        @include breakpoint(tablet, min) {\n          display: block;\n        }\n      }\n\n      @include breakpoint(tablet, min) {\n        margin-bottom: px-to-rem(100);\n      }\n\n      .candidate-name-wrapper {\n        display: flex;\n        margin-top: 20px;\n\n        @include breakpoint(tablet, min) {\n          margin-top: 10px;\n        }\n\n        .democratic-candidate-name {\n          width: 50%;\n          padding-left: 16%;\n\n          @include breakpoint(tablet, min) {\n            width: 65%;\n            padding-left: 0%;\n          }\n        }\n\n        .republican-candidate-name {\n          width: 50%;\n          text-align: end;\n          padding-left: 22%;\n\n          @include breakpoint(tablet, min) {\n            width: 35%;\n            text-align: center;\n            padding-left: 0%;\n          }\n        }\n      }\n    }\n\n    .headshots-container {\n      display: flex;\n      max-width: px-to-rem(655);\n      margin-top: px-to-rem(50);\n\n      div {\n        float: left;\n        display: inline-block;\n      }\n\n      .democratic-candidate-headshot {\n        z-index: 9;\n        padding-top: 20px;\n\n        @include breakpoint(tablet, min) {\n          padding-top: 0;\n\n          img {\n            margin-left: px-to-rem(60);\n          }\n        }\n      }\n\n      .republican-candidate-headshot {\n        img {\n          margin-left: px-to-rem(-75);\n          margin-top: px-to-rem(25);\n\n          @include breakpoint(tablet, min) {\n            margin-left: px-to-rem(-90);\n            margin-top: px-to-rem(60);\n          }\n        }\n      }\n\n      .vs-image img {\n        margin-left: px-to-rem(-60);\n        max-height: 200px;\n        max-width: fit-content;\n\n        @include breakpoint(tablet, min) {\n          max-height: 100%;\n        }\n      }\n\n      .candidate-headshot {\n        position: relative;\n\n        img {\n          max-width: 11rem;\n          max-height: 11rem;\n        }\n\n        .hover {\n          display: none;\n        }\n\n        .hover-icon {\n          max-height: 150px;\n          max-width: 150px;\n          opacity: 0;\n          position: absolute;\n          transform: rotate(22deg);\n          transition: all 0.1s ease-in-out;\n          top: -35%;\n          visibility: hidden;\n        }\n\n        &:hover {\n          > img.no-hover {\n            display: none;\n            margin-top: 0;\n          }\n\n          > img.hover {\n            display: inline;\n          }\n\n          .hover-icon {\n            display: block;\n            opacity: 1;\n            visibility: visible;\n          }\n        }\n\n        @include breakpoint(tablet, min) {\n          img {\n            max-width: px-to-rem(237);\n            max-height: px-to-rem(237);\n          }\n\n          .hover-icon {\n            right: -2%;\n            top: -16%;\n          }\n        }\n      }\n\n      &__icon {\n        color: $electoral-blue;\n        display: none;\n        height: 54px;\n        width: 64px;\n      }\n\n      @include breakpoint(tablet, min) {\n        margin-top: px-to-rem(47);\n\n        &__icon {\n          display: block;\n          position: absolute;\n          left: -120px;\n          top: 350px;\n          transform: rotate(7deg);\n        }\n      }\n    }\n\n    .accordion {\n      h3,\n      h6 {\n        text-align: left;\n      }\n\n      h6 {\n        margin-top: 0;\n        margin-bottom: px-to-rem(6);\n      }\n\n      button {\n        padding: 0;\n      }\n\n      &__title,\n      &__button {\n        width: 100%;\n\n        .accordion-item-description {\n          max-width: 75%;\n        }\n\n        svg {\n          float: right;\n\n          .plus {\n            transition: transform 0.3s ease-in;\n            transform-origin: center center;\n            transform: rotate(0deg);\n          }\n        }\n\n        &[aria-expanded='true'] {\n          .plus {\n            transform: rotate(90deg);\n          }\n        }\n      }\n\n      &__wrap {\n        border-top: 1px solid $white;\n        padding-top: 30px;\n        padding-bottom: 30px;\n        position: relative;\n        margin-left: 18px;\n        margin-right: 18px;\n\n        &:last-child {\n          border-bottom: 1px solid $white;\n        }\n\n        .candidatecomparison__icon {\n          display: none;\n        }\n\n        @include breakpoint(tablet, min) {\n          margin-left: 10.5px;\n          margin-right: 10.5px;\n          padding-top: px-to-rem(50);\n          padding-bottom: px-to-rem(50);\n        }\n\n        @include breakpoint(desktop, min) {\n          .candidatecomparison__icon {\n            display: block;\n            position: absolute;\n\n            &--flag {\n              height: 130px;\n              left: -150px;\n              top: 50%;\n              transform: rotate(8deg);\n              width: 130px;\n            }\n\n            &--trash {\n              height: 100px;\n              right: -150px;\n              top: 75%;\n              transform: rotate(-10deg);\n              width: 100px;\n            }\n\n            &--eyes {\n              height: 56px;\n              left: -120px;\n              top: 65%;\n              transform: rotate(-5.5deg);\n              width: 50px;\n            }\n\n            &--finger {\n              height: 70px;\n              right: -150px;\n              top: 65%;\n              transform: rotate(10.35deg);\n              width: 70px;\n            }\n          }\n        }\n      }\n\n      .accordion-item-description {\n        float: left;\n        color: $white;\n        text-align: left;\n      }\n    }\n\n    .candidate-pov {\n      @include breakpoint(tablet, min) {\n        display: flex;\n        column-gap: 90px;\n        clear: both;\n      }\n\n      ul {\n        li {\n          display: flex;\n          margin-bottom: 24px;\n          font-size: px-to-rem(14px);\n\n          @include breakpoint(tablet, min) {\n            font-size: px-to-rem(16px);\n          }\n\n          &::before {\n            margin-right: 12px;\n            vertical-align: middle;\n          }\n        }\n      }\n\n      .democratic-candidate-div {\n        ul li::before {\n          content: url('data:image/svg+xml,<svg width=\"17\" height=\"18\" viewBox=\"0 0 17 18\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><path d=\"M13.9985 1.17188H12.6936V2.47674H11.3742V3.78161V5.10098H10.0694V6.40585V7.72522H8.74999V9.03009V10.3495H7.44512V11.6543V12.9592H6.12576H4.82089V11.6543H3.50153V10.3495H2.19666V11.6543V12.9592H3.50153V14.2786H4.82089V15.5834H6.12576V16.9028H7.44512V15.5834V14.2786H8.74999V12.9592V11.6543H10.0694V10.3495V9.03009H11.3742V7.72522V6.40585H12.6936V5.10098H13.9985V3.78161H15.3033V2.47674V1.17188H13.9985Z\" fill=\"%231200CF\"/></svg>');\n        }\n      }\n\n      .republican-candidate-div {\n        ul li::before {\n          content: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"12\" height=\"13\" viewBox=\"0 0 12 13\" fill=\"none\"><rect y=\"0.789062\" width=\"1.71429\" height=\"1.71429\" fill=\"%23F65137\"/><rect x=\"3.42859\" y=\"4.21875\" width=\"1.71429\" height=\"1.71429\" fill=\"%23F65137\"/><rect x=\"3.42859\" y=\"7.64453\" width=\"1.71429\" height=\"1.71429\" fill=\"%23F65137\"/><rect x=\"10.2857\" y=\"0.789062\" width=\"1.71429\" height=\"1.71429\" fill=\"%23F65137\"/><rect x=\"1.71429\" y=\"9.35938\" width=\"1.71429\" height=\"1.71429\" fill=\"%23F65137\"/><rect x=\"8.57141\" y=\"2.50391\" width=\"1.71429\" height=\"1.71429\" fill=\"%23F65137\"/><rect y=\"11.0742\" width=\"1.71429\" height=\"1.71429\" fill=\"%23F65137\"/><rect x=\"6.85718\" y=\"4.21875\" width=\"1.71429\" height=\"1.71429\" fill=\"%23F65137\"/><rect x=\"6.85718\" y=\"7.64453\" width=\"1.71429\" height=\"1.71429\" fill=\"%23F65137\"/><rect x=\"1.71429\" y=\"2.50391\" width=\"1.71429\" height=\"1.71429\" fill=\"%23F65137\"/><rect x=\"5.14282\" y=\"5.93359\" width=\"1.71429\" height=\"1.71429\" fill=\"%23F65137\"/><rect x=\"8.57141\" y=\"9.35938\" width=\"1.71429\" height=\"1.71429\" fill=\"%23F65137\"/><rect x=\"10.2857\" y=\"11.0742\" width=\"1.71429\" height=\"1.71429\" fill=\"%23F65137\"/></svg>');\n        }\n      }\n\n      .candidate-pov-container {\n        float: left;\n        display: flex;\n        gap: 20px;\n        width: 100%;\n        margin-top: 40px;\n\n        h4 {\n          margin-bottom: px-to-rem(30);\n        }\n\n        .headshot {\n          min-width: 60px !important;\n\n          img {\n            width: 60px;\n            height: 60px;\n          }\n        }\n\n        @include breakpoint(tablet, min) {\n          width: 50%;\n          margin-top: px-to-rem(60);\n\n          .headshot {\n            min-width: 80px !important;\n\n            img {\n              width: 80px;\n              height: 80px;\n            }\n          }\n        }\n      }\n    }\n\n    @include breakpoint(tablet, min) {\n      .candidate-pov-container {\n        width: 100%;\n      }\n    }\n\n    .accordion__panel {\n      height: auto;\n      overflow: hidden;\n      margin-bottom: 0;\n      padding: 0;\n      transition: height 0.3s ease-in-out;\n\n      &[aria-hidden='false'] {\n        overflow: visible;\n        padding-top: px-to-rem(20px);\n      }\n\n      .accordion-item-description {\n        max-width: 768px;\n\n        // margin-top: px-to-rem(35);\n      }\n    }\n  }\n\n  /* \n  &:nth-child(3) {\n    padding-top: px-to-rem(200);\n  } \n  */\n}\n\n/**\n  Misc. Stlying\n**/\n\n.mb0 {\n  margin-bottom: 0 !important;\n}\n\n.candidatecomparison-blue-section {\n  padding-top: 60px;\n  padding-bottom: 60px;\n\n  @include breakpoint(tablet, min) {\n    padding-top: 90px;\n    padding-bottom: 90px;\n  }\n\n  .buttonlist {\n    gap: 20px;\n    margin-bottom: 0 !important;\n    justify-content: center;\n    align-items: center;\n  }\n}\n", ".cdformpart {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: calc(100vh - 93px);\n  padding: 175px $mobile-gutter;\n\n  &.hidden {\n    display: none;\n  }\n\n  > .wp-block-heading {\n    margin-left: auto;\n    margin-right: auto;\n    margin-top: 0;\n    max-width: $max-title-width;\n    text-align: center;\n    text-wrap: balance;\n  }\n\n  > .paragraph {\n    @include featured-text;\n\n    margin-left: auto;\n    margin-right: auto;\n    max-width: $max-text-width;\n    text-align: center;\n    text-wrap: balance;\n  }\n\n  > .wp-block-heading > .wp-block-heading,\n  > .wp-block-heading > .paragraph,\n  > .paragraph > .paragraph,\n  > .paragraph > .wp-block-heading {\n    margin-top: 30px;\n  }\n\n  &__form {\n    margin-left: auto;\n    margin-right: auto;\n    max-width: $max-title-width;\n    margin-top: 20px;\n  }\n\n  &__form-button {\n    margin-top: 35px;\n    max-width: 400px;\n    width: 100%;\n  }\n\n  &__actionnetwork {\n    align-items: flex-end;\n    display: flex;\n    flex-flow: row wrap;\n    grid-gap: 10px 35px;\n    justify-content: center;\n    max-width: $max-title-width;\n\n    &.hidden {\n      display: none;\n    }\n\n    .input-group {\n      &--email,\n      &--first-name,\n      &--last-name,\n      &--phone,\n      &--zip {\n        flex: 1 1 10px;\n        min-width: 240px;\n        max-width: 400px;\n        width: 100%;\n      }\n\n      .input-group__error-msg {\n        &::after {\n          right: -20px;\n        }\n      }\n    }\n  }\n\n  &__loader {\n    @keyframes fliphourglass {\n      0% {\n        transform: rotate(0deg);\n      }\n\n      50% {\n        transform: rotate(180deg);\n      }\n\n      100% {\n        transform: rotate(360deg);\n      }\n    }\n\n    &:not(.hidden) {\n      animation: fliphourglass 2.5s ease-in-out infinite;\n      padding: 30px;\n      max-width: 120px;\n      transform-origin: center;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    padding: 155px $tablet-gutter;\n\n    > .wp-block-heading > .wp-block-heading,\n    > .wp-block-heading > .paragraph,\n    > .paragraph > .paragraph,\n    > .paragraph > .wp-block-heading {\n      margin-top: 40px;\n    }\n  }\n\n  @include breakpoint(nav-desktop, min) {\n    padding-top: 225px;\n\n    &__form {\n      margin-top: 30px;\n    }\n\n    &__form-button {\n      width: fit-content;\n    }\n\n    &__actionnetwork {\n      grid-gap: 10px 35px;\n\n      .input-group {\n        .input-group__error-msg {\n          &::after {\n            right: -20px;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    padding-bottom: 155px;\n\n    > .wp-block-heading > .wp-block-heading,\n    > .wp-block-heading > .paragraph,\n    > .paragraph > .paragraph,\n    > .paragraph > .wp-block-heading {\n      margin-top: 50px;\n    }\n\n    &__actionnetwork {\n      .input-group {\n        &--email,\n        &--first-name,\n        &--last-name,\n        &--phone {\n          min-width: 400px;\n        }\n\n        &--zip {\n          max-width: 240px;\n        }\n      }\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    padding-bottom: 155px;\n\n    > .wp-block-heading > .wp-block-heading,\n    > .wp-block-heading > .paragraph,\n    > .paragraph > .paragraph,\n    > .paragraph > .wp-block-heading {\n      margin-top: 60px;\n    }\n  }\n\n  @include breakpoint(xl-desktop, min) {\n    padding: 120px $desktop-nav-width;\n  }\n}\n", ".cdtargetgroups {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 100vh;\n  padding-bottom: 155px;\n\n  &.hidden {\n    display: none;\n  }\n\n  &__restart-link {\n    @include inline-link($color: var(--text-color), $hover: var(--text-color));\n  }\n\n  &__restart {\n    text-align: center;\n  }\n\n  @include breakpoint(tablet, min) {\n    padding-bottom: 155px;\n  }\n\n  @include breakpoint(desktop, min) {\n    padding-bottom: 155px;\n  }\n\n  @include breakpoint(l-desktop, min) {\n    padding-bottom: 155px;\n  }\n\n  @include breakpoint(xl-desktop, min) {\n    padding-bottom: 120px;\n  }\n}\n\n.cdtargetgroup {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 175px $mobile-gutter 30px $mobile-gutter;\n\n  &.hidden {\n    display: none;\n  }\n\n  > .wp-block-heading {\n    margin-left: auto;\n    margin-right: auto;\n    margin-top: 0;\n    max-width: $max-title-width;\n    text-align: center;\n    text-wrap: balance;\n  }\n\n  > .paragraph {\n    @include featured-text;\n\n    margin-left: auto;\n    margin-right: auto;\n    max-width: $max-text-width;\n    text-align: center;\n    text-wrap: balance;\n  }\n\n  > .wp-block-heading > .wp-block-heading,\n  > .wp-block-heading > .paragraph,\n  > .paragraph > .paragraph,\n  > .paragraph > .wp-block-heading {\n    margin-top: 30px;\n  }\n\n  @include breakpoint(tablet, min) {\n    padding: 225px $tablet-gutter 40px;\n\n    > .wp-block-heading > .wp-block-heading,\n    > .wp-block-heading > .paragraph,\n    > .paragraph > .paragraph,\n    > .paragraph > .wp-block-heading {\n      margin-top: 40px;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    padding-bottom: 50px;\n    padding-top: 225px;\n\n    > .wp-block-heading > .wp-block-heading,\n    > .wp-block-heading > .paragraph,\n    > .paragraph > .paragraph,\n    > .paragraph > .wp-block-heading {\n      margin-top: 40px;\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    padding-bottom: 60px;\n\n    > .wp-block-heading > .wp-block-heading,\n    > .wp-block-heading > .paragraph,\n    > .paragraph > .paragraph,\n    > .paragraph > .wp-block-heading {\n      margin-top: 40px;\n      padding-left: calc($desktop-nav-width - $tablet-gutter);\n      padding-right: calc($desktop-nav-width - $tablet-gutter);\n    }\n  }\n\n  @include breakpoint(xl-desktop, min) {\n    padding-top: 120px;\n  }\n}\n", ".commentary {\n  @include commentary;\n\n  color: var(--text-color);\n  line-height: 1.3;\n  margin-bottom: var(--mobile-margin-bottom);\n  margin-top: var(--mobile-margin-top);\n  padding-left: var(--mobile-padding-left);\n  padding-right: var(--mobile-padding-right);\n  transform: rotate(var(--rotation));\n  transform-origin: left;\n\n  span {\n    display: inline-block;\n    overflow: hidden;\n    white-space: nowrap;\n    width: 0;\n  }\n\n  &[data-visible='true'] {\n    span {\n      animation: typing;\n      animation-duration: 3s;\n      animation-delay: 1.5s;\n      animation-timing-function: steps(30, end);\n      animation-fill-mode: forwards;\n    }\n  }\n\n  @keyframes typing {\n    from {\n      width: 0;\n    }\n\n    to {\n      width: 100%;\n      overflow: visible;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-bottom: var(--desktop-margin-bottom);\n    margin-top: var(--desktop-margin-top);\n    padding-left: var(--desktop-padding-left);\n    padding-right: var(--desktop-padding-right);\n  }\n}\n", "/**\n  DETAIL ITEM\n**/\n\n.detailitem {\n  position: relative;\n\n  &__title {\n    @include h5;\n\n    display: block;\n    margin-bottom: 16px;\n  }\n\n  &__text {\n    @include h6;\n\n    display: block;\n  }\n\n  &__note {\n    @include commentary;\n\n    display: none;\n  }\n\n  @include breakpoint(desktop, min) {\n    &__note {\n      display: block;\n      font-size: 22px;\n      left: unset;\n      max-width: 100px;\n      position: absolute;\n      transform: rotate(-10deg) translate(calc(100% + 15px), -15px)\n        translateZ(10px);\n      right: 0;\n      top: 30px;\n\n      span {\n        background: transparent;\n        display: block;\n        left: 5px;\n        padding: 0;\n        position: relative;\n        top: -40px;\n        transform: scale(1) skew(0deg);\n        transition: transform 0.3s ease-in;\n      }\n\n      svg {\n        left: -30px;\n        top: -35px;\n        position: relative;\n\n        path {\n          animation: dasharrow 1s linear;\n          animation-fill-mode: forwards;\n          stroke-dasharray: 120;\n          stroke-dashoffset: 120;\n        }\n      }\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    &__note {\n      font-size: 30px;\n      transform: rotate(-10deg) translate(calc(100% + 15px), -15px)\n        translateZ(10px);\n      max-width: 175px;\n\n      span {\n        left: 20px;\n        top: -30px;\n      }\n\n      svg {\n        left: -15px;\n        top: -30px;\n      }\n    }\n  }\n\n  @include breakpoint(wide-desktop, min) {\n    &__note {\n      font-size: 35px;\n      max-width: 200px;\n    }\n  }\n}\n\n@keyframes dasharrow {\n  to {\n    stroke-dashoffset: 0;\n  }\n}\n", "/**\n  DETAIL ITEM WRAP\n**/\n\n.detailitemwrap {\n  align-items: flex-start;\n  overflow: visible;\n  position: relative;\n\n  &__image {\n    clip-path: $pixel-tips-mobile;\n    height: 100px;\n    object-fit: cover;\n    width: 100px;\n  }\n\n  &--image {\n    column-gap: 35px;\n    grid-template-columns: 100px 1fr;\n    position: relative;\n\n    .detailitemwrap__circle {\n      position: absolute;\n      left: -20px;\n      stroke-dasharray: 500;\n      stroke-dashoffset: 500;\n      top: -6px;\n      transform: translateZ(10px);\n    }\n\n    &:hover {\n      .detailitemwrap__circle {\n        animation: dasharrow 0.5s ease-in;\n        animation-fill-mode: forwards;\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    &--left {\n      .detailitem__note {\n        left: -5px;\n        right: unset;\n        text-align: right;\n        transform: rotate(10deg) translate(calc(-100% - 15px), -15px)\n          translateZ(10px);\n\n        span {\n          left: -5px;\n        }\n\n        svg {\n          left: 35px;\n          transform: scale(-1, 1);\n        }\n      }\n\n      &.detailitemwrap--image {\n        .detailitem__note {\n          transform: rotate(10deg) translate(calc(-100% - 150px - 15px), -5px)\n            translateZ(10px);\n        }\n      }\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    &--left {\n      .detailitem__note {\n        transform: rotate(10deg) translate(-100%, -15px) translateZ(10px);\n\n        span {\n          left: -25px;\n        }\n\n        svg {\n          left: 25px;\n        }\n      }\n\n      &.detailitemwrap--image {\n        .detailitem__note {\n          transform: rotate(10deg) translate(calc(-100% - 150px - 15px), -5px)\n            translateZ(10px);\n        }\n      }\n    }\n  }\n\n  @include breakpoint(wide-desktop, min) {\n    &--left {\n      .detailitem__note {\n        transform: rotate(10deg) translate(calc(-100% - 15px), -15px)\n          translateZ(10px);\n\n        span {\n          left: -20px;\n        }\n      }\n\n      &.detailitemwrap--image {\n        .detailitem__note {\n          transform: rotate(10deg) translate(calc(-100% - 15px - 150px), -5px)\n            translateZ(10px);\n        }\n      }\n    }\n  }\n}\n\n@keyframes dasharrow {\n  to {\n    stroke-dashoffset: 0;\n  }\n}\n", "/**\n  DETAIL LIST\n**/\n\n.detaillist {\n  .detailitemwrap {\n    display: inline-flex;\n    margin-bottom: 25px;\n    transform: translate(0, 0);\n    width: 100%;\n\n    p {\n      margin-bottom: 0;\n    }\n  }\n\n  @include breakpoint(l-mobile, min) {\n    columns: 2;\n\n    .detailitemwrap {\n      margin-bottom: 30px;\n\n      p {\n        margin-bottom: 0;\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    .detailitemwrap {\n      margin-bottom: 35px;\n    }\n  }\n}\n", ".donategoaltracker {\n  display: block;\n\n  $self: &;\n\n  &__top {\n    align-items: flex-end;\n    display: flex;\n    justify-content: space-between;\n  }\n\n  &__raised {\n    color: $patriotic-red;\n    margin-top: 0;\n  }\n\n  &__goal {\n    color: $crt-black;\n    margin-top: 0;\n  }\n\n  &__bar {\n    border: 3px solid $crt-black;\n    height: 27px;\n    margin: 5px 0;\n    background-color: $white;\n    border-radius: 15px;\n    overflow: hidden;\n  }\n\n  &__progresswrap {\n    display: block;\n    height: 27px;\n  }\n\n  &__progress {\n    background-color: $patriotic-red;\n    display: block;\n    height: 27px;\n    width: 0;\n\n    @keyframes progressbar {\n      0% {\n        width: 0;\n      }\n\n      100% {\n        width: 100%;\n      }\n    }\n  }\n\n  &__bottom {\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n  }\n\n  &__lastUpdatedAt {\n    color: $crt-black;\n    font-size: px-to-rem(12px);\n    margin-top: 0;\n\n    svg {\n      height: 1rem;\n      vertical-align: bottom;\n      background-color: $white;\n      width: 1rem;\n    }\n  }\n\n  &--reached {\n    #{$self}__progress {\n      background-color: $electoral-blue;\n    }\n\n    #{$self}__raised {\n      color: $electoral-blue;\n    }\n  }\n\n  &[data-visible='true'],\n  &[data-has-animated='true'] {\n    #{$self}__progress {\n      animation: progressbar 3s ease-in-out;\n      animation-fill-mode: both;\n    }\n  }\n}\n", ".donationcta {\n  background-color: var(--bg-color);\n  color: var(--text-color);\n  margin-bottom: 50px;\n  margin-top: 50px;\n  position: relative;\n\n  .search & {\n    margin-left: 25px;\n    margin-right: 25px;\n  }\n\n  &--noimage {\n    padding: 33px $mobile-gutter;\n  }\n\n  &__content {\n    display: flex;\n    flex-direction: column;\n    padding: 33px $mobile-gutter 0;\n  }\n\n  h1,\n  h2,\n  h3,\n  h4,\n  h5,\n  h6 {\n    @include community-stats;\n\n    margin-bottom: px-to-rem(30px);\n    order: 1;\n\n    .is-style-heading-1 {\n      @include h1;\n    }\n\n    .is-style-heading-2 {\n      @include h2;\n    }\n\n    .is-style-heading-3 {\n      @include h3;\n\n      text-transform: none;\n    }\n\n    .is-style-heading-4 {\n      @include h4;\n\n      text-transform: none;\n    }\n\n    .is-style-heading-5 {\n      @include h5;\n\n      text-transform: none;\n    }\n\n    .is-style-heading-6 {\n      @include h6;\n    }\n  }\n\n  p {\n    @include h4;\n\n    margin-bottom: px-to-rem(30px);\n    order: 1;\n  }\n\n  &__asks {\n    background-color: $white;\n    clip-path: $pixel-tips-mobile;\n    display: grid;\n    grid-template-columns: calc(50% - 5px) calc(50% - 5px);\n    grid-gap: 10px;\n    order: 3;\n    padding: 20px;\n  }\n\n  &__disclaimer {\n    @include caption;\n\n    color: $crt-black;\n    font-style: italic;\n    grid-column: 1 / span 2;\n    margin-top: 5px;\n  }\n\n  &__button {\n    display: block;\n  }\n\n  &__image {\n    display: block;\n    margin-top: -30px;\n    max-height: 500px;\n    object-fit: cover;\n    object-position: top;\n    position: relative;\n    width: 100%;\n    z-index: 1;\n  }\n\n  .pixel-icon {\n    color: $golden-rod;\n    position: absolute;\n    transform: translate(var(--tx), var(--ty)) rotate(var(--r));\n    will-change: transform;\n    z-index: 2;\n\n    &--checkmark {\n      height: 44px;\n      top: 40px;\n      right: -15px;\n      width: 53px;\n    }\n\n    &--bullhorn {\n      left: -15px;\n      bottom: 40px;\n      height: 62px;\n      width: 62px;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    display: grid;\n    grid-template-columns: 2fr 1fr;\n    margin-bottom: 85px;\n    margin-top: 85px;\n\n    .search & {\n      margin-left: 25px;\n      margin-right: 25px;\n    }\n\n    &__asks {\n      clip-path: $pixel-tips-top-mobile;\n      margin-bottom: -1px;\n      padding: 24px;\n    }\n\n    &__content {\n      display: flex;\n      flex-direction: column;\n      padding: $tablet-gutter 0 0 70px;\n      position: relative;\n      z-index: 1;\n    }\n\n    &__image {\n      margin-bottom: 0;\n      margin-left: -12%;\n      margin-top: auto;\n      min-width: 112%;\n      z-index: 0;\n    }\n\n    &__disclaimer {\n      margin-top: 10px;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    grid-template-columns: 65% 35%;\n    padding: 70px 0 0 80px;\n    margin: 100px auto;\n\n    &__content {\n      padding: 0;\n    }\n\n    &__asks {\n      grid-template-columns: calc(33% - 7px) calc(33% - 7px) calc(33% - 7px);\n      margin-top: 10px;\n      padding: 33px;\n    }\n\n    &__disclaimer {\n      grid-column: 1 / span 3;\n      margin-top: 15px;\n    }\n\n    .pixel-icon {\n      &--checkmark {\n        height: 75px;\n        right: 13%;\n        top: -3%;\n        width: 62px;\n      }\n\n      &--bullhorn {\n        height: 90px;\n        left: unset;\n        right: 20%;\n        top: 12%;\n        width: 90px;\n      }\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    grid-template-columns: 60% 40%;\n    padding: 70px 0 0 115px;\n    max-width: 1215px;\n\n    .search & {\n      margin-left: auto;\n      margin-right: auto;\n    }\n  }\n\n  @media (prefers-reduced-motion) {\n    .pixel-icon {\n      transform: none;\n    }\n  }\n}\n", ".donationsection {\n  margin-bottom: 75px;\n  margin-top: 75px;\n\n  > p:first-of-type,\n  .detaillist {\n    margin-top: 30px;\n  }\n\n  .detaillist {\n    margin-bottom: -25px;\n  }\n\n  .sectiontitle {\n    margin-bottom: 30px;\n  }\n\n  .donategoaltracker__link-override {\n    position: relative;\n\n    &::after {\n      content: ' ';\n      position: absolute;\n      inset: 0;\n      z-index: 2;\n    }\n  }\n\n  @include breakpoint(l-mobile, min) {\n    .detaillist {\n      margin-bottom: -30px;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    margin-bottom: 85px;\n    margin-top: 85px;\n    padding: 0 60px;\n\n    > p:first-of-type,\n    .detaillist {\n      margin-top: 40px;\n    }\n\n    .detaillist {\n      margin-bottom: -35px;\n    }\n\n    .sectiontitle {\n      margin-bottom: 40px;\n      margin-left: -60px;\n      width: calc(100% + 120px);\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-top: 100px;\n    margin-bottom: 100px;\n    padding: 0 80px;\n\n    > p:first-of-type,\n    .detaillist {\n      margin-top: 50px;\n    }\n\n    .sectiontitle {\n      margin-bottom: 50px;\n      margin-left: -80px;\n      width: calc(100% + 160px);\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    padding: 0 110px;\n\n    > p:first-of-type,\n    .detaillist {\n      margin-top: 60px;\n    }\n\n    .sectiontitle {\n      margin-bottom: 60px;\n      margin-left: -110px;\n      width: calc(100% + 220px);\n    }\n  }\n}\n", ".emphasizedlink {\n  color: var(--text-color);\n  display: inline-block;\n  font-family: $inter-font;\n  font-size: px-to-rem(16px);\n  font-weight: 800;\n  line-height: 140%;\n  padding: 0;\n  position: relative;\n  transition: color 0.3s ease-in-out;\n\n  $self: &;\n\n  span {\n    display: inline-block;\n    padding-right: 25px;\n  }\n\n  &__svg {\n    position: absolute;\n    right: 5px;\n    top: 5px;\n    transition: transform 0.3s ease-in-out;\n  }\n\n  &:hover,\n  &:focus {\n    color: var(--text-hover-color);\n    font-style: italic;\n\n    #{$self}__svg {\n      &--two {\n        transform: translateX(10px);\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    font-size: px-to-rem(18px);\n  }\n\n  @include breakpoint(desktop, min) {\n    font-size: px-to-rem(18px);\n  }\n}\n", ".emphasizedlinklist {\n  column-count: var(--mobile-column-count);\n  grid-gap: 20px 30px;\n  display: grid;\n  grid-template-columns: repeat(\n    var(--mobile-column-count),\n    minmax(min-content, max-content)\n  );\n  margin-bottom: 40px;\n  margin-top: 40px;\n\n  @include breakpoint(tablet, min) {\n    grid-column-gap: 45px;\n    grid-template-columns: repeat(\n      var(--desktop-column-count),\n      minmax(min-content, max-content)\n    );\n    margin-bottom: 50px;\n    margin-top: 50px;\n  }\n\n  @include breakpoint(desktop, min) {\n    grid-column-gap: 60px;\n    margin-bottom: 60px;\n    margin-top: 60px;\n  }\n}\n", ".faq-child {\n  border-top-width: 1px;\n  border-top-style: solid;\n\n  $self: &;\n\n  &__accordionpanel {\n    height: auto;\n    overflow: hidden;\n    margin-bottom: 0;\n    padding: 0;\n    transition: height 0.3s linear;\n\n    &[aria-hidden='false'] {\n      overflow: visible;\n      padding-top: px-to-rem(30px);\n      padding-bottom: 30px;\n\n      & > * {\n        opacity: 1;\n      }\n    }\n\n    & > * {\n      transition: opacity 0s linear;\n      transition-delay: 0.1s;\n      opacity: 0;\n    }\n\n    > *:first-child {\n      margin-top: 0;\n    }\n\n    > *:last-child {\n      margin-bottom: 0;\n    }\n  }\n\n  &__accordionbutton {\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    justify-content: space-between;\n    padding: 30px 0;\n    text-align: left;\n    width: 100%;\n\n    svg {\n      color: $electoral-blue;\n      height: 16px;\n      margin-left: 40px;\n      min-height: 16px;\n      min-width: 16px;\n      width: 16px;\n\n      .vertical {\n        transform-origin: center;\n        transition: transform ease-in-out 0.3s;\n      }\n    }\n\n    &[aria-expanded='true'] {\n      padding-bottom: 0;\n\n      svg {\n        .vertical {\n          transform: rotate(90deg);\n        }\n      }\n    }\n  }\n\n  &--large {\n    #{$self} {\n      &__accordionbutton {\n        padding: 40px 0;\n\n        &[aria-expanded='true'] {\n          padding-bottom: 0;\n        }\n      }\n\n      &__accordionpanel {\n        &[aria-hidden='false'] {\n          padding-bottom: px-to-rem(40px);\n          padding-top: px-to-rem(40px);\n        }\n      }\n    }\n  }\n\n  &.is-style-heading-1 {\n    .accordion {\n      &__title {\n        @include h1;\n      }\n    }\n  }\n\n  &.is-style-heading-2 {\n    .accordion {\n      &__title {\n        @include h2;\n      }\n    }\n  }\n\n  &.is-style-heading-3 {\n    .accordion {\n      &__title {\n        @include h3;\n\n        text-transform: none;\n      }\n    }\n  }\n\n  &.is-style-heading-4 {\n    .accordion {\n      &__title {\n        @include h4;\n\n        text-transform: none;\n      }\n    }\n  }\n\n  &.is-style-heading-5 {\n    .accordion {\n      &__title {\n        @include h5;\n\n        text-transform: none;\n      }\n    }\n  }\n\n  &.is-style-heading-6 {\n    .accordion {\n      &__title {\n        @include h6;\n      }\n    }\n  }\n\n  &.is-style-body {\n    .accordion {\n      &__title {\n        @include body;\n      }\n    }\n  }\n\n  &.is-style-body-large {\n    .accordion {\n      &__title {\n        @include bodylarge;\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    &__accordionbutton {\n      padding: 30px 0;\n\n      svg {\n        height: 20px;\n        min-height: 20px;\n        min-width: 20px;\n        width: 20px;\n      }\n\n      &[aria-expanded='true'] {\n        padding-bottom: 0;\n      }\n    }\n\n    &__accordionpanel {\n      &[aria-hidden='false'] {\n        padding-bottom: 30px;\n      }\n\n      > *:first-child {\n        margin-top: 0;\n      }\n\n      > *:last-child {\n        margin-bottom: 0;\n      }\n    }\n\n    &--large {\n      #{$self} {\n        &__accordionbutton {\n          padding: 40px 0;\n\n          &[aria-expanded='true'] {\n            padding-bottom: 0;\n          }\n        }\n\n        &__accordionpanel {\n          &[aria-hidden='false'] {\n            padding-bottom: px-to-rem(40px);\n            padding-top: px-to-rem(40px);\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    &__accordionbutton {\n      padding: 40px 0;\n\n      &[aria-expanded='true'] {\n        padding-bottom: 0;\n      }\n    }\n\n    &__accordionpanel {\n      > *:first-child {\n        margin-top: 0;\n      }\n\n      > *:last-child {\n        margin-bottom: 0;\n      }\n\n      &[aria-hidden='false'] {\n        padding-bottom: 40px;\n      }\n    }\n\n    &--large {\n      #{$self} {\n        &__accordionbutton {\n          padding-bottom: 50px;\n          padding-top: 50px;\n\n          &[aria-expanded='true'] {\n            padding-bottom: 0;\n          }\n\n          svg {\n            height: 24px;\n            min-height: 24px;\n            min-width: 24px;\n            width: 24px;\n          }\n        }\n\n        &__accordionpanel {\n          &[aria-hidden='false'] {\n            padding-top: px-to-rem(50px);\n            padding-bottom: 50px;\n          }\n        }\n      }\n    }\n  }\n}\n", ".faqs {\n  margin-top: 40px;\n\n  > *:last-child {\n    border-bottom-width: 1px;\n    border-bottom-style: solid;\n  }\n\n  @include breakpoint(tablet, min) {\n    margin-top: 50px;\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-top: 60px;\n  }\n}\n", ".featuredcountdown {\n  background-color: var(--background-color);\n  color: var(--text-color);\n  margin-bottom: 45px;\n  overflow: hidden;\n  margin-top: 45px;\n  padding: 65px $mobile-gutter;\n\n  p {\n    @include h1;\n\n    text-align: center;\n\n    &:last-of-type {\n      margin-bottom: 0;\n    }\n  }\n\n  &__text {\n    margin: 0 auto;\n    max-width: calc($max-wide-width + 100px);\n    position: relative;\n  }\n\n  &__before {\n    display: block;\n\n    &.hidden {\n      display: none;\n    }\n  }\n\n  &__today {\n    display: block;\n\n    &.hidden {\n      display: none;\n    }\n  }\n\n  &__date {\n    display: block;\n\n    &.hidden {\n      display: none;\n    }\n  }\n\n  &__icon {\n    color: var(--icon-color);\n    position: absolute;\n    transform: translate(var(--tx), var(--ty)) rotate(var(--r));\n\n    &--email {\n      top: -20px;\n      height: 32px;\n      left: calc(50% - 25px);\n      width: 49px;\n    }\n\n    &--eyes {\n      height: 110px;\n      left: 0%;\n      bottom: -5%;\n      width: 110px;\n    }\n\n    &--finger {\n      height: 120px;\n      right: -20px;\n      bottom: max(10px, 25%);\n      width: 120px;\n    }\n\n    &--youdecide {\n      top: max(25px, 25%);\n      height: 56px;\n      right: 0;\n      width: 80px;\n    }\n\n    &--congress {\n      height: 71px;\n      left: 0%;\n      top: max(10%, 25px);\n      width: 74px;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    margin-bottom: 65px;\n    margin-top: 65px;\n    padding: 80px $tablet-gutter;\n\n    &__icon {\n      &--youdecide {\n        bottom: 5px;\n        height: 56px;\n        right: 0;\n        width: 80px;\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-bottom: 90px;\n    margin-top: 90px;\n    padding: 100px $desktop-gutter;\n\n    &__icon {\n      &--email {\n        top: -25px;\n        height: 50px;\n        left: unset;\n        right: 50%;\n        width: 75px;\n      }\n\n      &--eyes {\n        height: 170px;\n        left: 50%;\n        bottom: -5%;\n        width: 170px;\n      }\n\n      &--finger {\n        height: 160px;\n        left: 10%;\n        right: unset;\n        top: unset;\n        bottom: -45px;\n        width: 160px;\n      }\n\n      &--youdecide {\n        top: 10%;\n        height: 88px;\n        right: 5%;\n        width: 115px;\n      }\n\n      &--congress {\n        height: 110px;\n        left: -10px;\n        top: 40%;\n        width: 115px;\n      }\n    }\n  }\n}\n", ".featuredtext {\n  background-color: var(--background-color);\n  color: var(--text-color);\n  margin-bottom: 45px;\n  overflow: hidden;\n  margin-top: 45px;\n  padding: 65px $mobile-gutter;\n\n  p {\n    @include h1;\n\n    text-align: center;\n\n    &:last-of-type {\n      margin-bottom: 0;\n    }\n  }\n\n  &__text {\n    margin: 0 auto;\n    max-width: calc($max-wide-width + 100px);\n    position: relative;\n  }\n\n  &__icon {\n    color: var(--icon-color);\n    position: absolute;\n    transform: translate(var(--tx), var(--ty)) rotate(var(--r));\n\n    &--email {\n      bottom: 15px;\n      height: 32px;\n      left: 0;\n      width: 49px;\n    }\n\n    &--eyes {\n      height: 110px;\n      left: calc(50% - 110px);\n      top: 45px;\n      width: 110px;\n    }\n\n    &--finger {\n      height: 120px;\n      right: -30px;\n      top: -60px;\n      width: 120px;\n    }\n\n    &--youdecide {\n      bottom: 25px;\n      height: 56px;\n      right: 0;\n      width: 80px;\n    }\n\n    &--whitehouse {\n      height: 71px;\n      left: -16px;\n      top: -16px;\n      width: 74px;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    margin-bottom: 65px;\n    margin-top: 65px;\n    padding: 80px $tablet-gutter;\n\n    &__icon {\n      &--youdecide {\n        bottom: 5px;\n        height: 56px;\n        right: 0;\n        width: 80px;\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-bottom: 90px;\n    margin-top: 90px;\n    padding: 100px $desktop-gutter;\n\n    &__icon {\n      &--email {\n        bottom: 15px;\n        height: 50px;\n        left: unset;\n        right: 0;\n        width: 75px;\n      }\n\n      &--eyes {\n        height: 170px;\n        left: 50%;\n        top: 60px;\n        width: 170px;\n      }\n\n      &--finger {\n        height: 160px;\n        left: calc(50% - 180px);\n        right: unset;\n        top: unset;\n        bottom: -85px;\n        width: 160px;\n      }\n\n      &--youdecide {\n        bottom: 115px;\n        height: 88px;\n        right: 100px;\n        width: 115px;\n      }\n\n      &--whitehouse {\n        height: 110px;\n        left: 15%;\n        top: -16px;\n        width: 115px;\n      }\n    }\n  }\n}\n", ".findyourteam {\n  background-color: var(--bg-color);\n  color: var(--heading-color);\n  overflow-x: clip;\n  overflow-y: visible;\n  padding: 70px $mobile-gutter 55px;\n  position: relative;\n\n  $self: &;\n\n  &__teamicons {\n    align-items: center;\n    color: var(--heading-color);\n    display: flex;\n    flex-flow: row wrap;\n    justify-content: center;\n    margin-bottom: 0;\n  }\n\n  &__stateselector {\n    margin-bottom: 10px;\n  }\n\n  &__teamicon {\n    height: auto;\n    transform: scale(1);\n    transition: opacity 0.6s ease-in-out 0.2s, transform 0.6s ease-in-out 0.2s;\n    width: px-to-rem(100px);\n\n    &--selected {\n      transform: scale(1.05);\n    }\n\n    &--notselected {\n      opacity: 0.5;\n      transform: scale(0.95);\n    }\n  }\n\n  &__icon {\n    height: 40px;\n    position: absolute;\n    transform: translate(var(--tx), var(--ty)) rotate(var(--r))\n      scale(var(--scale));\n    width: 40px;\n  }\n\n  &__vs {\n    @include h5;\n\n    margin-left: 20px;\n    margin-right: 20px;\n    transition: opacity 0.3s ease-in-out;\n\n    &--selected {\n      opacity: 0.5;\n    }\n  }\n\n  &__heading {\n    @include h1;\n\n    color: var(--heading-color);\n    margin-top: 40px;\n    text-align: center;\n\n    &--where {\n      margin-bottom: 40px;\n    }\n\n    &--state {\n      margin-bottom: 20px;\n      margin-left: auto;\n      margin-right: auto;\n      max-width: 1200px;\n    }\n  }\n\n  &__restart-link {\n    @include inline-link($color: $white, $hover: $white);\n  }\n\n  &__restart {\n    margin-bottom: 50px;\n    position: relative;\n    text-align: center;\n    z-index: 3;\n  }\n\n  &__content {\n    overflow: hidden;\n  }\n\n  &__part {\n    display: grid;\n    grid-template-rows: 0fr;\n    opacity: 0;\n    visibility: hidden;\n\n    &.show {\n      grid-template-rows: 1fr;\n      opacity: 1;\n      visibility: visible;\n\n      #{$self} {\n        &__content {\n          overflow: visible;\n        }\n      }\n    }\n\n    &--part1 {\n      align-items: center;\n      flex-flow: column;\n      justify-content: center;\n      margin-left: auto;\n      margin-right: auto;\n      max-width: $max-title-width;\n      transition: visibility 0s ease 0s, opacity 0.3s ease-in-out 0.1s,\n        grid-template-rows 0s ease-in-out 0s;\n\n      &.show {\n        display: flex;\n      }\n\n      #{$self} {\n        &__icon {\n          &:nth-child(1) {\n            color: $golden-rod;\n            left: -10px;\n            top: 130px;\n          }\n\n          &:nth-child(2) {\n            color: $team-green;\n            right: 5px;\n            bottom: 130px;\n          }\n\n          &:nth-child(3),\n          &:nth-child(4),\n          &:nth-child(5),\n          &:nth-child(6) {\n            display: none;\n          }\n        }\n      }\n    }\n\n    &--part2 {\n      transition: visibility 0s ease 0s, opacity 0.15s ease-in-out 0.1s,\n        grid-template-rows 0.3s ease-in-out 0.1s;\n\n      #{$self} {\n        &__teamoverview {\n          display: none;\n          margin: 0 auto;\n          max-width: $max-text-width;\n          text-align: center;\n\n          &--featuredactions {\n            margin-top: px-to-rem(50px);\n            max-width: calc($max-title-width + 100px);\n          }\n\n          &.show {\n            display: block;\n          }\n\n          p {\n            &:last-child {\n              margin-bottom: 0;\n            }\n          }\n        }\n\n        &__subheading {\n          @include h4;\n\n          + p {\n            margin-top: 30px;\n          }\n        }\n\n        &__featuredactions {\n          align-items: center;\n          display: grid;\n          grid-template-columns: repeat(1, 1fr);\n          grid-gap: 30px;\n          justify-content: center;\n        }\n\n        &__featuredaction {\n          height: 100%;\n        }\n\n        &__actionlink {\n          align-items: center;\n          border: 10px solid $golden-rod;\n          display: flex;\n          flex-direction: column;\n          height: 100%;\n          justify-content: center;\n          padding: 28px 16px;\n\n          &:hover,\n          &:focus {\n            #{$self} {\n              &__actionbutton {\n                background-color: $patriotic-red-dark;\n                border-color: $patriotic-red-dark;\n                color: $white;\n                font-style: italic;\n              }\n            }\n          }\n        }\n\n        &__actiontitle {\n          @include h4;\n\n          display: block;\n          margin-bottom: 40px;\n        }\n\n        &__actionbutton {\n          @include button(\n            $color: $patriotic-red,\n            $hover: $patriotic-red-dark,\n            $text-color: $white\n          );\n        }\n\n        &__icon {\n          color: var(--selected-team-color);\n\n          &:nth-child(1) {\n            left: -10px;\n            top: 30px;\n          }\n\n          &:nth-child(2) {\n            right: 5px;\n            top: 40%;\n          }\n\n          &:nth-child(3) {\n            left: 10px;\n            bottom: 33%;\n          }\n\n          &:nth-child(4) {\n            right: -20px;\n            bottom: calc(30% - 40px);\n          }\n\n          &:nth-child(5) {\n            left: 40%;\n            bottom: 0;\n          }\n\n          &:nth-child(6) {\n            display: none;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(mobile, max) {\n    &__heading {\n      font-size: min(24vmin, 105px);\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    padding: 90px $tablet-gutter 75px;\n\n    &__stateselector {\n      margin-bottom: 15px;\n    }\n\n    &__restart {\n      margin-bottom: 70px;\n    }\n\n    &__teamicons {\n      margin-bottom: 0;\n    }\n\n    &__teamicon {\n      height: auto;\n      width: px-to-rem(170px);\n    }\n\n    &__icon {\n      height: 60px;\n      width: 60px;\n    }\n\n    &__vs {\n      font-size: px-to-rem(32px);\n      margin-left: 40px;\n      margin-right: 40px;\n    }\n\n    &__heading {\n      font-size: px-to-rem(118px);\n      margin-top: 50px;\n\n      &--where {\n        margin-bottom: 60px;\n      }\n\n      &--state {\n        margin-bottom: 25px;\n      }\n    }\n\n    &__part {\n      &--part2 {\n        #{$self} {\n          &__subheading {\n            + p {\n              margin-top: 40px;\n            }\n          }\n\n          &__teamoverview {\n            &--featuredactions {\n              margin-top: px-to-rem(60px);\n            }\n          }\n\n          &__featuredactions {\n            grid-template-columns: repeat(2, 1fr);\n            grid-gap: 40px;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    padding: 120px $desktop-gutter 100px;\n\n    &__stateselector {\n      margin-bottom: 15px;\n    }\n\n    &__restart {\n      margin-bottom: 90px;\n    }\n\n    &__teamicons {\n      margin-bottom: 0;\n    }\n\n    &__teamicon {\n      height: auto;\n      width: px-to-rem(270px);\n    }\n\n    &__icon {\n      height: 82px;\n      width: 82px;\n    }\n\n    &__part {\n      &--part1 {\n        #{$self} {\n          &__icon {\n            &:nth-child(1) {\n              left: 74px;\n              top: 32%;\n            }\n\n            &:nth-child(2) {\n              bottom: 40%;\n              right: 70px;\n            }\n\n            &:nth-child(3) {\n              color: $golden-rod;\n              display: block;\n              right: -20px;\n              top: 78px;\n            }\n\n            &:nth-child(4) {\n              bottom: 130px;\n              color: $team-green;\n              display: block;\n              left: -20px;\n            }\n\n            &:nth-child(5) {\n              bottom: -30px;\n              color: $golden-rod;\n              display: block;\n              left: 20%;\n            }\n\n            &:nth-child(6) {\n              bottom: 55px;\n              color: $golden-rod;\n              display: block;\n              right: 0;\n            }\n          }\n        }\n      }\n\n      &--part2 {\n        #{$self} {\n          &__subheading {\n            + p {\n              margin-top: 50px;\n            }\n          }\n\n          &__teamoverview {\n            &--featuredactions {\n              margin-top: px-to-rem(70px);\n            }\n          }\n\n          &__featuredactions {\n            grid-template-columns: repeat(3, 1fr);\n            grid-gap: 50px;\n\n            &--1 {\n              grid-template-columns: repeat(1, 1fr);\n            }\n\n            &--2 {\n              grid-template-columns: repeat(2, 1fr);\n            }\n          }\n\n          &__actionlink {\n            border-width: 12px;\n          }\n\n          &__icon {\n            color: var(--selected-team-color);\n\n            &:nth-child(1) {\n              left: unset;\n              right: -10px;\n              top: 16%;\n            }\n\n            &:nth-child(2) {\n              left: 70px;\n              right: unset;\n              top: 45%;\n            }\n\n            &:nth-child(3) {\n              bottom: 20%;\n              left: -20px;\n            }\n\n            &:nth-child(4) {\n              bottom: 40%;\n              right: 70px;\n            }\n\n            &:nth-child(5) {\n              bottom: 0;\n              left: unset;\n              right: 10px;\n            }\n          }\n        }\n      }\n    }\n\n    &__vs {\n      font-size: px-to-rem(46px);\n      margin-left: 60px;\n      margin-right: 60px;\n    }\n\n    &__heading {\n      font-size: px-to-rem(154px);\n      margin-top: 100px;\n\n      &--where {\n        margin-bottom: 70px;\n      }\n\n      &--state {\n        margin-bottom: 30px;\n      }\n    }\n  }\n\n  @include breakpoint(xl-desktop, min) {\n    &__heading {\n      font-size: px-to-rem(188px);\n    }\n  }\n\n  .limitedfeaturedactions,\n  .allfeaturedactions {\n    display: none;\n\n    &.hide {\n      display: none;\n    }\n\n    &.show {\n      display: grid;\n    }\n  }\n}\n", ".formsection {\n  background-color: var(--bg-color);\n  color: var(--text-color);\n  padding: px-to-rem(40px) $mobile-gutter;\n\n  $self: &;\n\n  &__max {\n    display: grid;\n    grid-template-columns: 100%;\n    margin-left: auto;\n    margin-right: auto;\n    max-width: px-to-rem($max-title-width);\n  }\n\n  &__text {\n    .paragraph:last-child {\n      margin-bottom: 0;\n    }\n  }\n\n  &__form {\n    .form__error-msg {\n      margin-bottom: 0;\n      margin-top: 16px;\n    }\n  }\n\n  &__post-submit:not(.hidden) {\n    margin-top: px-to-rem(30px);\n  }\n\n  &__form-button {\n    margin-top: px-to-rem(50px);\n  }\n\n  &--left {\n    #{$self} {\n      &__form {\n        grid-row: 1;\n      }\n\n      &__text {\n        grid-row: 2;\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    padding: 60px $tablet-gutter;\n\n    &__text {\n      .paragraph:last-child {\n        margin-bottom: 0;\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    padding: 85px $desktop-gutter;\n\n    &__max {\n      grid-template-columns: calc(100% - 440px) 380px;\n      grid-column-gap: 60px;\n    }\n\n    &__text {\n      .paragraph:last-child {\n        margin-bottom: 0;\n      }\n    }\n\n    &__form {\n      .form__error-msg {\n        &::after {\n          background-size: 48px 33px;\n          height: 33px;\n          right: 0;\n          width: 48px;\n        }\n      }\n    }\n\n    &--left {\n      #{$self} {\n        &__form {\n          grid-row: 1;\n          grid-column: 1;\n        }\n\n        &__text {\n          grid-row: 1;\n          grid-column: 2;\n        }\n\n        &__max {\n          grid-template-columns: 380px calc(100% - 440px);\n        }\n      }\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    &__max {\n      grid-template-columns: calc(100% - 470px) 380px;\n      grid-column-gap: 90px;\n    }\n\n    &__form {\n      .form__error-msg {\n        &::after {\n          background-size: 85px 53px;\n          height: 53px;\n          right: -85px;\n          width: 85px;\n        }\n      }\n    }\n\n    &--left {\n      #{$self} {\n        &__max {\n          grid-template-columns: 380px calc(100% - 470px);\n        }\n      }\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    &__max {\n      grid-template-columns: calc(100% - 500px) 380px;\n      grid-column-gap: 120px;\n\n      &--left {\n        #{$self} {\n          &__max {\n            grid-template-columns: 380px calc(100% - 500px);\n          }\n        }\n      }\n    }\n  }\n}\n", ".wp-block-gallery {\n  margin-bottom: 20px;\n  margin-top: 20px;\n\n  &.has-nested-images {\n    &.is-style-downloadables {\n      gap: 20px;\n\n      figcaption.wp-element-caption.blocks-gallery-caption {\n        @include caption;\n\n        background: transparent;\n        color: $crt-black;\n        flex-basis: fit-content;\n        flex-grow: 0;\n        font-style: normal;\n        padding: 0;\n        position: relative;\n        margin-top: 20px;\n        text-align: left;\n\n        a {\n          @include inline-link;\n\n          text-decoration: none;\n        }\n      }\n\n      figure.wp-block-image {\n        border: 1px solid $grey;\n        padding: 10px;\n\n        &:not(#individual-image) {\n          width: 100%;\n        }\n\n        img {\n          width: 100%;\n        }\n\n        figcaption.wp-element-caption {\n          @include caption;\n\n          background: transparent;\n          color: $crt-black;\n          flex-basis: fit-content;\n          flex-grow: 0;\n          font-style: normal;\n          padding: 0;\n          position: relative;\n          margin-top: 20px;\n          text-align: left;\n\n          a {\n            @include inline-link;\n\n            text-decoration: none;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(m-mobile, min) {\n    &.columns-2,\n    &.columns-3 {\n      &.has-nested-images {\n        &.is-style-downloadables {\n          figure.wp-block-image {\n            &:not(#individual-image) {\n              max-width: calc(50% - 10px);\n              width: calc(50% - 10px);\n            }\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    margin-bottom: 35px;\n    margin-top: 35px;\n\n    &.has-nested-images {\n      &.is-style-downloadables {\n        gap: 25px;\n\n        &.columns-2 {\n          figure.wp-block-image {\n            &:not(#individual-image) {\n              max-width: calc(50% - 12.5px);\n              width: calc(50% - 12.5px);\n            }\n          }\n        }\n\n        &.columns-3 {\n          figure.wp-block-image {\n            &:not(#individual-image) {\n              max-width: calc(33.33% - 17px);\n              width: calc(33.33% - 17px);\n            }\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-bottom: 50px;\n    margin-top: 50px;\n\n    &.has-nested-images {\n      &.is-style-downloadables {\n        gap: 30px;\n\n        &.columns-2 {\n          figure.wp-block-image {\n            &:not(#individual-image) {\n              max-width: calc(50% - 30px);\n              width: calc(50% - 30px);\n            }\n          }\n        }\n\n        &.columns-3 {\n          figure.wp-block-image {\n            &:not(#individual-image) {\n              max-width: calc(33.33% - 20px);\n              width: calc(33.33% - 20px);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n", ".generichero {\n  padding: 85px $mobile-gutter;\n  position: relative;\n  margin-bottom: 50px;\n\n  $self: &;\n\n  .search & {\n    margin-bottom: 0;\n    padding-bottom: 248px;\n  }\n\n  &--toc {\n    padding-bottom: 50px;\n    padding-top: 80px;\n  }\n\n  &__bg-image {\n    background-size: cover;\n    background-position: center center;\n    inset: 0;\n    position: absolute;\n  }\n\n  &__bg-swirl-wrap {\n    align-items: center;\n    display: flex;\n    flex-direction: column;\n    inset: 0;\n    justify-content: center;\n    position: absolute;\n    overflow: hidden;\n    z-index: 0;\n  }\n\n  &__bg-swirl {\n    left: 0;\n    min-width: max(calc(100vh - 93px), 100vw, 100%);\n    min-height: max(calc(100vh - 93px), 100vw, 100%);\n    right: 0;\n  }\n\n  &__content {\n    display: flex;\n    flex-flow: row wrap;\n    max-width: $max-title-width;\n    margin: 0 auto;\n    position: relative;\n    z-index: 1;\n\n    h1 {\n      margin-bottom: 0;\n      width: 100%;\n    }\n\n    p {\n      margin-bottom: 0;\n      margin-top: 26px;\n      max-width: $max-text-width;\n      width: 100%;\n    }\n\n    .tableofcontents {\n      margin-top: 36px;\n      width: 100%;\n    }\n  }\n\n  .emphasizedlinklist {\n    grid-row-gap: 10px;\n  }\n\n  + .tickertape {\n    margin-top: -50px;\n  }\n\n  @include breakpoint(tablet, min) {\n    margin-bottom: 60px;\n    padding: 100px $tablet-gutter;\n\n    &--toc {\n      padding-bottom: 65px;\n    }\n\n    + .tickertape {\n      margin-top: -60px;\n    }\n  }\n\n  @include breakpoint(nav-desktop, min) {\n    &--toc {\n      padding-bottom: 95px;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    padding-left: $desktop-gutter;\n    padding-right: $desktop-gutter;\n    margin-bottom: 70px;\n\n    &--toc {\n      #{$self} {\n        &__content {\n          display: grid;\n          grid-template-columns: calc(100% - 305px) 275px;\n          grid-column-gap: 30px;\n          max-width: 1115px;\n          transform: translateX(0);\n        }\n      }\n\n      h1,\n      p {\n        grid-column: 1;\n      }\n\n      h1 {\n        grid-row: 1;\n      }\n\n      p {\n        grid-row: 2;\n      }\n\n      nav + h1 + p {\n        grid-row: 3;\n      }\n\n      .tableofcontents {\n        grid-column: 2;\n        grid-row: 1 / span 2;\n        margin-left: auto;\n        margin-right: 0;\n        margin-top: 7px;\n        max-width: 275px;\n        width: auto;\n      }\n    }\n\n    &__content {\n      margin: 0 auto;\n    }\n\n    .emphasizedlinklist {\n      grid-row-gap: 20px;\n    }\n\n    + .tickertape {\n      margin-top: -70px;\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    &--toc {\n      .tableofcontents {\n        margin-top: 13px;\n      }\n    }\n  }\n\n  @include breakpoint(xl-desktop, min) {\n    padding-right: $desktop-gutter;\n    margin-bottom: 80px;\n\n    &__content {\n      margin: 0 auto;\n      transform: translateX(-90px);\n    }\n\n    &--toc {\n      #{$self} {\n        &__content {\n          display: grid;\n          grid-column-gap: 40px;\n          grid-template-columns: calc(100% - 315px) 275px;\n        }\n      }\n\n      .tableofcontents {\n        max-width: 275px;\n        width: 100%;\n      }\n    }\n\n    + .tickertape {\n      margin-top: -80px;\n    }\n  }\n}\n", ".homepagecd {\n  background-color: var(--form-background-color);\n  min-height: calc(100vh - 93px);\n  transition: background 0.3s ease-in-out;\n\n  &.target {\n    background-color: var(--target-background-color);\n  }\n}\n", ".homepageform {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 85px $mobile-gutter;\n  position: relative;\n  min-height: calc(100vh - 93px);\n\n  &__bg-swirl-wrap {\n    align-items: center;\n    display: flex;\n    flex-direction: column;\n    inset: 0;\n    justify-content: center;\n    position: absolute;\n    overflow: hidden;\n    z-index: 0;\n  }\n\n  &__bg-swirl {\n    left: 0;\n    right: 0;\n    min-width: max(100vw, calc(100vh - 93px), 100%);\n    min-height: max(100vw, calc(100vh - 93px), 100%);\n  }\n\n  &__title {\n    margin-top: 30px;\n    max-width: $max-title-width;\n    order: 1;\n    position: relative;\n\n    .italicize {\n      display: inline-block;\n      transform: scale(1) rotate(0deg) translate(-0.01em, 0) skew(14.3deg, 0deg);\n      transform-origin: center;\n    }\n  }\n\n  &__content {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-flow: column;\n    max-width: $max-wide-width;\n    margin: 0 auto;\n    z-index: 1;\n\n    h1 {\n      margin-bottom: 0;\n      text-align: center;\n      width: 100%;\n    }\n  }\n\n  &__icon {\n    color: $golden-rod;\n    height: 0;\n    margin-bottom: 20px;\n    margin-top: 35px;\n    order: 2;\n    width: 130px;\n    visibility: hidden;\n  }\n\n  &__subheading,\n  &__postsubmit {\n    color: var(--subheading-color);\n    max-width: $max-text-width;\n    text-align: center;\n\n    a:not(.emphasizedlink, .button) {\n      @include inline-link(\n        $color: var(--subheading-link-color),\n        $hover: var(--subheading-link-color)\n      );\n    }\n  }\n\n  &.is-style-heading-1-5 {\n    .homepageform__content {\n      h1 {\n        @include community-stats;\n      }\n    }\n  }\n\n  &.is-style-heading-2 {\n    .homepageform__content {\n      h1 {\n        @include h2;\n      }\n    }\n  }\n\n  &.is-style-heading-3 {\n    .homepageform__content {\n      h1 {\n        @include h3;\n\n        text-transform: none;\n      }\n    }\n  }\n\n  &.is-style-heading-4 {\n    .homepageform__content {\n      h1 {\n        @include h4;\n\n        text-transform: none;\n      }\n    }\n  }\n\n  &.is-style-heading-5 {\n    .homepageform__content {\n      h1 {\n        @include h5;\n\n        text-transform: none;\n      }\n    }\n  }\n\n  &.is-style-heading-6 {\n    .homepageform__content {\n      h1 {\n        @include h6;\n\n        text-transform: none;\n      }\n    }\n  }\n\n  &__postsubmit {\n    display: block;\n    margin-top: 10px;\n    order: 3;\n\n    &.hidden {\n      display: none;\n      margin-top: 0;\n    }\n  }\n\n  &__heading + &__subheading {\n    margin-top: 35px;\n    margin-left: auto;\n    margin-right: auto;\n  }\n\n  .homepageformpart,\n  .homepageform__donation {\n    margin-top: 30px;\n    order: 3;\n    position: relative;\n    width: 100%;\n  }\n\n  .homepageformpart {\n    &__text {\n      margin-left: auto;\n      margin-right: auto;\n      max-width: $max-text-width;\n      text-align: center;\n    }\n\n    &__form {\n      margin-top: 20px;\n    }\n\n    &__form-button {\n      margin-top: 35px;\n      max-width: 400px;\n      width: 100%;\n    }\n\n    &__actionnetwork {\n      align-items: flex-end;\n      display: flex;\n      flex-flow: row wrap;\n      grid-gap: 10px 35px;\n      justify-content: center;\n      max-width: $max-title-width;\n\n      &.hidden {\n        display: none;\n      }\n\n      .input-group {\n        &--email,\n        &--first-name,\n        &--last-name,\n        &--phone,\n        &--zip {\n          flex: 1 1 10px;\n          min-width: 240px;\n          max-width: 400px;\n          width: 100%;\n        }\n\n        .input-group__error-msg {\n          &::after {\n            right: -20px;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    padding: 120px $tablet-gutter;\n  }\n\n  @include breakpoint(nav-desktop, min) {\n    &__title {\n      margin-top: 0;\n      order: 2;\n    }\n\n    &__icon {\n      margin-bottom: 0;\n      order: 1;\n    }\n\n    &__bg-swirl {\n      position: absolute;\n    }\n\n    .homepageformpart,\n    .homepageform__donation {\n      margin-top: 40px;\n      order: 3;\n    }\n\n    .homepageformpart {\n      &__form {\n        margin-top: 30px;\n      }\n\n      &__form-button {\n        width: fit-content;\n      }\n\n      &__actionnetwork {\n        grid-gap: 10px 35px;\n\n        .input-group {\n          .input-group__error-msg {\n            &::after {\n              right: -20px;\n            }\n          }\n        }\n      }\n    }\n\n    &__post-submit {\n      margin-top: 30px;\n\n      &.hidden {\n        margin-top: 0;\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    &__icon {\n      height: 0;\n      margin-bottom: 0;\n      width: 160px;\n    }\n\n    &__heading + &__subheading {\n      margin-top: 40px;\n    }\n\n    &__post-submit {\n      margin-top: 40px;\n\n      &.hidden {\n        margin-top: 0;\n      }\n    }\n\n    .homepageformpart,\n    .homepageform__donation {\n      margin-top: 50px;\n    }\n\n    .homepageformpart {\n      &__actionnetwork {\n        .input-group {\n          &--email,\n          &--first-name,\n          &--last-name,\n          &--phone {\n            min-width: 400px;\n          }\n\n          &--zip {\n            max-width: 240px;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    &__heading + &__subheading {\n      margin-top: 50px;\n    }\n\n    &__post-submit {\n      margin-top: 50px;\n\n      &.hidden {\n        margin-top: 0;\n      }\n    }\n  }\n\n  @include breakpoint(xl-desktop, min) {\n    padding-left: $desktop-nav-width;\n    padding-right: $desktop-nav-width;\n  }\n}\n", ".wp-block-image {\n  &.is-style-corner-pixels {\n    img {\n      clip-path: $pixel-tips-mobile;\n    }\n  }\n\n  figcaption.wp-element-caption {\n    @include caption;\n\n    margin-top: 20px;\n    position: relative;\n  }\n\n  @include breakpoint(desktop, min) {\n    &.is-style-corner-pixels {\n      img {\n        clip-path: $pixel-tips-desktop;\n      }\n    }\n  }\n}\n", ".imagecaption {\n  margin-bottom: 50px;\n  margin-top: 50px;\n\n  &__caption {\n    margin-left: 0;\n    margin-right: auto;\n    margin-top: 10px;\n    max-width: 720px;\n  }\n\n  &__image {\n    clip-path: $pixel-tips-mobile;\n    display: block;\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-bottom: 60px;\n    margin-top: 60px;\n\n    &__image {\n      clip-path: $pixel-tips-desktop;\n    }\n\n    &__caption {\n      margin-top: 20px;\n    }\n  }\n}\n", ".homepagemidsection {\n  background-color: var(--bg-color);\n  min-height: calc(100vh - 93px);\n  overflow: hidden;\n  padding-bottom: 75px;\n  position: relative;\n\n  $self: &;\n\n  &__wave {\n    grid-column: 1 / span 6;\n    grid-row: 1 / span 5;\n    height: 134vw;\n    margin-top: auto;\n    margin-bottom: -25vw;\n    width: 100vw;\n\n    &--desktop {\n      display: none;\n    }\n  }\n\n  &__grid {\n    align-items: start;\n    display: grid;\n    grid-auto-rows: auto;\n    grid-gap: 0 $mobile-gutter;\n    grid-template-columns: repeat(6, 1fr);\n    inset: 0;\n    padding-top: 45px;\n    position: relative;\n    width: 100%;\n  }\n\n  &__bgimage {\n    display: block;\n    position: relative;\n    z-index: 1;\n    width: 100%;\n\n    &::after {\n      background: linear-gradient(\n        180deg,\n        rgba($crt-black, 0%) 50%,\n        rgba($crt-black, 50%) 75%,\n        rgba($crt-black, 70%) 100%\n      );\n      clip-path: $pixel-tips-mobile;\n      content: '';\n      inset: 0;\n      position: absolute;\n      z-index: 0;\n    }\n\n    &--1 {\n      grid-column: 1 / span 5;\n      grid-row: 1;\n      margin-left: 0;\n      margin-right: auto;\n      max-width: 255px;\n\n      &#{$self}__bgimage--count-2 {\n        &#{$self}__bgimage--loop-2 {\n          animation: fade2 10s linear infinite;\n          animation-direction: alternate;\n          animation-delay: 2s;\n        }\n      }\n\n      &#{$self}__bgimage--count-3 {\n        animation: rotate-in-out 15s linear infinite;\n\n        &#{$self}__bgimage--loop-1 {\n          animation-delay: 0s;\n        }\n\n        &#{$self}__bgimage--loop-2 {\n          animation-delay: -5s;\n        }\n\n        &#{$self}__bgimage--loop-3 {\n          animation-delay: -10s;\n        }\n\n        @keyframes rotate-in-out {\n          0% {\n            opacity: 1;\n          }\n\n          26% {\n            opacity: 1;\n          }\n\n          33% {\n            opacity: 0;\n          }\n\n          93% {\n            opacity: 0;\n          }\n\n          100% {\n            opacity: 1;\n          }\n        }\n      }\n    }\n\n    &--2 {\n      grid-column: 4 / span 3;\n      grid-row: 3;\n      margin-left: auto;\n      margin-right: 0;\n      margin-top: -30px;\n      max-width: 170px;\n\n      &#{$self}__bgimage--count-2 {\n        &#{$self}__bgimage--loop-2 {\n          animation: fade2 8s linear infinite;\n          animation-direction: alternate;\n        }\n      }\n\n      &#{$self}__bgimage--count-3 {\n        animation: rotate-in-out 13.5s linear infinite;\n\n        &#{$self}__bgimage--loop-1 {\n          animation-delay: 0s;\n        }\n\n        &#{$self}__bgimage--loop-2 {\n          animation-delay: -4.5s;\n        }\n\n        &#{$self}__bgimage--loop-3 {\n          animation-delay: -9s;\n        }\n\n        @keyframes rotate-in-out {\n          0% {\n            opacity: 1;\n          }\n\n          25.93% {\n            opacity: 1;\n          }\n\n          33.33% {\n            opacity: 0;\n          }\n\n          92.59% {\n            opacity: 0;\n          }\n\n          100% {\n            opacity: 1;\n          }\n        }\n      }\n    }\n\n    &--3 {\n      grid-column: 1 / span 3;\n      grid-row: 4;\n      margin-left: $mobile-gutter;\n      margin-right: auto;\n      margin-top: -30px;\n      max-width: 160px;\n\n      &#{$self}__bgimage--count-2 {\n        &#{$self}__bgimage--loop-2 {\n          animation: fade2 8s linear infinite;\n          animation-direction: alternate;\n          animation-delay: -4s;\n        }\n      }\n\n      &#{$self}__bgimage--count-3 {\n        animation: rotate-in-out 16.5s linear infinite;\n\n        &#{$self}__bgimage--loop-1 {\n          animation-delay: 0s;\n        }\n\n        &#{$self}__bgimage--loop-2 {\n          animation-delay: -5.5s;\n        }\n\n        &#{$self}__bgimage--loop-3 {\n          animation-delay: -11s;\n        }\n\n        @keyframes rotate-in-out {\n          0% {\n            opacity: 1;\n          }\n\n          27.27% {\n            opacity: 1;\n          }\n\n          33.33% {\n            opacity: 0;\n          }\n\n          93.94% {\n            opacity: 0;\n          }\n\n          100% {\n            opacity: 1;\n          }\n        }\n      }\n    }\n\n    &--4 {\n      grid-column: 2 / span 5;\n      grid-row: 5;\n      margin-left: auto;\n      margin-right: 0;\n      margin-right: $mobile-gutter;\n      margin-top: 100px;\n      max-width: 265px;\n\n      &#{$self}__bgimage--count-2 {\n        &#{$self}__bgimage--loop-2 {\n          animation: fade2 8s linear infinite;\n          animation-direction: alternate;\n          animation-delay: -2s;\n        }\n      }\n\n      &#{$self}__bgimage--count-3 {\n        animation: rotate-in-out 18s linear infinite;\n\n        &#{$self}__bgimage--loop-1 {\n          animation-delay: 0s;\n        }\n\n        &#{$self}__bgimage--loop-2 {\n          animation-delay: -6s;\n        }\n\n        &#{$self}__bgimage--loop-3 {\n          animation-delay: -12s;\n        }\n\n        @keyframes rotate-in-out {\n          0% {\n            opacity: 1;\n          }\n\n          27.77% {\n            opacity: 1;\n          }\n\n          33.33% {\n            opacity: 0;\n          }\n\n          94.44% {\n            opacity: 0;\n          }\n\n          100% {\n            opacity: 1;\n          }\n        }\n      }\n    }\n  }\n\n  &__caption {\n    @include caption-small;\n\n    color: $white;\n    padding-bottom: 6px;\n    padding-left: 16px;\n    padding-right: 16px;\n    position: absolute;\n    bottom: 0;\n    z-index: 1;\n  }\n\n  &__image {\n    clip-path: $pixel-tips-mobile;\n    display: block;\n    position: relative;\n    width: 100%;\n\n    &--1 {\n      max-width: 255px;\n    }\n\n    &--2 {\n      max-width: 170px;\n    }\n\n    &--3 {\n      max-width: 160px;\n    }\n\n    &--4 {\n      max-width: 265px;\n    }\n  }\n\n  &__section {\n    grid-column: 1 / span 6;\n    grid-row: 2;\n    margin-top: 50px;\n    padding-left: $mobile-gutter;\n    padding-right: $mobile-gutter;\n    position: relative;\n    z-index: 3;\n    width: 100%;\n\n    &:nth-of-type(2) {\n      grid-column: 1 / span 6;\n      grid-row: 6;\n      margin-top: 105px;\n    }\n  }\n\n  &__icon {\n    color: $golden-rod;\n    position: relative;\n    transform: translate(var(--tx), var(--ty)) rotate(var(--r));\n    z-index: 2;\n\n    &--whitehouse {\n      grid-column: 1 / span 6;\n      grid-row: 1;\n      height: 49px;\n      margin: -20px auto auto 40px;\n      width: 72px;\n    }\n\n    &--handwritten {\n      grid-column: 3 / span 3;\n      grid-row: 1;\n      margin: auto -10px 40px auto;\n      height: 47px;\n      width: 53px;\n    }\n\n    &--handtie {\n      grid-column: 3 / span 6;\n      grid-row: 4;\n      height: 53px;\n      width: 35px;\n    }\n\n    &--email {\n      grid-column: 1 / span 6;\n      grid-row: 6;\n      margin-bottom: auto;\n      margin-left: 40px;\n      margin-top: auto;\n      height: 32px;\n      width: 47px;\n    }\n\n    &--youdecide {\n      grid-column: 1 / span 6;\n      grid-row: 4;\n      margin-bottom: auto;\n      margin-top: -70px;\n      margin-left: 35px;\n      height: 55px;\n      width: 78px;\n    }\n\n    &--star {\n      grid-column: 1 / span 6;\n      grid-row: 7;\n      margin-left: auto;\n      margin-right: 22px;\n      height: 64px;\n      width: 72px;\n    }\n  }\n\n  @include breakpoint(l-mobile, min) {\n    padding-bottom: 85px;\n\n    &__grid {\n      grid-template-columns: repeat(12, 1fr);\n    }\n\n    &__icon {\n      &--whitehouse {\n        margin-top: -35px;\n      }\n\n      &--handwritten {\n        grid-column: 6 / span 6;\n      }\n\n      &--handtie {\n        grid-row: 3;\n        grid-column: 1 / span 6;\n      }\n\n      &--email {\n        margin-top: 50px;\n        grid-column: 10 / span 4;\n        grid-row: 5;\n      }\n\n      &--star {\n        grid-column: 1 / span 11;\n      }\n\n      &--youdecide {\n        grid-row: 5;\n        margin-top: 10%;\n        margin-bottom: auto;\n      }\n    }\n\n    &__section {\n      grid-column: 1 / span 12;\n      grid-row: 2;\n      margin-top: -50px;\n      padding-left: $tablet-gutter;\n      padding-right: $tablet-gutter;\n\n      &:nth-of-type(2) {\n        grid-row: 5;\n        grid-column: 1 / span 12;\n        margin-top: 105px;\n        padding-left: $tablet-gutter;\n        padding-right: $tablet-gutter;\n      }\n    }\n\n    &__bgimage {\n      &--1 {\n        grid-column: 1 / span 5;\n        grid-row: 1;\n      }\n\n      &--2 {\n        grid-column: 9 / span 4;\n        grid-row: 2;\n        margin-top: auto;\n      }\n\n      &--3 {\n        grid-column: 1 / span 5;\n        grid-row: 3;\n        max-width: 335px;\n        margin-left: $mobile-gutter;\n      }\n\n      &--4 {\n        grid-column: 8 / span 5;\n        grid-row: 4;\n        margin-top: -35px;\n        max-width: 360px;\n        margin-right: $desktop-gutter;\n      }\n    }\n\n    &__image {\n      &--3 {\n        max-width: 335px;\n      }\n\n      &--4 {\n        max-width: 360px;\n      }\n    }\n\n    &__wave {\n      grid-column: 1 / span 12;\n      grid-row: 1 / span 4;\n      height: 100vw;\n      margin-bottom: -30vw;\n\n      &--mobile {\n        display: none;\n      }\n\n      &--desktop {\n        display: block;\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    &__section {\n      margin-top: -100px;\n\n      &:nth-of-type(2) {\n        margin-top: 105px;\n      }\n    }\n\n    &__bgimage {\n      &--3 {\n        margin-top: -100px;\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    padding-bottom: 85px;\n\n    &__grid {\n      grid-template-columns: repeat(24, 1fr);\n    }\n\n    &__bgimage {\n      &::after {\n        clip-path: $pixel-tips-desktop;\n      }\n\n      &--1 {\n        grid-column: 2 / span 11;\n        max-width: 435px;\n      }\n\n      &--2 {\n        grid-column: 19 / span 6;\n        margin-top: 175px;\n        max-width: 260px;\n      }\n\n      &--3 {\n        grid-column: 1 / span 8;\n        margin-top: -165px;\n        max-width: 400px;\n        margin-left: 0;\n      }\n\n      &--4 {\n        grid-column: 12 / span 11;\n        max-width: 385px;\n        margin-right: $desktop-gutter;\n      }\n    }\n\n    &__image {\n      clip-path: $pixel-tips-desktop;\n\n      &--1 {\n        max-width: 435px;\n      }\n\n      &--2 {\n        max-width: 260px;\n      }\n\n      &--3 {\n        max-width: 435px;\n      }\n\n      &--4 {\n        max-width: 435px;\n      }\n    }\n\n    &__icon {\n      &--whitehouse {\n        grid-column: 3 / span 10;\n        height: 78px;\n        margin-top: -40px;\n        width: 115px;\n      }\n\n      &--handwritten {\n        grid-column: 13 / span 10;\n        height: 75px;\n        width: 85px;\n      }\n\n      &--handtie {\n        height: 85px;\n        grid-row: 5;\n        grid-column: 21 / span 6;\n        width: 55px;\n      }\n\n      &--email {\n        grid-column: 3 / span 8;\n        height: 50px;\n        width: 75px;\n      }\n\n      &--star {\n        grid-column: 10 / span 11;\n        height: 103px;\n        width: 115px;\n      }\n\n      &--youdecide {\n        grid-row: 2;\n        height: 88px;\n        margin-top: auto;\n        width: 125px;\n      }\n    }\n\n    &__caption {\n      padding-bottom: 10px;\n      padding-left: 24px;\n      padding-right: 24px;\n    }\n\n    &__section {\n      grid-column: 1 / span 24;\n      padding-left: $desktop-gutter;\n      padding-right: $desktop-gutter;\n      margin-top: -80px;\n\n      &:nth-of-type(2) {\n        grid-column: 1 / span 24;\n        padding-left: $desktop-gutter;\n        padding-right: $desktop-gutter;\n        margin-top: 105px;\n      }\n    }\n\n    &__wave {\n      grid-column: 1 / span 24;\n      margin-bottom: -25vw;\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    padding-bottom: 90px;\n\n    &__section {\n      margin-top: -100px;\n\n      &:nth-of-type(2) {\n        margin-top: 135px;\n      }\n    }\n  }\n\n  @include breakpoint(xl-desktop, min) {\n    &__wave {\n      margin-bottom: -30vw;\n    }\n\n    &__section {\n      margin-top: -110px;\n\n      &:nth-of-type(2) {\n        margin-top: 145px;\n      }\n    }\n\n    &__bgimage {\n      &--3 {\n        max-width: 435px;\n      }\n\n      &--4 {\n        max-width: 425px;\n      }\n    }\n  }\n\n  @media (prefers-reduced-motion) {\n    &__bgimage {\n      animation-play-state: paused;\n    }\n\n    &__icon {\n      transform: none;\n    }\n  }\n}\n\n@keyframes fade2 {\n  0% {\n    opacity: 1;\n  }\n\n  25% {\n    opacity: 1;\n  }\n\n  43.75% {\n    opacity: 1;\n  }\n\n  56.52% {\n    opacity: 0;\n  }\n\n  75% {\n    opacity: 0;\n  }\n\n  100% {\n    opacity: 0;\n  }\n}\n", "/**\n  THEME\n  specific theme style variables\n**/\n\n/* Pixel Tips */\n$pixel-tips-button: polygon(\n  0 5px,\n  5px 5px,\n  5px 5px,\n  5px 5px,\n  5px 0,\n  calc(100% - 5px) 0,\n  calc(100% - 5px) 5px,\n  calc(100% - 5px) 5px,\n  calc(100% - 5px) 5px,\n  100% 5px,\n  100% calc(100% - 5px),\n  calc(100% - 5px) calc(100% - 5px),\n  calc(100% - 5px) calc(100% - 5px),\n  calc(100% - 5px) calc(100% - 5px),\n  calc(100% - 5px) 100%,\n  5px 100%,\n  5px calc(100% - 5px),\n  5px calc(100% - 5px),\n  5px calc(100% - 5px),\n  0 calc(100% - 5px)\n);\n$pixel-tips-mobile: polygon(\n  0 8px,\n  8px 8px,\n  8px 8px,\n  8px 8px,\n  8px 0,\n  calc(100% - 8px) 0,\n  calc(100% - 8px) 8px,\n  calc(100% - 8px) 8px,\n  calc(100% - 8px) 8px,\n  100% 8px,\n  100% calc(100% - 8px),\n  calc(100% - 8px) calc(100% - 8px),\n  calc(100% - 8px) calc(100% - 8px),\n  calc(100% - 8px) calc(100% - 8px),\n  calc(100% - 8px) 100%,\n  8px 100%,\n  8px calc(100% - 8px),\n  8px calc(100% - 8px),\n  8px calc(100% - 8px),\n  0 calc(100% - 8px)\n);\n$pixel-tips-top-mobile: polygon(\n  0 8px,\n  8px 8px,\n  8px 8px,\n  8px 8px,\n  8px 0,\n  calc(100% - 8px) 0,\n  calc(100% - 8px) 8px,\n  calc(100% - 8px) 8px,\n  calc(100% - 8px) 8px,\n  100% 8px,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  0 100%,\n  0 100%,\n  0 0,\n  0 100%,\n  0 100%\n);\n$pixel-tips-top-desktop: polygon(\n  0 12px,\n  12px 12px,\n  12px 12px,\n  12px 12px,\n  12px 0,\n  calc(100% - 12px) 0,\n  calc(100% - 12px) 12px,\n  calc(100% - 12px) 12px,\n  calc(100% - 12px) 12px,\n  100% 12px,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  0 100%,\n  0 100%,\n  0 0,\n  0 100%,\n  0 100%\n);\n$pixel-tips-desktop: polygon(\n  0 12px,\n  12px 12px,\n  12px 12px,\n  12px 12px,\n  12px 0,\n  calc(100% - 12px) 0,\n  calc(100% - 12px) 12px,\n  calc(100% - 12px) 12px,\n  calc(100% - 12px) 12px,\n  100% 12px,\n  100% calc(100% - 12px),\n  calc(100% - 12px) calc(100% - 12px),\n  calc(100% - 12px) calc(100% - 12px),\n  calc(100% - 12px) calc(100% - 12px),\n  calc(100% - 12px) 100%,\n  12px 100%,\n  12px calc(100% - 12px),\n  12px calc(100% - 12px),\n  12px calc(100% - 12px),\n  0 calc(100% - 12px)\n);\n$pixel-tips-right-mobile: polygon(\n  0 0,\n  calc(100% - 8px) 0,\n  calc(100% - 8px) 8px,\n  calc(100% - 8px) 8px,\n  calc(100% - 8px) 8px,\n  100% 8px,\n  100% calc(100% - 8px),\n  calc(100% - 8px) calc(100% - 8px),\n  calc(100% - 8px) calc(100% - 8px),\n  calc(100% - 8px) calc(100% - 8px),\n  calc(100% - 8px) 100%,\n  8px 100%,\n  0 100%\n);\n$pixel-tips-left-mobile: polygon(\n  0 8px,\n  8px 8px,\n  8px 8px,\n  8px 8px,\n  8px 0,\n  100% 0,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  8px 100%,\n  8px calc(100% - 8px),\n  8px calc(100% - 8px),\n  8px calc(100% - 8px),\n  0 calc(100% - 8px)\n);\n$pixel-tips-left-desktop: polygon(\n  0 12px,\n  12px 12px,\n  12px 12px,\n  12px 12px,\n  12px 0,\n  100% 0,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  100% 100%,\n  12px 100%,\n  12px calc(100% - 12px),\n  12px calc(100% - 12px),\n  12px calc(100% - 12px),\n  0 calc(100% - 12px)\n);\n$pixel-tips-right-desktop: polygon(\n  0 0,\n  calc(100% - 12px) 0,\n  calc(100% - 12px) 12px,\n  calc(100% - 12px) 12px,\n  calc(100% - 12px) 12px,\n  100% 12px,\n  100% calc(100% - 12px),\n  calc(100% - 12px) calc(100% - 12px),\n  calc(100% - 12px) calc(100% - 12px),\n  calc(100% - 12px) calc(100% - 12px),\n  calc(100% - 12px) 100%,\n  12px 100%,\n  0 100%\n);\n", ".wp-block-media-text {\n  $self: &;\n\n  &.is-stacked-on-mobile {\n    #{$self} {\n      &__content {\n        margin-bottom: 30px;\n        margin-top: 30px;\n      }\n    }\n  }\n\n  #{$self}__content {\n    padding-right: 0;\n    padding-left: 0;\n  }\n\n  @include breakpoint(wp-admin, min) {\n    #{$self} {\n      &__content {\n        padding-left: 45px;\n      }\n    }\n\n    &.is-stacked-on-mobile {\n      #{$self} {\n        &__content {\n          margin-bottom: 0;\n          margin-top: 0;\n        }\n      }\n    }\n\n    &.has-media-on-the-right {\n      #{$self} {\n        &__content {\n          padding-left: 0;\n          padding-right: 45px;\n        }\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    #{$self} {\n      &__content {\n        padding-left: 60px;\n      }\n    }\n\n    &.has-media-on-the-right {\n      #{$self} {\n        &__content {\n          padding-left: 0;\n          padding-right: 60px;\n        }\n      }\n    }\n  }\n}\n", ".mobilizeevent {\n  color: $white;\n  position: relative;\n\n  &--vertical {\n    text-align: left;\n  }\n\n  &__name {\n    @include h5;\n\n    margin-bottom: 26px;\n  }\n\n  &__date {\n    @include h6;\n\n    display: block;\n    margin-bottom: 14px;\n  }\n\n  &__det {\n    @include body;\n  }\n\n  &__fig {\n    background-color: $white;\n    border-top: 6px solid $electoral-blue;\n    display: inline-flex;\n    height: 0;\n    justify-content: center;\n    margin-bottom: 30px;\n    overflow: hidden;\n    padding-top: 250px / 475px * 100%;\n    position: relative;\n    transition: border 0.3s ease-in-out;\n    width: 100%;\n  }\n\n  &__img {\n    display: block;\n    filter: grayscale(100%);\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    object-position: center;\n    transition: filter 0.3s ease-in-out;\n  }\n\n  &__link {\n    .emphasizedlink {\n      display: inline-block;\n    }\n\n    &::after {\n      content: '';\n      inset: 0;\n      position: absolute;\n      z-index: 1;\n    }\n  }\n\n  &:hover,\n  &:focus {\n    .mobilizeevent {\n      &__fig {\n        border-top-color: $patriotic-red;\n      }\n\n      &__img {\n        filter: grayscale(0%);\n      }\n    }\n\n    .emphasizedlink {\n      color: var(--text-hover-color);\n      font-style: italic;\n\n      .emphasizedlink__svg {\n        &--two {\n          transform: translateX(10px);\n        }\n      }\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    &--horizontal {\n      align-items: center;\n      display: grid;\n      grid-column-gap: 30px;\n      grid-template-columns: min(calc(50% - 30px), 325px) 1fr;\n\n      .mobilizeevent__fig {\n        border-top: none;\n        border-left: 6px solid $electoral-blue;\n        margin-bottom: 0;\n      }\n\n      &:hover,\n      &:focus {\n        .mobilizeevent__fig {\n          border-left-color: $patriotic-red;\n        }\n      }\n    }\n  }\n}\n", ".pagination {\n  align-items: center;\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  width: 100%;\n\n  &-block {\n    margin: 24px auto 70px;\n    max-width: 1115px;\n    padding: 0 25px;\n    width: 100%;\n  }\n\n  .page-number {\n    color: $electoral-blue;\n    display: block;\n    text-align: center;\n    leading-trim: both;\n    text-edge: cap;\n    font-family: $rainer-font;\n    font-size: 50px;\n    font-style: normal;\n    font-weight: 700;\n    line-height: 93%; /* 46.5px */\n    letter-spacing: 0.5px;\n    min-width: 57px;\n    position: relative;\n    text-transform: uppercase;\n\n    &.current {\n      opacity: 0.25;\n      pointer-events: none;\n    }\n\n    &:hover,\n    &:focus {\n      color: $patriotic-red;\n      font-style: italic;\n\n      &::after {\n        content: '';\n        background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='57' height='10' fill='none'%3E%3Cpath stroke='%23F8502D' stroke-linecap='round' stroke-linejoin='round' stroke-miterlimit='10' d='M10.923 1.016C25.856.392 40.653.425 55.18 2.34c-7.02 1.686-14.085-.057-20.857-.03-10.829.046-21.692.141-32.657.956 3.03-.743 6.613-.288 9.427-.334 7.11-.072 14.199-.034 21.264.227 7.98.279 15.984.677 23.773 1.999-9.45-.696-19.172-.309-28.769-.066-8.625.215-17.284-.318-26.112 1.451 9.97.12 20.121-.977 29.956-.088 5.28.463 11.383-.33 16.166 2.424'/%3E%3C/svg%3E\");\n        bottom: -10px;\n        height: 10px;\n        left: 0;\n        position: absolute;\n        width: 57px;\n      }\n    }\n  }\n\n  .btn {\n    position: relative;\n\n    .label {\n      @include is-visually-hidden;\n    }\n\n    &::after {\n      display: block;\n      position: absolute;\n      top: 0;\n    }\n\n    a {\n      display: block;\n      height: 100%;\n      position: relative;\n      width: 100%;\n      z-index: 1;\n    }\n  }\n\n  .first,\n  .last {\n    display: inline-block;\n    height: 16px;\n    width: 36px;\n  }\n\n  .next,\n  .prev {\n    display: inline-block;\n    height: 16px;\n    width: 12px;\n  }\n\n  .first {\n    &::after {\n      content: '';\n      background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='20' fill='none'%3E%3Cpath stroke='%23160fcc' stroke-width='4' d='m16 12-5-5 5-5M8 12 3 7l5-5'/%3E%3C/svg%3E\");\n      background-position: center;\n      background-repeat: no-repeat;\n      background-size: contain;\n      height: 24px;\n      width: 36px;\n    }\n\n    &:hover,\n    &:focus {\n      &::after {\n        background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='20' fill='none'%3E%3Cpath stroke='%23f65137' stroke-width='4' d='m16 12-5-5 5-5M8 12 3 7l5-5'/%3E%3C/svg%3E\");\n      }\n    }\n  }\n\n  .last {\n    &::after {\n      content: '';\n      background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='20' fill='none'%3E%3Cpath stroke='%23160fcc' stroke-width='4' d='m2 2 5 5-5 5M10 2l5 5-5 5'/%3E%3C/svg%3E\");\n      background-position: center;\n      background-repeat: no-repeat;\n      background-size: contain;\n      height: 24px;\n      width: 36px;\n    }\n\n    &:hover,\n    &:focus {\n      &::after {\n        background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='20' fill='none'%3E%3Cpath stroke='%23f65137' stroke-width='4' d='m2 2 5 5-5 5M10 2l5 5-5 5'/%3E%3C/svg%3E\");\n      }\n    }\n  }\n\n  .prev {\n    &::after {\n      content: '';\n      background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='20' fill='none'%3E%3Cpath stroke='%23160FCC' stroke-width='3' d='m7.5 12-5-5 5-5'/%3E%3C/svg%3E%0A\");\n      background-position: center;\n      background-repeat: no-repeat;\n      background-size: contain;\n      height: 24px;\n      width: 12px;\n    }\n\n    &:hover,\n    &:focus {\n      &::after {\n        background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='20' fill='none'%3E%3Cpath stroke='%23f65137' stroke-width='3' d='m7.5 12-5-5 5-5'/%3E%3C/svg%3E%0A\");\n      }\n    }\n  }\n\n  .next {\n    &::after {\n      content: '';\n      background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='20' fill='none'%3E%3Cpath stroke='%23160FCC' stroke-width='4' d='m2 1.5 5 5-5 5'/%3E%3C/svg%3E\");\n      background-position: center;\n      background-repeat: no-repeat;\n      background-size: contain;\n      height: 24px;\n      width: 12px;\n    }\n\n    &:hover,\n    &:focus {\n      &::after {\n        background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='20' fill='none'%3E%3Cpath stroke='%23f65137' stroke-width='4' d='m2 1.5 5 5-5 5'/%3E%3C/svg%3E\");\n      }\n    }\n  }\n\n  .first,\n  .last,\n  .next,\n  .prev {\n    &.disabled {\n      opacity: 0.25;\n      pointer-events: none;\n    }\n  }\n\n  li {\n    margin-left: 20px;\n\n    &:last-of-type {\n      margin-right: 20px;\n    }\n  }\n}\n", ".priorityaction {\n  display: grid;\n  grid-column-gap: 20px;\n  grid-template-columns: 55px calc(100% - 75px);\n  position: relative;\n\n  $self: &;\n\n  &__icon {\n    color: var(--text-color);\n    grid-column: 1;\n    grid-row: 1 / span 2;\n    margin-top: -10px;\n    transform: translate(var(--tx), var(--ty)) rotate(var(--r));\n    width: 55px;\n  }\n\n  &__title {\n    @include h5;\n\n    color: var(--title-color);\n    grid-column: 2;\n    margin-bottom: 20px;\n  }\n\n  &__eyebrow {\n    @include h6;\n\n    color: var(--text-color);\n    display: block;\n    grid-column: 2;\n    margin-bottom: 10px;\n  }\n\n  &__desc {\n    @include body;\n\n    color: var(--title-color);\n    grid-column: 2;\n  }\n\n  &__link {\n    grid-column: 2;\n\n    &::after {\n      content: ' ';\n      position: absolute;\n      inset: 0;\n    }\n  }\n\n  &:hover,\n  &:focus {\n    #{$self}__linktext {\n      color: var(--text-hover-color);\n      font-style: italic;\n\n      .emphasizedlink__svg {\n        &--two {\n          transform: translateX(10px);\n        }\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    grid-column-gap: 10px;\n    grid-template-columns: 75px calc(100% - 85px);\n\n    &__icon {\n      margin-top: -10px;\n      width: 75px;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    grid-column-gap: 30px;\n    grid-template-columns: 115px calc(100% - 145px);\n\n    &__icon {\n      margin-top: -15px;\n      width: 115px;\n    }\n  }\n}\n", ".programhero {\n  padding: 55px $mobile-gutter 55px $mobile-gutter;\n  position: relative;\n  margin-bottom: 50px;\n\n  $self: &;\n\n  &__bg-swirl-wrap {\n    align-items: center;\n    display: flex;\n    flex-direction: column;\n    inset: 0;\n    justify-content: center;\n    position: absolute;\n    overflow: hidden;\n    z-index: 0;\n  }\n\n  &__bg-swirl {\n    left: 0;\n    min-width: max(calc(100vh - 93px), 100vw, 100%);\n    min-height: max(calc(100vh - 93px), 100vw, 100%);\n    right: 0;\n    transform: translateY(70px);\n  }\n\n  &__logo {\n    height: var(--mobile-height);\n    margin-left: auto;\n    margin-right: auto;\n    position: relative;\n    width: var(--mobile-width);\n    z-index: 1;\n  }\n\n  &__a11y-title {\n    @include is-visually-hidden;\n  }\n\n  + .tickertape {\n    margin-top: -50px;\n  }\n\n  @include breakpoint(tablet, min) {\n    margin-bottom: 60px;\n    padding: 65px $tablet-gutter;\n\n    &__logo {\n      height: var(--tablet-height);\n      width: var(--tablet-width);\n    }\n\n    &__bg-swirl {\n      transform: translateY(100px);\n    }\n\n    + .tickertape {\n      margin-top: -60px;\n    }\n  }\n\n  @include breakpoint(nav-desktop, min) {\n    padding: 100px $desktop-gutter;\n\n    &__bg-swirl {\n      transform: translateY(0);\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-bottom: 70px;\n\n    &__content {\n      margin: 0 auto 0 0;\n    }\n\n    &__logo {\n      height: var(--desktop-height);\n      width: var(--desktop-width);\n    }\n\n    + .tickertape {\n      margin-top: -70px;\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    margin-bottom: 70px;\n\n    + .tickertape {\n      margin-top: -70px;\n    }\n  }\n\n  @include breakpoint(xl-desktop, min) {\n    margin-bottom: 80px;\n\n    &__content {\n      margin: 0 auto;\n      transform: translateX(-90px);\n    }\n\n    + .tickertape {\n      margin-top: -80px;\n    }\n  }\n}\n", ".programmap {\n  padding: 0;\n\n  &__content {\n    display: grid;\n    grid-gap: 40px;\n    align-items: center;\n    margin-left: auto;\n    margin-right: auto;\n    max-width: $max-title-width;\n    position: relative;\n  }\n\n  $self: &;\n\n  &__fieldset {\n    border: none;\n    display: grid;\n    grid-template-columns: 1fr;\n    grid-gap: 10px;\n    grid-row: 3;\n    padding: 0;\n  }\n\n  &__radio {\n    min-height: px-to-rem(22px);\n    min-width: px-to-rem(22px);\n  }\n\n  &__radio-label {\n    @include click-cursor;\n\n    color: var(--text-color);\n    display: flex;\n    flex-direction: row;\n    align-items: flex-start;\n    margin-top: 0;\n\n    input[type='radio'].programmap__radio {\n      border-color: var(--text-color);\n    }\n  }\n\n  &__radio-text {\n    color: var(--text-color);\n    margin-left: 10px;\n    margin-top: 4px;\n  }\n\n  &__maplegend {\n    display: flex;\n    flex-flow: row wrap;\n    align-items: center;\n    justify-content: center;\n    grid-row: 2;\n  }\n\n  &__maplegenditem {\n    align-items: center;\n    color: var(--text-color);\n    display: flex;\n    flex-flow: row nowrap;\n    margin: 0 8px;\n    padding-left: 28px;\n    position: relative;\n    text-transform: none;\n\n    &::before {\n      background: var(--bg-color);\n      border: 2px solid $crt-black;\n      content: '';\n      height: 18px;\n      left: 0;\n      position: absolute;\n      width: 18px;\n    }\n  }\n\n  &__map-svg {\n    height: auto;\n    grid-row: 1;\n    order: 0;\n    position: relative;\n    width: 100%;\n  }\n\n  &__state {\n    stroke: $crt-black;\n    stroke-width: 2px;\n    transition: fill 0.3s ease-in-out, fill-opacity 0.3s ease-in-out;\n  }\n\n  &__tooltip {\n    display: block;\n    margin-top: 0;\n    max-width: 150px;\n    position: absolute;\n    width: auto;\n\n    &.hidden {\n      display: none;\n    }\n  }\n\n  &__tooltip-content {\n    text-align: center;\n  }\n\n  &__tooltip-title {\n    @include h6;\n\n    background-color: rgba($white, 0.8);\n    color: var(--text-color);\n    padding: 1px 5px;\n    white-space: nowrap;\n  }\n\n  &.hide-pointer {\n    .programmap__statecentroid {\n      display: none !important;\n    }\n  }\n\n  @include breakpoint(nav-tablet, min) {\n    &__content {\n      grid-template-columns: 1fr 3fr;\n      grid-gap: 30px 20px;\n    }\n\n    &__fieldset {\n      grid-column: 1;\n      grid-row: 1 / span 2;\n    }\n\n    &__map-svg {\n      grid-column: 2;\n      grid-row: 1;\n    }\n\n    &__maplegend {\n      grid-column: 2;\n      grid-row: 2;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    padding: 0;\n\n    &__state {\n      stroke-width: 2px;\n    }\n  }\n\n  @include breakpoint(nav-desktop, min) {\n    padding: 0;\n  }\n}\n", ".programtargets {\n  padding: 70px $mobile-gutter;\n  position: relative;\n  overflow-x: clip;\n  overflow-y: visible;\n\n  &__icon {\n    height: 50px;\n    position: absolute;\n    transform: translate(var(--tx), var(--ty)) rotate(var(--r))\n      scale(var(--scale));\n    width: 50px;\n    z-index: 1;\n\n    &:nth-child(1) {\n      color: $team-green;\n      left: -10px;\n      top: 10px;\n    }\n\n    &:nth-child(2) {\n      color: $team-green;\n      right: -10px;\n      bottom: 33%;\n    }\n\n    &:nth-child(3) {\n      color: $golden-rod;\n      right: -20px;\n      bottom: -25px;\n    }\n\n    &:nth-child(4) {\n      color: $golden-rod;\n      left: -20px;\n      top: 50%;\n    }\n\n    &:nth-child(5) {\n      color: $golden-rod;\n      right: 0;\n      top: 170px;\n    }\n\n    &:nth-child(6) {\n      color: $team-green;\n      right: 20px;\n      bottom: 0;\n    }\n  }\n\n  &__content {\n    display: grid;\n    grid-template-columns: 100%;\n    grid-gap: 50px;\n    margin: auto;\n    max-width: $max-title-width;\n\n    .heading2 {\n      margin-bottom: 0;\n      text-align: center;\n    }\n\n    .paragraph {\n      text-align: center;\n\n      &:last-of-type {\n        margin-bottom: 0;\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    padding: 90px $tablet-gutter;\n\n    &__icon {\n      height: 80px;\n      width: 80px;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    padding: 120px $desktop-gutter;\n\n    &__icon {\n      height: 100px;\n      width: 100px;\n\n      &:nth-child(1) {\n        left: 25px;\n        top: 200px;\n      }\n\n      &:nth-child(2) {\n        right: 10px;\n        bottom: 100px;\n      }\n\n      &:nth-child(3) {\n        right: -20px;\n        bottom: 60%;\n      }\n\n      &:nth-child(4) {\n        left: 0;\n        top: 50%;\n      }\n\n      &:nth-child(5) {\n        right: -30px;\n        top: 80px;\n      }\n\n      &:nth-child(6) {\n        left: 80px;\n        right: unset;\n        bottom: -30px;\n      }\n    }\n  }\n}\n", ".pullquote {\n  border-bottom: 6px solid var(--border-color);\n  border-top: 6px solid var(--border-color);\n  margin-bottom: 35px;\n  margin-top: 35px;\n  padding-bottom: 30px;\n  padding-top: 30px;\n\n  p {\n    @include pullquote;\n\n    &:last-of-type {\n      margin-bottom: 0;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    border-bottom: 12px solid var(--border-color);\n    border-top: 12px solid var(--border-color);\n    padding-bottom: 45px;\n    padding-top: 45px;\n    margin-bottom: 40px;\n    margin-top: 40px;\n  }\n}\n", ".resource {\n  position: relative;\n\n  $self: &;\n\n  &__title {\n    @include h5;\n\n    margin-bottom: 20px;\n    position: relative;\n  }\n\n  &__circle {\n    bottom: -5px;\n    height: auto;\n    left: -3px;\n    position: absolute;\n    width: calc(100% + 10px);\n    z-index: -1;\n\n    path {\n      stroke-dasharray: 675;\n      stroke-dashoffset: 675;\n    }\n\n    &[data-visible='true'] {\n      path {\n        animation: circledash 1.5s linear;\n        animation-delay: 1s;\n        animation-fill-mode: forwards;\n      }\n    }\n  }\n\n  &__eyebrow {\n    @include h6;\n\n    display: block;\n    margin-bottom: 10px;\n    position: relative;\n    width: fit-content;\n  }\n\n  &__desc {\n    @include body;\n  }\n\n  &__link {\n    &::after {\n      content: ' ';\n      position: absolute;\n      inset: 0;\n    }\n  }\n\n  &:hover,\n  &:focus {\n    #{$self}__linktext {\n      color: var(--text-hover-color);\n      font-style: italic;\n\n      .emphasizedlink__svg {\n        &--two {\n          transform: translateX(10px);\n        }\n      }\n    }\n  }\n}\n\n@keyframes circledash {\n  to {\n    stroke-dashoffset: 0;\n  }\n}\n", ".resourcelisting {\n  margin-bottom: 45px;\n  margin-top: 45px;\n\n  &__title {\n    @include h2;\n\n    margin-bottom: 30px;\n    margin-left: auto;\n    margin-right: auto;\n    max-width: $max-text-width;\n  }\n\n  &__intro {\n    @include body;\n\n    margin-bottom: 30px;\n    margin-left: auto;\n    margin-right: auto;\n    max-width: $max-text-width;\n  }\n\n  &__button {\n    margin-bottom: 30px;\n    margin-left: auto;\n    margin-right: auto;\n    max-width: $max-text-width;\n    width: 100%;\n  }\n\n  &__list {\n    display: flex;\n    flex-wrap: wrap;\n    gap: 45px;\n    justify-content: flex-start;\n    margin-top: 25px;\n  }\n\n  &__item {\n    width: 325px;\n  }\n\n  @include breakpoint(m-mobile, min) {\n    &__button {\n      width: fit-content;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    align-items: center;\n    display: flex;\n    flex-direction: column;\n    margin-bottom: 70px;\n    margin-top: 70px;\n\n    &__list {\n      gap: 55px;\n      justify-content: center;\n      margin-top: 25px;\n      max-width: 705px;\n\n      &:has(> :nth-child(2)) {\n        justify-content: flex-start;\n      }\n    }\n\n    &__button {\n      margin-bottom: 35px;\n    }\n\n    &__title {\n      margin-bottom: 35px;\n      text-align: center;\n    }\n\n    &__intro {\n      margin-bottom: 35px;\n      text-align: center;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-bottom: 100px;\n    margin-top: 100px;\n\n    &__list {\n      gap: 65px;\n      margin-top: 30px;\n      max-width: 715px;\n    }\n\n    &__button {\n      margin-bottom: 40px;\n    }\n\n    &__title {\n      margin-bottom: 40px;\n    }\n\n    &__intro {\n      margin-bottom: 40px;\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    margin-bottom: 160px;\n    margin-top: 160px;\n\n    &__list {\n      gap: 70px;\n      justify-content: center;\n      margin-top: 40px;\n      max-width: 1115px;\n\n      &:has(> :nth-child(3)) {\n        justify-content: flex-start;\n      }\n    }\n  }\n}\n", ".rotatingtext {\n  margin-left: auto;\n  margin-right: auto;\n  max-width: $max-text-width;\n  text-align: center;\n\n  h2 {\n    @include community-stats;\n\n    text-shadow: 0 4px 34px #00000040;\n  }\n\n  .italicize {\n    display: inline-block;\n    transform: scale(1) rotate(0deg) translate(-0.01em, 0) skew(14.3deg, 0deg);\n    transform-origin: center;\n  }\n\n  .button {\n    display: inline-block;\n    margin-top: 40px;\n  }\n\n  &__part1 {\n    display: block;\n  }\n\n  &__part2 {\n    display: block;\n    text-indent: 0;\n    margin-bottom: 0;\n    position: relative;\n\n    li {\n      animation: rotatewords 8s ease-in-out infinite 0s;\n      display: inline;\n      left: 0;\n      margin-bottom: 0;\n      position: absolute;\n      opacity: 0;\n      overflow: hidden;\n      white-space: nowrap;\n      width: 100%;\n\n      &:nth-child(1) {\n        position: relative;\n      }\n\n      &:nth-child(2) {\n        animation-delay: 2s;\n      }\n\n      &:nth-child(3) {\n        animation-delay: 4s;\n      }\n\n      &:nth-child(4) {\n        animation-delay: 6s;\n      }\n\n      @keyframes rotatewords {\n        0% {\n          opacity: 0;\n          transform: scale(0.75);\n        }\n\n        0.5% {\n          opacity: 0;\n          transform: scale(0.75);\n        }\n\n        7% {\n          opacity: 1;\n          transform: scale(1);\n        }\n\n        25% {\n          opacity: 1;\n          transform: scale(1);\n        }\n\n        27.5% {\n          opacity: 0;\n          transform: scale(0.75);\n        }\n\n        50% {\n          opacity: 0;\n          transform: scale(0.85);\n        }\n\n        100% {\n          opacity: 0;\n          transform: scale(0.85);\n        }\n      }\n    }\n  }\n\n  &__part3 {\n    display: block;\n  }\n\n  @include breakpoint(tablet, min) {\n    padding-left: $desktop-gutter;\n    padding-right: $desktop-gutter;\n\n    .button {\n      margin-top: 50px;\n    }\n\n    &__part2 {\n      margin-bottom: 0;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    .button {\n      margin-top: 60px;\n    }\n\n    &__part2 {\n      margin-bottom: 0;\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    max-width: $max-title-width;\n  }\n}\n", ".searchform {\n  height: 385px;\n  padding-top: 100px;\n  position: relative;\n  margin-top: -170px;\n  width: 100%;\n\n  form {\n    align-items: center;\n    background-color: $patriotic-red;\n    display: flex;\n    flex-direction: column;\n    height: 220px;\n    justify-content: center;\n    left: 25px;\n    padding: 22px;\n    position: absolute;\n    top: 0;\n    width: calc(100% - 50px);\n  }\n\n  &-info {\n    background-color: $patriotic-red-light;\n    height: 212px;\n    padding: 145px 26px 42px;\n    width: 100%;\n\n    &_count {\n      color: $crt-black;\n      font-size: 12px;\n      font-style: italic;\n    }\n  }\n\n  input[type='text'] {\n    &:not([id^='acf']) {\n      @include input($color: $white, $active-color: $electoral-blue);\n\n      text-overflow: ellipsis;\n      width: 100%;\n\n      &::placeholder {\n        color: $white;\n      }\n    }\n  }\n\n  input[type='submit']:not(.button-secondary),\n  input[type='submit']:not([id^='acf']) {\n    background-color: $electoral-blue;\n    border-color: $electoral-blue;\n    color: #fff;\n    margin-top: 40px;\n    width: 100%;\n\n    &:hover,\n    &:focus {\n      background-color: $electoral-blue-dark;\n      border-color: $electoral-blue-dark;\n      font-style: italic;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    form {\n      flex-direction: row;\n      max-width: 1115px;\n\n      @include horizontal-align;\n    }\n\n    &-info {\n      padding: 145px 42px 0;\n\n      &_count {\n        display: block;\n        font-size: 14px;\n        margin: 0 auto;\n        max-width: 1115px;\n      }\n    }\n\n    input[type='text'] {\n      &:not([id^='acf']) {\n        margin-top: 0;\n        padding-top: 0;\n        width: 65%;\n      }\n    }\n\n    input[type='submit']:not(.button-secondary),\n    input[type='submit']:not([id^='acf']) {\n      margin-left: 56px;\n      margin-top: 0;\n      width: 16.5%;\n    }\n  }\n}\n", ".searchresults {\n  margin: 50px auto;\n  max-width: 814px;\n  padding: 0 25px;\n  width: 100%;\n\n  &_list {\n    list-style: none;\n\n    &-item {\n      border-bottom: 1px solid $golden-rod;\n      display: block;\n      margin-bottom: 50px;\n      padding-bottom: 50px;\n\n      &-headline {\n        color: $crt-black;\n        display: block;\n        leading-trim: both;\n        text-edge: cap;\n        font-family: $inter-font;\n        font-size: 22px;\n        font-style: normal;\n        font-weight: 800;\n        line-height: 125%; /* 37.5px */\n        margin-bottom: 24px;\n        margin-top: 24px;\n\n        @include breakpoint(tablet, min) {\n          font-size: 26px;\n        }\n\n        @include breakpoint(desktop, min) {\n          font-size: 30px;\n        }\n      }\n\n      &-copy {\n        color: $crt-black;\n        display: block;\n        leading-trim: both;\n        text-edge: cap;\n        font-family: $inter-font;\n        font-size: 16px;\n        font-style: normal;\n        font-weight: 400;\n        line-height: 125%; /* 37.5px */\n        margin-bottom: 24px;\n      }\n\n      &-cta {\n        color: $electoral-blue;\n        display: inline-block;\n        leading-trim: both;\n        text-edge: cap;\n        font-family: $inter-font;\n        font-size: 18px;\n        font-style: normal;\n        font-weight: 580;\n        line-height: 125%; /* 22.5px */\n        position: relative;\n\n        &::after {\n          content: '';\n          background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='13' fill='none'%3E%3Cpath stroke='%23160FCC' stroke-width='4' d='m2 1.5 5 5-5 5'/%3E%3C/svg%3E\");\n          height: 13px;\n          position: absolute;\n          right: -18px;\n          top: 6px;\n          width: 10px;\n        }\n      }\n\n      &-eyebrow {\n        color: $patriotic-red;\n        display: block;\n        leading-trim: both;\n        text-edge: cap;\n        font-family: $inter-font;\n        font-size: 12px;\n        font-style: normal;\n        font-weight: 400;\n        line-height: 155%; /* 18.6px */\n      }\n\n      &-link {\n        display: block;\n\n        /* stylelint-disable */\n        &:hover,\n        &:focus {\n          .searchresults_list-item-cta {\n            color: $patriotic-red;\n            font-style: italic;\n\n            &::after {\n              content: '';\n              background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='18' height='14' fill='none'%3E%3Cpath stroke='%23f65137' stroke-width='4' d='m2 2 5 5-5 5M10 2l5 5-5 5'/%3E%3C/svg%3E\");\n              height: 13px;\n              position: absolute;\n              right: -26px;\n              top: 6px;\n              width: 18px;\n            }\n          }\n        }\n        /* stylelint-enable */\n      }\n    }\n  }\n\n  .heading4 {\n    @include h4;\n  }\n}\n", ".sectionbackground {\n  padding-bottom: 60px;\n  position: relative;\n\n  .generichero--transparent {\n    padding-bottom: 0;\n    margin-bottom: 30px;\n  }\n\n  &__bg-swirl-wrap {\n    align-items: center;\n    display: flex;\n    flex-direction: column;\n    inset: 0;\n    justify-content: center;\n    position: absolute;\n    overflow: hidden;\n  }\n\n  &__bg-swirl {\n    left: 0;\n    position: absolute;\n    right: 0;\n    min-width: 200%;\n    min-height: 100%;\n  }\n\n  &__bg-wavy-wrap {\n    align-items: center;\n    display: flex;\n    flex-direction: column;\n    inset: 0;\n    justify-content: center;\n    position: absolute;\n    overflow: hidden;\n  }\n\n  &__bg-wavy {\n    left: 0;\n    position: absolute;\n    right: 0;\n    min-width: 200%;\n    min-height: 100%;\n  }\n\n  &__content {\n    padding-left: $mobile-gutter;\n    padding-right: $mobile-gutter;\n    position: relative;\n    z-index: 1;\n\n    &\n      > *:not(\n        .wide,\n        .alignwide,\n        .full,\n        .alignfull,\n        .title-wide,\n        .block-editor-inner-blocks,\n        .sectionbackgroundimage,\n        .testimonial--half\n      ) {\n      max-width: $max-text-width;\n      margin-left: auto;\n      margin-right: auto;\n    }\n\n    & > .title-wide {\n      max-width: $max-title-width;\n      margin-left: auto;\n      margin-right: auto;\n    }\n\n    & > .wide,\n    .alignwide {\n      max-width: $max-wide-width;\n      margin-left: auto;\n      margin-right: auto;\n    }\n\n    & > .full,\n    .alignfull {\n      max-width: calc(100% + $mobile-gutter + $mobile-gutter);\n      margin-left: -$mobile-gutter;\n      width: calc(100% + $mobile-gutter + $mobile-gutter);\n    }\n  }\n\n  .sectionbackgroundimage {\n    color: var(--text-color);\n    position: relative;\n    z-index: 2;\n\n    &::after {\n      background: linear-gradient(\n        180deg,\n        rgba($crt-black, 0%) 50%,\n        rgba($crt-black, 50%) 75%,\n        rgba($crt-black, 70%) 100%\n      );\n      clip-path: $pixel-tips-mobile;\n      content: '';\n      inset: 0;\n      position: absolute;\n      z-index: 0;\n    }\n\n    &__icon {\n      color: $golden-rod;\n      position: absolute;\n      transform: translate(var(--tx), var(--ty)) rotate(var(--r));\n      z-index: 1;\n    }\n\n    &--325x255 {\n      margin-bottom: $mobile-gutter;\n      margin-left: auto;\n      margin-right: -$mobile-gutter;\n      max-height: 204px;\n      max-width: 260px;\n\n      .sectionbackgroundimage {\n        &__icon {\n          bottom: 40px;\n          height: 82px;\n          left: -80px;\n          width: 92px;\n        }\n\n        &__image {\n          clip-path: $pixel-tips-mobile;\n          height: 204px;\n          max-height: 204px;\n          object-fit: cover;\n          width: 260px;\n        }\n      }\n    }\n\n    &--440x440 {\n      margin-bottom: 32px;\n      margin-left: -$mobile-gutter;\n      margin-right: auto;\n      max-height: 220px;\n      max-width: 220px;\n\n      .sectionbackgroundimage {\n        &__icon {\n          &--heart {\n            bottom: 30px;\n            height: 57px;\n            right: -30px;\n            width: 67px;\n          }\n\n          &--flag {\n            top: 0%;\n            width: 120px;\n            height: 120px;\n            right: -50%;\n          }\n        }\n\n        &__image {\n          clip-path: $pixel-tips-mobile;\n          height: 220px;\n          max-height: 220px;\n          object-fit: cover;\n          width: 220px;\n        }\n      }\n    }\n\n    &--440x550 {\n      margin-bottom: $mobile-gutter;\n      margin-left: auto;\n      margin-right: 0;\n      max-height: 313px;\n      max-width: 250px;\n\n      .sectionbackgroundimage {\n        &__icon {\n          &--fire {\n            bottom: 10px;\n            height: 60px;\n            left: -40px;\n            width: 60px;\n          }\n\n          &--peace {\n            bottom: -80px;\n            height: 60px;\n            right: 40px;\n            width: 60px;\n          }\n\n          &--thumbsup {\n            display: none;\n            top: -200%;\n            height: 140px;\n            right: -20px;\n            width: 140px;\n          }\n        }\n\n        &__image {\n          clip-path: $pixel-tips-mobile;\n          height: 313px;\n          max-height: 313px;\n          object-fit: cover;\n          width: 250px;\n        }\n      }\n    }\n\n    &__caption {\n      padding-bottom: 6px;\n      padding-left: 16px;\n      padding-right: 16px;\n      position: absolute;\n      bottom: 0;\n      width: 100%;\n      z-index: 1;\n\n      p {\n        font-size: 12px;\n        margin-bottom: 0;\n      }\n    }\n  }\n\n  .statlistsection {\n    position: relative;\n    z-index: 3;\n  }\n\n  @include breakpoint(l-mobile, min) {\n    .sectionbackgroundimage {\n      &--440x550 {\n        margin-top: -150px;\n      }\n\n      &--325x255 {\n        max-height: 255px;\n        max-width: 325px;\n\n        .sectionbackgroundimage {\n          &__image {\n            height: 255px;\n            max-height: 255px;\n            width: 325px;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    padding-bottom: 70px;\n\n    &__content {\n      padding-left: $tablet-gutter;\n      padding-right: $tablet-gutter;\n\n      & > .wide,\n      .alignwide {\n        &:not(.title-wide) {\n          margin-left: calc($tablet-gutter / -2);\n          width: calc(100% + $tablet-gutter);\n        }\n      }\n\n      & > .full,\n      .alignfull {\n        max-width: calc(100% + $tablet-gutter + $tablet-gutter);\n        margin-left: -$tablet-gutter;\n        width: calc(100% + $tablet-gutter + $tablet-gutter);\n      }\n\n      & > .title-wide {\n        max-width: $max-title-width;\n      }\n\n      & > :last-child {\n        margin-bottom: 0;\n      }\n    }\n\n    .sectionbackgroundimage {\n      &--440x440 {\n        margin-left: auto;\n        margin-right: 0;\n        margin-top: -160px;\n\n        .sectionbackgroundimage {\n          &__icon {\n            &--flag {\n              right: 0;\n              top: -80px;\n            }\n          }\n        }\n      }\n\n      &--440x550 {\n        margin: -100px auto -220px 0;\n\n        .sectionbackgroundimage {\n          &__icon {\n            &--thumbsup {\n              display: block;\n              right: -200%;\n              top: -175%;\n            }\n          }\n        }\n      }\n\n      &--325x255 {\n        margin-bottom: -200px;\n        max-height: 204px;\n        max-width: 260px;\n\n        .sectionbackgroundimage {\n          &__image {\n            height: 204px;\n            max-height: 204px;\n            width: 260px;\n          }\n        }\n      }\n    }\n\n    .statlistsection {\n      &__heading {\n        max-width: calc(100% - 300px);\n      }\n\n      .statlist {\n        li {\n          &:first-of-type {\n            max-width: calc(100% - 250px);\n          }\n        }\n      }\n    }\n\n    .testimonial--half {\n      max-width: 55%;\n\n      &.right,\n      &.alignright {\n        margin-left: auto;\n        margin-right: 0;\n      }\n\n      &.left,\n      &.alignleft {\n        margin-left: 0;\n        margin-right: auto;\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    padding-bottom: 80px;\n\n    &__content {\n      padding-left: $desktop-gutter;\n      padding-right: $desktop-gutter;\n\n      & > .full,\n      .alignfull {\n        max-width: calc(100% + $desktop-gutter + $desktop-gutter);\n        margin-left: -$desktop-gutter;\n        width: calc(100% + $desktop-gutter + $desktop-gutter);\n      }\n\n      & > .wide,\n      .alignwide {\n        &:not(.title-wide) {\n          margin-left: calc($desktop-gutter / -2);\n          width: calc(100% + $desktop-gutter);\n        }\n      }\n\n      & > :last-child {\n        margin-bottom: 0;\n      }\n    }\n\n    .sectionbackgroundimage {\n      &--325x255 {\n        margin-bottom: -200px;\n        margin-left: auto;\n        margin-right: -$desktop-gutter;\n        max-height: 245px;\n        max-width: 312px;\n\n        .sectionbackgroundimage {\n          &__image {\n            clip-path: $pixel-tips-desktop;\n            height: 245px;\n            max-height: 245px;\n            width: 312px;\n          }\n\n          &__icon {\n            bottom: unset;\n            height: 102px;\n            left: unset;\n            right: 40px;\n            top: -70px;\n            width: 115px;\n          }\n        }\n      }\n\n      &--440x440 {\n        margin-left: auto;\n        margin-right: 0;\n        margin-top: -160px;\n        max-height: 264px;\n        max-width: 264px;\n\n        .sectionbackgroundimage {\n          &__icon {\n            &--heart {\n              bottom: 50px;\n              height: 71px;\n              left: -50px;\n              width: 84px;\n            }\n\n            &--flag {\n              top: -100%;\n              width: 150px;\n              height: 150px;\n              right: 100px;\n            }\n          }\n\n          &__image {\n            clip-path: $pixel-tips-desktop;\n            height: 264px;\n            max-height: 264px;\n            width: 264px;\n          }\n        }\n      }\n\n      &--440x550 {\n        margin: -100px auto -325px -22px;\n        max-height: 375px;\n        max-width: 300px;\n\n        .sectionbackgroundimage {\n          &__icon {\n            &--fire {\n              bottom: -100px;\n              height: 70px;\n              left: unset;\n              right: -50px;\n              width: 70px;\n            }\n\n            &--peace {\n              bottom: 170px;\n              height: 76px;\n              right: -140%;\n              width: 76px;\n            }\n\n            &--thumbsup {\n              bottom: -40%;\n              height: 160px;\n              left: 0%;\n              top: unset;\n              width: 160px;\n            }\n          }\n\n          &__image {\n            clip-path: $pixel-tips-desktop;\n            height: 375px;\n            max-height: 375px;\n            width: 300px;\n          }\n        }\n      }\n\n      &__caption {\n        padding-bottom: 10px;\n        padding-left: 24px;\n        padding-right: 24px;\n\n        p {\n          font-size: 12px;\n          margin-bottom: 0;\n        }\n      }\n    }\n\n    .testimonial--half {\n      max-width: 565px;\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    padding-bottom: 100px;\n\n    .sectionbackgroundimage {\n      &--440x440 {\n        margin-top: -200px;\n        max-height: 400px;\n        max-width: 400px;\n\n        .sectionbackgroundimage {\n          &__image {\n            height: 400px;\n            max-height: 400px;\n            width: 400px;\n          }\n        }\n      }\n\n      &--440x550 {\n        margin-top: -160px;\n        margin-bottom: -380px;\n        max-height: 500px;\n        max-width: 400px;\n\n        .sectionbackgroundimage {\n          &__image {\n            height: 500px;\n            max-height: 500px;\n            width: 400px;\n          }\n\n          &__icon {\n            &--peace {\n              right: -175%;\n            }\n          }\n        }\n      }\n    }\n\n    .statlistsection {\n      &__heading {\n        max-width: calc(100% - 200px);\n      }\n\n      .statlist {\n        li {\n          &:first-of-type {\n            max-width: calc(100% - 200px);\n          }\n        }\n      }\n    }\n\n    .testimonial--half {\n      &.right {\n        margin-right: 55px;\n      }\n    }\n  }\n\n  @include breakpoint(wide-desktop, min) {\n    &__content {\n      & > .wide,\n      .alignwide {\n        &:not(.title-wide) {\n          margin-left: auto;\n          margin-right: auto;\n          width: 100%;\n        }\n      }\n    }\n\n    .sectionbackgroundimage {\n      &--325x255 {\n        margin-bottom: -200px;\n        margin-left: auto;\n        margin-right: -$desktop-gutter;\n        max-height: 255px;\n        max-width: 325px;\n\n        .sectionbackgroundimage {\n          &__image {\n            height: 255px;\n            max-height: 255px;\n            width: 325px;\n          }\n        }\n      }\n\n      &--440x440 {\n        margin-left: auto;\n        margin-right: calc((100vw - $max-wide-width) / 2 - $desktop-gutter);\n        margin-top: -200px;\n        max-height: 440px;\n        max-width: 440px;\n\n        .sectionbackgroundimage {\n          &__image {\n            height: 440px;\n            max-height: 440px;\n            width: 440px;\n          }\n        }\n      }\n\n      &--440x550 {\n        margin-top: -130px;\n        margin-bottom: -450px;\n        margin-left: calc((100vw - $max-wide-width) / 2 - $desktop-gutter);\n        max-height: 550px;\n        max-width: 440px;\n\n        .sectionbackgroundimage {\n          &__image {\n            height: 550px;\n            max-height: 550px;\n            width: 440px;\n          }\n\n          &__icon {\n            &--peace {\n              right: -175%;\n            }\n          }\n        }\n      }\n    }\n\n    .testimonial--half {\n      &.right {\n        margin-right: calc((100vw - $max-wide-width) / 2 + 25px);\n      }\n\n      &.left {\n        margin-right: calc((100vw - $max-wide-width) / 2);\n      }\n    }\n  }\n\n  @include breakpoint(xl-desktop, min) {\n    .title-wide {\n      transform: translateX(-14px);\n    }\n  }\n}\n", ".sectiontitle {\n  background: var(--bg-color);\n  padding: px-to-rem(25px) px-to-rem(20px);\n\n  $self: &;\n\n  &__button {\n    display: inline-block;\n    width: 100%;\n  }\n\n  &__title {\n    color: var(--heading-color);\n    margin-bottom: 20px;\n  }\n\n  &--image {\n    display: grid;\n    margin-top: 20px;\n    padding-top: 0;\n\n    #{$self} {\n      &__image {\n        grid-row: 1;\n        margin-bottom: 24px;\n        margin-top: -20px;\n      }\n    }\n\n    &#{$self} {\n      &--imagenooverflow {\n        padding-left: 0;\n        padding-right: 0;\n\n        #{$self} {\n          &__image {\n            margin-top: 0;\n          }\n\n          &__content {\n            padding-left: px-to-rem(20px);\n            padding-right: px-to-rem(20px);\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(m-mobile, min) {\n    &__button {\n      width: fit-content;\n    }\n\n    &--image {\n      align-items: flex-end;\n      grid-template-columns: 60% 40%;\n      margin-top: 0;\n      padding: 0;\n\n      #{$self} {\n        &__content {\n          grid-column: 1;\n          grid-row: 1;\n          padding: px-to-rem(25px) px-to-rem(20px);\n        }\n\n        &__image {\n          grid-column: 2;\n          grid-row: 1;\n          margin-bottom: 0;\n          max-height: calc(100% + 20px);\n        }\n      }\n\n      &#{$self} {\n        &--imagenooverflow {\n          align-items: center;\n\n          #{$self} {\n            &__image {\n              padding: px-to-rem(25px) px-to-rem(20px) px-to-rem(25px) 0;\n            }\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    padding: px-to-rem(40px) px-to-rem(50px);\n\n    &__title {\n      margin-bottom: 30px;\n    }\n\n    &--image {\n      align-items: flex-end;\n      grid-template-columns: 60% 40%;\n      margin-top: 0;\n      padding: 0;\n\n      #{$self} {\n        &__content {\n          grid-column: 1;\n          grid-row: 1;\n          padding: px-to-rem(40px) px-to-rem(20px) px-to-rem(40px)\n            px-to-rem(50px);\n        }\n\n        &__image {\n          grid-column: 2;\n          grid-row: 1;\n          margin-bottom: 0;\n        }\n      }\n\n      &#{$self} {\n        &--imagenooverflow {\n          #{$self} {\n            &__content {\n              padding: px-to-rem(40px) px-to-rem(20px) px-to-rem(40px)\n                px-to-rem(50px);\n            }\n\n            &__image {\n              padding: px-to-rem(40px) px-to-rem(50px) px-to-rem(40px) 0;\n            }\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    padding: px-to-rem(55px) px-to-rem(55px);\n\n    &__title {\n      margin-bottom: 30px;\n    }\n\n    &--image {\n      padding: 0;\n      grid-template-columns: 60% 40%;\n\n      #{$self} {\n        &__content {\n          padding: px-to-rem(55px) px-to-rem(20px) px-to-rem(55px)\n            px-to-rem(55px);\n        }\n      }\n\n      &#{$self} {\n        &--imagenooverflow {\n          #{$self} {\n            &__content {\n              padding: px-to-rem(55px) px-to-rem(20px) px-to-rem(55px)\n                px-to-rem(55px);\n            }\n\n            &__image {\n              padding: px-to-rem(55px) px-to-rem(55px) px-to-rem(55px) 0;\n            }\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    &--image {\n      padding: 0;\n      grid-template-columns: 60% 40%;\n\n      #{$self} {\n        &__content {\n          padding: px-to-rem(55px) px-to-rem(20px) px-to-rem(55px)\n            px-to-rem(55px);\n        }\n      }\n\n      &#{$self} {\n        &--imagenooverflow {\n          #{$self} {\n            &__content {\n              padding: px-to-rem(55px) px-to-rem(20px) px-to-rem(55px)\n                px-to-rem(55px);\n            }\n\n            &__image {\n              padding: px-to-rem(55px) px-to-rem(55px) px-to-rem(55px) 0;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n", ".schema-faq {\n  @include body;\n\n  a {\n    @include inline-link;\n  }\n\n  &-question {\n    display: block;\n    margin-bottom: px-to-rem(30px);\n    margin-top: px-to-rem(40px);\n\n    a {\n      @include inline-link;\n    }\n  }\n\n  &-answer {\n    margin-bottom: px-to-rem(40px);\n\n    br {\n      content: '';\n      display: block;\n      margin-bottom: $body-margin-mobile;\n    }\n\n    a {\n      @include inline-link;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    &-answer {\n      margin-bottom: px-to-rem(50px);\n\n      br {\n        margin-bottom: $body-margin-tablet;\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-bottom: px-to-rem(60px);\n\n    &-answer {\n      br {\n        margin-bottom: $body-margin-desktop;\n      }\n    }\n  }\n}\n", ".signupcta {\n  margin-bottom: 50px;\n  margin-top: 20px;\n  position: relative;\n  width: 100%;\n\n  &__wrapper {\n    padding: 27px $mobile-gutter 8px $mobile-gutter;\n    overflow: hidden;\n    width: 100%;\n  }\n\n  &__wave-left {\n    bottom: 0;\n    height: 160px;\n    left: -$mobile-gutter;\n    position: absolute;\n    width: 320px;\n    z-index: 1;\n  }\n\n  &__wave-right {\n    top: 0;\n    height: 126px;\n    right: 0;\n    position: absolute;\n    width: 260px;\n    z-index: 1;\n  }\n\n  &__content {\n    background-color: var(--bg-color);\n    color: var(--text-color);\n    padding: 100px 30px 135px;\n    width: 100%;\n\n    h2 {\n      @include h1;\n\n      margin-bottom: 0;\n      margin-top: 0;\n      order: 2;\n    }\n\n    p {\n      @include h4;\n\n      order: 1;\n      margin-bottom: 5px;\n    }\n  }\n\n  .raise {\n    position: relative;\n    z-index: 2;\n  }\n\n  &__form {\n    margin-right: 0;\n    margin-left: auto;\n    max-width: 100%;\n    position: relative;\n    width: 100%;\n    z-index: 2;\n\n    .form__error-msg {\n      &::after {\n        top: 12px;\n      }\n    }\n  }\n\n  &__post-submit:not(.hidden) {\n    align-items: flex-start;\n    color: $golden-rod;\n    display: flex;\n    flex-direction: column;\n    margin-top: 40px;\n  }\n\n  &__icon {\n    color: $golden-rod;\n    position: absolute;\n    transform: translate(var(--tx), var(--ty)) rotate(var(--r));\n    will-change: transform;\n\n    &--email {\n      bottom: 40px;\n      height: 35px;\n      right: 5px;\n      width: 50px;\n    }\n\n    &--handwritten {\n      bottom: 90px;\n      height: 50px;\n      right: 40px;\n      width: 55px;\n    }\n  }\n\n  &__max {\n    max-width: $max-title-width;\n    margin: 0 auto;\n    width: 100%;\n  }\n\n  &__text {\n    display: flex;\n    flex-direction: column;\n  }\n\n  &__form-button {\n    margin-top: 30px;\n  }\n\n  .form__error-msg {\n    margin-bottom: 0;\n    margin-top: 16px;\n\n    &::after {\n      z-index: 2;\n    }\n  }\n\n  .input-group {\n    transition: background 0.3s ease-in-out;\n\n    &:focus-within {\n      background-color: var(--bg-color);\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    &__wrapper {\n      padding: 41px $tablet-gutter 12px $tablet-gutter;\n    }\n\n    p {\n      margin-bottom: 0;\n    }\n\n    &__wave-left {\n      bottom: 1px;\n      height: 217px;\n      left: -$tablet-gutter;\n      width: 450px;\n    }\n\n    &__wave-right {\n      height: 189px;\n      width: 390px;\n    }\n\n    &__content {\n      display: grid;\n      padding: 120px 40px 155px;\n\n      h2 {\n        margin-bottom: 0;\n        margin-top: 0;\n      }\n    }\n  }\n\n  @include breakpoint(nav-desktop, min) {\n    &__wrapper {\n      padding: 54px $tablet-gutter 16px $tablet-gutter;\n    }\n\n    &__content {\n      padding: 150px 40px 155px;\n    }\n\n    &__form-button {\n      margin-top: 40px;\n    }\n\n    &__form {\n      margin-left: auto;\n      margin-right: 0;\n      max-width: 365px;\n    }\n\n    &__post-submit:not(.hidden) {\n      align-items: center;\n      height: 100%;\n      justify-content: center;\n      margin-bottom: 0;\n      margin-top: -50px;\n    }\n\n    &__max {\n      display: grid;\n      grid-template-columns: min(50%, calc(95% - 365px)) min(45%, 365px);\n      column-gap: 5%;\n    }\n\n    &__wave-left {\n      height: 290px;\n      left: -$tablet-gutter;\n      width: 600px;\n    }\n\n    &__wave-right {\n      height: 252px;\n      width: 520px;\n    }\n\n    &__icon {\n      &--email {\n        bottom: 125px;\n        height: 47px;\n        right: 15px;\n        width: 69px;\n      }\n\n      &--handwritten {\n        bottom: 32px;\n        height: 65px;\n        right: 110px;\n        width: 75px;\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    &__wrapper {\n      padding: 56px $desktop-gutter 17px;\n    }\n\n    &__content {\n      padding: 115px $desktop-gutter 55px $desktop-gutter;\n\n      h2 {\n        margin-bottom: 0;\n        margin-top: 0;\n      }\n    }\n\n    &__form-button {\n      margin-top: 50px;\n    }\n\n    &__text {\n      padding-bottom: 140px;\n    }\n\n    &__wave-right {\n      top: 0;\n      height: 264px;\n      right: 0;\n      width: 545px;\n    }\n\n    &__wave-left {\n      bottom: 1px;\n      height: 306px;\n      left: -$desktop-gutter;\n      width: 630px;\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    &__wrapper {\n      padding: 56px $desktop-gutter 22px;\n    }\n\n    &__max {\n      grid-template-columns: calc(95% - 365px) 365px;\n    }\n\n    &__content {\n      padding: 135px 100px 75px 90px;\n    }\n\n    &__wave-left {\n      height: 397px;\n      left: 0;\n      width: 700px;\n    }\n  }\n\n  @include breakpoint(xl-desktop, min) {\n    &__wrapper {\n      padding: 56px 100px 25px;\n    }\n\n    &__content {\n      padding: 135px 120px 75px 90px;\n    }\n\n    &__text {\n      padding-bottom: 170px;\n    }\n\n    &__wave-left {\n      height: 459px;\n      width: 810px;\n    }\n\n    &__icon {\n      &--email {\n        bottom: 130px;\n        right: 70px;\n      }\n\n      &--handwritten {\n        bottom: 32px;\n        right: 175px;\n      }\n    }\n  }\n\n  @media (prefers-reduced-motion) {\n    &__icon {\n      transform: none;\n    }\n  }\n}\n", ".simplecta {\n  margin-bottom: 45px;\n  margin-top: 45px;\n  position: relative;\n  z-index: 1;\n\n  $self: &;\n\n  h2 {\n    @include h3;\n\n    text-transform: none;\n  }\n\n  h1,\n  h2,\n  h3,\n  h4,\n  h5,\n  h6 {\n    color: var(--text-color);\n    margin-bottom: 24px;\n    text-align: left;\n\n    &.is-style-heading-1 {\n      @include h1;\n\n      color: var(--text-color);\n    }\n\n    &.is-style-heading-2 {\n      @include h2;\n\n      color: var(--text-color);\n    }\n\n    &.is-style-heading-3 {\n      @include h3;\n\n      color: var(--text-color);\n      text-transform: none;\n    }\n\n    &.is-style-heading-4 {\n      @include h4;\n\n      color: var(--text-color);\n      text-transform: none;\n    }\n\n    &.is-style-heading-5 {\n      @include h5;\n\n      color: var(--text-color);\n      text-transform: none;\n    }\n\n    &.is-style-heading-6 {\n      @include h6;\n\n      color: var(--text-color);\n    }\n  }\n\n  p {\n    margin-bottom: 24px;\n    text-align: left;\n  }\n\n  .button {\n    width: 100%;\n  }\n\n  &__content {\n    border: 6px solid var(--border-color);\n    color: var(--text-color);\n    position: relative;\n  }\n\n  &__text {\n    padding: 22px;\n  }\n\n  &__image {\n    clip-path: $pixel-tips-mobile;\n    left: -21px;\n    position: relative;\n    max-width: calc(100% + 42px);\n    object-fit: cover;\n    object-position: var(--image-alignment);\n    top: -21px;\n    z-index: -1;\n    width: calc(100% + 42px);\n  }\n\n  &--list {\n    #{$self} {\n      &__picture {\n        display: block;\n        margin-bottom: -22px;\n      }\n    }\n  }\n\n  &--image {\n    padding: 15px 15px 0;\n\n    &:not(#{$self}--list) {\n      #{$self} {\n        &__text {\n          padding-top: 0;\n        }\n      }\n    }\n  }\n\n  &--small {\n    h2 {\n      @include h4;\n\n      color: var(--text-color);\n    }\n\n    h2,\n    h3,\n    h4,\n    h5 {\n      margin-bottom: 22px;\n    }\n\n    p {\n      margin-bottom: 22px;\n    }\n\n    &#{$self}--image {\n      padding: 12px 12px 0;\n\n      #{$self} {\n        &__text {\n          margin-top: -5px;\n        }\n      }\n    }\n\n    #{$self} {\n      &__text {\n        padding-left: 16px;\n        padding-right: 16px;\n      }\n\n      &__image {\n        left: -18px;\n        max-height: max(180px, 50vw);\n        max-width: calc(100% + 36px);\n        top: -18px;\n        width: calc(100% + 36px);\n      }\n    }\n  }\n\n  @include breakpoint(l-mobile, min) {\n    .button {\n      width: fit-content;\n    }\n\n    &--narrow {\n      margin-left: auto;\n      margin-right: auto;\n      max-width: 764px;\n    }\n\n    &--small {\n      #{$self} {\n        &__text {\n          padding: 40px;\n          min-height: fit-content;\n        }\n      }\n\n      &#{$self}--image:not(#{$self}--list) {\n        padding: 12px 0 12px 12px;\n\n        #{$self} {\n          &__text {\n            margin-bottom: auto;\n            margin-top: auto;\n            min-height: fit-content;\n            padding: 30px;\n          }\n\n          &__image {\n            height: calc(100% + 36px);\n            left: -18px;\n            max-height: calc(100% + 36px);\n            max-width: calc(40% + 18px);\n            position: absolute;\n            top: -18px;\n            width: calc(40% + 18px);\n          }\n\n          &__content {\n            display: grid;\n            grid-template-columns: 40% 60%;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    margin-bottom: 65px;\n    margin-top: 65px;\n\n    &__text {\n      align-items: flex-start;\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      margin-bottom: auto;\n      margin-top: auto;\n      min-height: fit-content;\n      padding: 50px 40px;\n    }\n\n    &--image:not(#{$self}--list) {\n      padding: 15px 0 15px 15px;\n\n      #{$self} {\n        &__text {\n          padding: 32px;\n          min-height: 420px;\n        }\n\n        &__content {\n          display: grid;\n          grid-template-columns: 50% 50%;\n        }\n\n        &__image {\n          height: calc(100% + 42px);\n          left: -21px;\n          max-width: calc(50% + 21px);\n          position: absolute;\n          top: -21px;\n          width: calc(50% + 21px);\n        }\n      }\n    }\n\n    &--small {\n      #{$self} {\n        &__text {\n          padding: 42px;\n          min-height: fit-content;\n        }\n      }\n\n      &#{$self}--image:not(#{$self}--list) {\n        padding: 12px 0 12px 12px;\n\n        #{$self} {\n          &__text {\n            margin-bottom: auto;\n            margin-top: auto;\n            min-height: fit-content;\n            padding: 30px;\n          }\n\n          &__image {\n            height: calc(100% + 36px);\n            left: -18px;\n            max-height: calc(100% + 36px);\n            max-width: calc(40% + 18px);\n            top: -18px;\n            width: calc(40% + 18px);\n          }\n\n          &__content {\n            display: grid;\n            grid-template-columns: 40% 60%;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-bottom: 90px;\n    margin-top: 90px;\n\n    &__content {\n      border: 12px solid var(--border-color);\n    }\n\n    &__text {\n      padding: 48px;\n    }\n\n    h2,\n    h3,\n    h4,\n    h5 {\n      margin-bottom: 40px;\n    }\n\n    p {\n      margin-bottom: 40px;\n    }\n\n    &--image:not(#{$self}--list) {\n      padding: 22px 0 22px 22px;\n\n      #{$self} {\n        &__text {\n          min-height: 566px;\n          padding: 55px;\n        }\n\n        &__content {\n          display: grid;\n          grid-template-columns: 50% 50%;\n        }\n\n        &__image {\n          clip-path: $pixel-tips-desktop;\n          height: calc(100% + 68px);\n          left: -34px;\n          max-width: calc(50% + 34px);\n          top: -34px;\n          width: calc(50% + 34px);\n        }\n      }\n    }\n\n    &--small {\n      h2,\n      h3,\n      h4,\n      h5 {\n        margin-bottom: 26px;\n      }\n\n      p {\n        margin-bottom: 26px;\n      }\n\n      #{$self} {\n        &__text {\n          min-height: fit-content;\n          padding: 42px;\n        }\n      }\n\n      &#{$self}--image:not(#{$self}--list) {\n        padding: 16px 0 16px 16px;\n\n        #{$self} {\n          &__text {\n            margin-top: 0;\n            min-height: fit-content;\n            padding: 30px;\n          }\n\n          &__image {\n            clip-path: $pixel-tips-mobile;\n            height: calc(100% + 56px);\n            left: -28px;\n            max-height: calc(100% + 56px);\n            max-width: calc(35% + 28px);\n            top: -28px;\n            width: calc(35% + 28px);\n          }\n\n          &__content {\n            display: grid;\n            grid-template-columns: 35% 65%;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    &--image:not(#{$self}--list) {\n      #{$self} {\n        &__text {\n          padding: 48px;\n        }\n      }\n    }\n\n    &--small {\n      #{$self} {\n        &__text {\n          padding: 48px;\n        }\n      }\n\n      &#{$self}--image:not(#{$self}--list) {\n        #{$self} {\n          &__content {\n            display: grid;\n            grid-template-columns: 35% 65%;\n          }\n\n          &__image {\n            max-width: calc(35% + 28px);\n            width: calc(35% + 28px);\n          }\n        }\n      }\n    }\n  }\n}\n", ".simplectalist {\n  display: grid;\n  grid-gap: 30px;\n  margin-bottom: 45px;\n  margin-top: 45px;\n\n  &__item {\n    height: 100%;\n    margin-bottom: 0;\n    margin-top: 0;\n\n    .simplecta {\n      &__content {\n        height: 100%;\n      }\n\n      &__text {\n        height: 100%;\n      }\n    }\n\n    &.simplecta {\n      &--image {\n        .simplecta {\n          &__text {\n            height: fit-content;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(l-mobile, min) {\n    grid-template-columns: calc(50% - $mobile-gutter / 2) calc(\n        50% - $mobile-gutter / 2\n      );\n    grid-gap: $mobile-gutter;\n  }\n\n  @include breakpoint(tablet, min) {\n    grid-gap: 35px;\n    grid-template-columns: calc(50% - 17.5px) calc(50% - 17.5px);\n    margin-bottom: 65px;\n    margin-top: 65px;\n  }\n\n  @include breakpoint(desktop, min) {\n    grid-gap: 45px;\n    grid-template-columns: calc(50% - 22.5px) calc(50% - 22.5px);\n    margin-bottom: 90px;\n    margin-top: 90px;\n  }\n}\n", ".simpleform {\n  margin-bottom: 45px;\n  margin-top: 45px;\n  position: relative;\n  z-index: 1;\n\n  $self: &;\n\n  h2 {\n    @include h3;\n\n    margin-bottom: 24px;\n    text-transform: none;\n  }\n\n  p {\n    margin-bottom: 24px;\n\n    &:last-of-type {\n      margin-bottom: 0;\n    }\n  }\n\n  &__image {\n    clip-path: $pixel-tips-mobile;\n    left: -21px;\n    position: relative;\n    max-width: calc(100% + 42px);\n    object-fit: cover;\n    object-position: var(--image-alignment);\n    top: -21px;\n    z-index: -1;\n    width: calc(100% + 42px);\n  }\n\n  &__content {\n    border: 6px solid var(--border-color);\n    display: grid;\n    grid-template-columns: 100%;\n    color: var(--text-color);\n    grid-gap: 22px;\n    padding: 22px;\n    position: relative;\n  }\n\n  &__form-button {\n    margin-top: 30px;\n  }\n\n  &__form {\n    width: 100%;\n\n    label {\n      color: var(--text-color);\n    }\n\n    .input-group.error .input-group__error-msg {\n      margin-top: 16px;\n      margin-bottom: 0;\n\n      &::after {\n        top: 14px;\n      }\n    }\n  }\n\n  &__post-submit:not(.hidden) {\n    align-items: flex-start;\n    color: var(--text-color);\n    display: flex;\n    flex-direction: column;\n    margin-top: 40px;\n  }\n\n  &--image {\n    padding: 15px 15px 0;\n\n    #{$self} {\n      &__text {\n        padding: 0 22px 22px;\n      }\n\n      &__content {\n        display: block;\n        grid-gap: 0;\n        padding: 0;\n\n        h2 + div.form {\n          margin-top: -28px;\n        }\n      }\n\n      &__post-submit:not(.hidden) {\n        align-items: flex-start;\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    margin-bottom: 65px;\n    margin-top: 65px;\n\n    &__content {\n      padding: 50px 40px;\n      grid-template-columns: calc(50% - 25px) calc(50% - 25px);\n      grid-gap: 50px;\n    }\n\n    &__post-submit:not(.hidden) {\n      align-items: center;\n      justify-content: center;\n      margin-bottom: 40px;\n    }\n\n    &__text {\n      align-items: flex-start;\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n      margin-bottom: auto;\n      margin-top: auto;\n      min-height: fit-content;\n    }\n\n    &--image {\n      padding: 15px 0 15px 15px;\n\n      #{$self} {\n        &__text {\n          padding: 32px;\n          min-height: 420px;\n        }\n\n        &__content {\n          display: grid;\n          grid-template-columns: 50% 50%;\n        }\n\n        &__image {\n          height: calc(100% + 42px);\n          left: -21px;\n          max-width: calc(50% + 21px);\n          position: absolute;\n          top: -21px;\n          width: calc(50% + 21px);\n        }\n\n        &__post-submit:not(.hidden) {\n          align-items: flex-start;\n        }\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-bottom: 90px;\n    margin-top: 90px;\n\n    &__content {\n      border: 12px solid var(--border-color);\n      padding: 48px 68px;\n      grid-gap: 70px;\n      grid-template-columns: calc(50% - 35px) calc(50% - 35px);\n    }\n\n    &__form {\n      .input-group.error .input-group__error-msg {\n        &::after {\n          right: -40px;\n        }\n      }\n    }\n\n    &__form-button {\n      margin-top: 40px;\n    }\n\n    h2 {\n      margin-bottom: 40px;\n    }\n\n    p {\n      margin-bottom: 40px;\n    }\n\n    &--image {\n      padding: 22px 0 22px 22px;\n\n      #{$self} {\n        &__text {\n          min-height: 566px;\n          padding: 55px;\n        }\n\n        &__content {\n          display: grid;\n          grid-template-columns: 50% 50%;\n        }\n\n        &__image {\n          clip-path: $pixel-tips-desktop;\n          height: calc(100% + 68px);\n          left: -34px;\n          max-width: calc(50% + 34px);\n          top: -34px;\n          width: calc(50% + 34px);\n        }\n      }\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    &__content {\n      grid-gap: 90px;\n      grid-template-columns: calc(50% - 45px) calc(50% - 45px);\n      padding: 48px 80px;\n    }\n\n    &--image {\n      #{$self} {\n        &__text {\n          padding: 48px;\n        }\n      }\n    }\n  }\n}\n", ".stateselector {\n  position: relative;\n  z-index: 2;\n\n  $self: &;\n\n  &__container {\n    position: relative;\n    margin-top: 10px;\n    width: 100%;\n  }\n\n  &__input {\n    @include h5;\n\n    border-style: solid;\n    border-width: 6px;\n    display: block;\n    line-height: 100%;\n    max-height: 63px;\n    min-height: 60px;\n    padding: 14px 55px 12px 20px;\n    width: 100%;\n  }\n\n  &__dropdown-arrow {\n    height: 42px;\n    position: absolute;\n    right: 11px;\n    top: 10px;\n    width: 42px;\n\n    svg {\n      pointer-events: none;\n      transition: transform 0.3s linear;\n    }\n\n    &.expanded {\n      svg {\n        transform: rotate(-180deg);\n      }\n    }\n  }\n\n  &__select {\n    inset: 0;\n    height: 100%;\n    opacity: 0;\n    position: absolute;\n    width: 100%;\n  }\n\n  &__results {\n    border-bottom-width: 6px;\n    border-left-width: 6px;\n    border-right-width: 6px;\n    border-bottom-style: solid;\n    border-left-style: solid;\n    border-right-style: solid;\n    margin-top: 0;\n    max-height: 300px;\n    overflow-y: auto;\n    padding: 0;\n    position: absolute;\n    top: 100%;\n    visibility: hidden;\n    width: 100%;\n\n    &.visible {\n      visibility: visible;\n    }\n\n    & > li {\n      @include h5;\n      @include click-cursor;\n\n      padding: 0 22px;\n\n      span {\n        border-top-style: solid;\n        border-top-width: 2px;\n        display: block;\n        padding: 18px 30px;\n      }\n\n      &:first-of-type {\n        span {\n          border-top-width: 0;\n        }\n      }\n\n      &:hover,\n      &:focus {\n        border: none;\n\n        span {\n          border-top-width: 0;\n          font-style: italic;\n        }\n\n        & + li {\n          span {\n            border-top-width: 0;\n          }\n        }\n      }\n    }\n  }\n\n  &--yellow {\n    #{$self} {\n      &__input {\n        background-color: $crt-black;\n        border-color: $golden-rod;\n        color: $white;\n\n        &:focus {\n          background-color: $crt-black-light;\n        }\n      }\n\n      &__dropdown-arrow {\n        background-color: $golden-rod;\n      }\n\n      &__results {\n        background-color: $crt-black;\n        border-color: $golden-rod;\n\n        &::-webkit-scrollbar {\n          width: 1em;\n        }\n\n        &::-webkit-scrollbar-track {\n          box-shadow: inset 0 0 6px rgb(0 0 0 / 30%);\n        }\n\n        &::-webkit-scrollbar-thumb {\n          background-color: $golden-rod;\n          outline: 2px solid transparent;\n        }\n\n        & > li {\n          color: $golden-rod;\n\n          &:hover,\n          &:focus {\n            background-color: $golden-rod;\n            border-color: $golden-rod;\n            color: $crt-black;\n          }\n        }\n      }\n    }\n  }\n\n  &--blueyellow {\n    /* stylelint-disable */\n    input {\n      &::placeholder,\n      &::-webkit-input-placeholder {\n        color: $white;\n      }\n    }\n    /* stylelint-enable */\n\n    #{$self} {\n      &__input {\n        background-color: $electoral-blue;\n        border-color: $golden-rod;\n        color: $white;\n\n        &:focus {\n          background-color: $electoral-blue-light;\n        }\n      }\n\n      &__dropdown-arrow {\n        background-color: $golden-rod;\n      }\n\n      &__results {\n        background-color: $electoral-blue;\n        border-color: $golden-rod;\n\n        &::-webkit-scrollbar {\n          width: 1em;\n        }\n\n        &::-webkit-scrollbar-track {\n          box-shadow: inset 0 0 6px rgb(0 0 0 / 30%);\n        }\n\n        &::-webkit-scrollbar-thumb {\n          background-color: $golden-rod;\n          outline: 2px solid transparent;\n        }\n\n        & > li {\n          color: $golden-rod;\n\n          &:hover,\n          &:focus {\n            background-color: $golden-rod;\n            border-color: $golden-rod;\n            color: $electoral-blue;\n          }\n        }\n      }\n    }\n  }\n\n  &--blue {\n    #{$self} {\n      &__label {\n        color: $crt-black;\n      }\n\n      &__input {\n        background-color: $golden-rod;\n        border-color: $electoral-blue;\n        color: $crt-black;\n\n        &:focus {\n          background-color: $golden-rod-light;\n        }\n      }\n\n      &__dropdown-arrow {\n        background-color: $electoral-blue;\n        color: $golden-rod;\n      }\n\n      &__results {\n        background-color: $golden-rod;\n        border-color: $electoral-blue;\n\n        &::-webkit-scrollbar {\n          width: 1em;\n        }\n\n        &::-webkit-scrollbar-track {\n          box-shadow: inset 0 0 6px rgb(0 0 0 / 30%);\n        }\n\n        &::-webkit-scrollbar-thumb {\n          background-color: $electoral-blue;\n          outline: 2px solid transparent;\n        }\n\n        & > li {\n          color: $electoral-blue;\n\n          &:hover,\n          &:focus {\n            background-color: $electoral-blue;\n            border-color: $electoral-blue;\n            color: $white;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    &__select {\n      display: none;\n    }\n  }\n}\n", ".statlist {\n  color: var(--text-color);\n\n  .stat {\n    @include community-stats;\n\n    &--note {\n      .stat__stat {\n        display: block;\n      }\n    }\n\n    margin-bottom: 20px;\n\n    &__title {\n      display: inline-block;\n      text-shadow: 0 4px 34px #00000040;\n    }\n\n    &__stat {\n      display: inline-block;\n      text-shadow: 0 4px 34px #00000040;\n      transition: all 1s cubic-bezier(0.3333, 0.6667, 0.6667, 1);\n    }\n\n    &__note {\n      @include h6;\n\n      color: var(--note-color);\n      display: block;\n      margin-left: 0;\n      margin-right: auto;\n      max-width: 525px;\n      padding-left: 0;\n      padding-right: 30px;\n      padding-top: 20px;\n      position: relative;\n    }\n\n    &__arrow {\n      height: 90px;\n      color: var(--note-color);\n      right: -5px;\n      position: absolute;\n      stroke-dasharray: 250;\n      stroke-dashoffset: 250;\n      top: -70px;\n      width: 40px;\n    }\n\n    &[data-visible='true'] {\n      .stat {\n        &__stat {\n          transform: scale(1) rotate(0deg) translate(-0.01em, 0)\n            skew(14.3deg, 0deg);\n          transform-origin: center;\n          transition-delay: 1s;\n        }\n\n        &__arrow {\n          .line {\n            animation: dasharrow 1.5s ease-in 0.5s;\n            animation-fill-mode: forwards;\n          }\n\n          .head {\n            animation: dasharrow 1s ease-in 1.5s;\n            animation-fill-mode: forwards;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(nav-tablet, min) {\n    .stat {\n      &--note {\n        .stat__stat {\n          display: inline-block;\n        }\n      }\n\n      &__note {\n        padding-left: 25px;\n        padding-top: 30px;\n      }\n\n      &__arrow {\n        height: 70px;\n        left: -18px;\n        right: unset;\n        position: absolute;\n        top: -18px;\n        transform: scaleX(-1);\n        width: 30px;\n      }\n\n      &__title {\n        display: inline-block;\n      }\n\n      &__stat {\n        display: inline-block;\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    .stat {\n      margin-bottom: 5px;\n\n      &[data-visible='true'] {\n        .stat {\n          &__stat {\n            transform: scale(1) rotate(0deg) translate(0, 0) skew(0deg, 0deg);\n            transition-delay: 0s;\n          }\n        }\n\n        &:hover {\n          .stat {\n            &__stat {\n              transform: scale(1) rotate(0deg) translate(-0.01em, 0)\n                skew(14.3deg, 0deg);\n              transform-origin: center;\n            }\n          }\n        }\n      }\n    }\n  }\n}\n\n@keyframes dasharrow {\n  to {\n    stroke-dashoffset: 0;\n  }\n}\n", ".statlistsection {\n  color: var(--heading-color);\n  margin-bottom: 40px;\n  margin-top: 40px;\n  max-width: $max-title-width;\n\n  &__heading {\n    @include h4;\n\n    margin-bottom: 20px;\n    margin-left: 0;\n    margin-right: auto;\n    max-width: $max-text-width;\n  }\n\n  @include breakpoint(tablet, min) {\n    &__heading {\n      margin-bottom: 30px;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    &__heading {\n      margin-bottom: 45px;\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    &__heading {\n      margin-bottom: 60px;\n    }\n  }\n}\n", ".wp-block-table {\n  display: block;\n  margin-bottom: 15px;\n  margin-top: 30px;\n  width: calc(100% + $mobile-gutter);\n  overflow: auto;\n\n  &.center,\n  &.alignleft,\n  &.alignright {\n    display: block;\n    width: calc(100% + $mobile-gutter);\n  }\n\n  table {\n    margin-bottom: 15px;\n    max-width: 100%;\n  }\n\n  td,\n  th {\n    @include body;\n\n    border-color: inherit;\n    border-style: solid;\n    border-width: inherit;\n    min-width: 150px;\n    padding: 20px 12px;\n    vertical-align: top;\n  }\n\n  th {\n    font-weight: 700;\n  }\n\n  .wp-element-caption {\n    @include caption;\n\n    margin-bottom: 15px;\n    margin-top: -5px;\n  }\n\n  &::-webkit-scrollbar {\n    display: block;\n    height: 20px;\n    margin-top: 10px 1px 0 1px;\n    appearance: none;\n    width: 20px;\n  }\n\n  &::-webkit-scrollbar-track {\n    background-color: $golden-rod;\n    border-radius: 10px;\n    border: 1px solid $electoral-blue;\n    outline: 0 solid $electoral-blue;\n  }\n\n  &::-webkit-scrollbar-thumb {\n    background-color: $electoral-blue;\n    border-radius: 10px;\n    display: block;\n    outline: 0 solid $crt-black;\n  }\n\n  @include breakpoint(tablet, min) {\n    width: 100%;\n\n    td,\n    th {\n      padding: 20px;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    td,\n    th {\n      padding: 24px;\n    }\n  }\n}\n", ".tableofcontents {\n  h2 {\n    @include h6;\n\n    color: inherit;\n    margin-bottom: 20px;\n  }\n\n  .emphasizedlinklist {\n    margin-bottom: 0;\n    margin-top: 0;\n  }\n\n  .emphasizedlink {\n    padding: 0 px-to-rem(16px) 0 0;\n    width: calc(100% + 16px);\n\n    span {\n      display: inline;\n      padding-right: 0;\n    }\n\n    &__svg {\n      display: inline-block;\n      margin-right: -16px;\n      position: relative;\n      right: unset;\n      top: unset;\n      transition: transform 0.3s ease-in-out;\n\n      &--two {\n        margin-right: -14px;\n        transform: translateX(2px);\n      }\n    }\n\n    &:hover,\n    &:focus {\n      color: var(--text-hover-color);\n      font-style: italic;\n\n      .emphasizedlink__svg {\n        &--two {\n          transform: translateX(13px);\n        }\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    .emphasizedlinklist {\n      margin-bottom: 0;\n      margin-top: 0;\n    }\n\n    .emphasizedlink {\n      padding-right: px-to-rem(18px);\n      width: calc(100% + 18px);\n\n      &__svg {\n        margin-right: -18px;\n\n        &--two {\n          margin-right: -14px;\n          transform: translateX(4px);\n        }\n      }\n\n      &:hover,\n      &:focus {\n        .emphasizedlink__svg {\n          &--two {\n            transform: translateX(15px);\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    .emphasizedlinklist {\n      margin-bottom: 0;\n      margin-top: 0;\n    }\n  }\n}\n", ".takeactionstepone {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 85px $mobile-gutter;\n  position: relative;\n  min-height: calc(100vh - 93px);\n\n  &__bg-swirl-wrap {\n    align-items: center;\n    display: flex;\n    flex-direction: column;\n    inset: 0;\n    justify-content: center;\n    position: absolute;\n    overflow: hidden;\n    z-index: 0;\n  }\n\n  &__bg-swirl {\n    left: 0;\n    right: 0;\n    min-width: max(100vw, calc(100vh - 93px), 100%);\n    min-height: max(100vw, calc(100vh - 93px), 100%);\n  }\n\n  &__title {\n    margin-top: 30px;\n    max-width: $max-title-width;\n    order: 1;\n    position: relative;\n\n    .italicize {\n      display: inline-block;\n      transform: scale(1) rotate(0deg) translate(-0.01em, 0) skew(14.3deg, 0deg);\n      transform-origin: center;\n    }\n  }\n\n  &__content {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-flow: column;\n    max-width: $max-wide-width;\n    margin: 0 auto;\n    z-index: 1;\n\n    h1 {\n      margin-bottom: 0;\n      text-align: center;\n      width: 100%;\n    }\n  }\n\n  &__icon {\n    color: $golden-rod;\n    height: 0;\n    margin-bottom: 20px;\n    margin-top: 35px;\n    order: 2;\n    width: 130px;\n    visibility: hidden;\n  }\n\n  &__ctas {\n    order: 3;\n    position: relative;\n  }\n\n  &__subheading {\n    @include commentary;\n\n    color: var(--subheading-color);\n    margin-bottom: 0;\n    text-align: center;\n    transform: rotate(-3deg);\n\n    span {\n      display: inline;\n      color: #0000;\n      background: linear-gradient(-90deg, var(--subheading-color) 5px, #0000 0)\n          10px 0,\n        linear-gradient(var(--subheading-color) 0 0) 0 0;\n      background-size: calc(var(--n) * 1ch) 200%;\n      /* stylelint-disable */\n      background-clip: padding-box, text;\n      -webkit-background-clip: padding-box, text;\n      /* stylelint-enable */\n      background-repeat: no-repeat;\n      animation: b 0.35s infinite steps(1),\n        t calc(var(--n) * 0.1s) steps(var(--n)) forwards;\n      line-height: 1.1;\n\n      @keyframes t {\n        from {\n          background-size: 0 200%;\n        }\n      }\n\n      @keyframes b {\n        50% {\n          background-position: 0 -100%, 0 0;\n        }\n      }\n    }\n\n    &--fallback {\n      top: -15px;\n      margin-bottom: -40px;\n      position: relative;\n      text-align: center;\n    }\n\n    &--priority {\n      position: absolute;\n      left: -10px;\n      transform: rotate(-5deg);\n      top: -5px;\n      z-index: 2;\n\n      svg {\n        height: 60px;\n        transform: rotate(5deg);\n        position: absolute;\n        stroke-width: 3px;\n        top: 30px;\n        right: 10px;\n      }\n    }\n  }\n\n  .buttonlist {\n    display: flex;\n    flex-flow: row wrap;\n    align-items: center;\n    justify-content: center;\n    gap: 20px;\n    margin-top: 0;\n\n    &__item {\n      width: 100%;\n      max-width: 290px;\n    }\n  }\n\n  &--state {\n    .buttonlist {\n      margin-top: 60px;\n\n      &__item {\n        &:first-of-type {\n          animation: wiggle 1s linear;\n          animation-delay: 2.5s;\n          animation-iteration-count: 3;\n\n          @keyframes wiggle {\n            25% {\n              transform: scale(0.97);\n            }\n\n            50% {\n              transform: scale(1);\n            }\n\n            75% {\n              transform: scale(1.03);\n            }\n\n            100% {\n              transform: scale(1);\n            }\n          }\n        }\n      }\n    }\n\n    .takeactionstepone__icon {\n      margin-top: 30px;\n    }\n\n    .takeactionstepone__subheading {\n      &--priority {\n        svg {\n          path {\n            animation: dash 1s linear;\n            animation-delay: calc(var(--n) * 0.066s);\n            animation-fill-mode: forwards;\n            animation-iteration-count: 1;\n            animation-direction: alternate;\n            stroke-dasharray: 120;\n            stroke-dashoffset: 120;\n          }\n\n          path:first-of-type {\n            animation-name: arrow;\n          }\n\n          @keyframes dash {\n            0% {\n              stroke-dashoffset: 120;\n            }\n\n            50% {\n              stroke-dashoffset: 0;\n            }\n\n            100% {\n              stroke-dashoffset: 0;\n            }\n          }\n\n          @keyframes arrow {\n            0% {\n              stroke-dashoffset: 120;\n            }\n\n            50% {\n              stroke-dashoffset: 120;\n            }\n\n            75% {\n              stroke-dashoffset: 0;\n            }\n\n            100% {\n              stroke-dashoffset: 0;\n            }\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    padding: 120px $tablet-gutter;\n\n    .buttonlist {\n      &__item {\n        max-width: fit-content;\n      }\n    }\n\n    &__subheading {\n      &--fallback {\n        margin-bottom: -20;\n        position: relative;\n        right: -5px;\n        text-align: right;\n      }\n\n      &--priority {\n        left: unset;\n        top: -15px;\n\n        svg {\n          height: 67px;\n          top: 35px;\n          right: unset;\n          left: 240px;\n        }\n      }\n    }\n  }\n\n  @include breakpoint(nav-desktop, min) {\n    padding-bottom: 120px;\n    padding-top: 120px;\n\n    &__title {\n      margin-top: 0;\n      order: 2;\n    }\n\n    &__icon {\n      margin-bottom: 0;\n      order: 1;\n    }\n\n    &__ctas {\n      order: 3;\n    }\n\n    &__bg-swirl {\n      position: absolute;\n    }\n\n    .buttonlist {\n      gap: 30px;\n      margin-top: 60px;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    &__icon {\n      height: 0;\n      margin-bottom: 0;\n      width: 160px;\n    }\n  }\n\n  @include breakpoint(xl-desktop, min) {\n    padding-left: $desktop-nav-width;\n    padding-right: $desktop-nav-width;\n  }\n}\n", ".takeactionsteptwo {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 85px $mobile-gutter;\n  position: relative;\n  min-height: calc(100vh - 93px);\n\n  .stateselector {\n    z-index: 5;\n  }\n\n  &__see-all {\n    @include inline-link($color: $white, $hover: $patriotic-red);\n\n    height: fit-content;\n    grid-row: 3;\n    width: fit-content;\n  }\n\n  .priorityaction {\n    margin-top: 60px;\n    max-width: 380px;\n  }\n\n  &__form-heading {\n    @include h5;\n\n    color: $white;\n  }\n\n  &__post-submit {\n    color: $golden-rod;\n    margin-top: 30px;\n  }\n\n  &__post-submit-icon {\n    margin-bottom: 0;\n  }\n\n  &__accordiontitle {\n    @include h6;\n\n    color: $white;\n    padding: 0;\n  }\n\n  &__accordionbutton {\n    display: flex;\n    justify-content: space-between;\n    padding: $mobile-gutter px-to-rem(6px);\n    width: 100%;\n\n    svg {\n      color: $golden-rod;\n      height: 14px;\n      width: 14px;\n\n      .plus {\n        transition: transform 0.3s ease-in;\n        transform-origin: center center;\n        transform: rotate(0deg);\n      }\n    }\n\n    &[aria-expanded='true'] {\n      svg {\n        .plus {\n          transform: rotate(90deg);\n        }\n      }\n    }\n  }\n\n  &__accordionpanel {\n    height: auto;\n    overflow: hidden;\n    margin-bottom: 0;\n    padding: 0 px-to-rem(6px);\n    transition: height 0.3s ease-in-out;\n\n    &[aria-hidden='false'] {\n      padding: 0 px-to-rem(6px) px-to-rem($mobile-gutter);\n    }\n  }\n\n  &__form {\n    background-color: $electoral-blue;\n    display: none;\n    grid-row: 1;\n\n    .form__error-msg {\n      margin-bottom: 0;\n    }\n\n    &.show {\n      display: block;\n      margin-bottom: 40px;\n      padding: $mobile-gutter;\n\n      .takeactionsteptwo__actionnetwork {\n        align-items: flex-end;\n        display: grid;\n        grid-column-gap: 30px;\n        grid-template-columns: 100%;\n\n        &.hidden {\n          display: none;\n        }\n\n        .form__form-disclaimer {\n          grid-column: span 1;\n          grid-row: 4;\n        }\n\n        .input-group {\n          grid-column: span 1;\n\n          &--phone {\n            grid-row: 3;\n          }\n\n          &.error {\n            margin-top: 20px;\n\n            input {\n              padding-top: 10px;\n            }\n          }\n        }\n\n        .input-group__submit {\n          height: fit-content;\n          margin-top: 28px;\n        }\n      }\n    }\n  }\n\n  &__bg-swirl-wrap {\n    align-items: center;\n    display: flex;\n    flex-direction: column;\n    inset: 0;\n    justify-content: center;\n    position: absolute;\n    overflow: hidden;\n    z-index: 0;\n  }\n\n  &__bg-swirl {\n    min-width: max(100vw, calc(100vh - 93px), 100%);\n    min-height: max(100vw, calc(100vh - 93px), 100%);\n  }\n\n  &__heading {\n    max-width: $max-title-width;\n    position: relative;\n    text-align: center;\n\n    &--where {\n      margin-bottom: 40px;\n    }\n\n    &--state {\n      margin-bottom: 25px;\n    }\n  }\n\n  &__no-results {\n    inset: 0;\n    max-width: $max-title-width;\n    opacity: 0;\n    position: absolute;\n    text-align: center;\n    transition: opacity 0.3s ease-in-out 0s;\n    z-index: -1;\n    visibility: hidden;\n\n    &.show {\n      opacity: 1;\n      position: relative;\n      transition: opacity 0.5s ease-in-out 0.1s;\n      z-index: 2;\n      visibility: visible;\n    }\n  }\n\n  &__no-results-heading {\n    @include h4;\n\n    color: $white;\n    margin-top: 40px;\n    text-transform: none;\n  }\n\n  &__content {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-flow: column;\n    max-width: $max-wide-width;\n    margin: 0 auto;\n    position: relative;\n    z-index: 1;\n  }\n\n  &__title {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-flow: column;\n    inset: 0;\n    opacity: 0;\n    position: absolute;\n    transition: opacity 0.3s ease-in-out 0s;\n    z-index: -1;\n    visibility: hidden;\n\n    &.show {\n      opacity: 1;\n      position: relative;\n      transition: opacity 0.5s ease-in-out 0.1s;\n      z-index: 2;\n      visibility: visible;\n    }\n  }\n\n  &__results {\n    display: grid;\n    grid-gap: 30px;\n    grid-template-columns: 100%;\n    grid-template-rows: auto;\n    height: 0;\n    margin-top: 0;\n    opacity: 0;\n    padding-bottom: 0;\n    padding-top: 0;\n    position: relative;\n    width: 100%;\n    z-index: -1;\n    visibility: hidden;\n\n    &.show {\n      height: auto;\n      opacity: 1;\n      margin-top: 30px;\n      padding-bottom: 20px;\n      padding-top: 20px;\n      position: relative;\n      transition: opacity 0.5s ease-in-out 0.1s;\n      z-index: 2;\n      visibility: visible;\n\n      .takeactionsteptwo {\n        &__event-list {\n          grid-auto-rows: min-content;\n        }\n\n        &__form {\n          + .takeactionsteptwo__event-list {\n            margin-top: -30px;\n          }\n        }\n      }\n\n      &.final {\n        .takeactionsteptwo {\n          &__see-all {\n            grid-row: 3;\n          }\n\n          &__event-list {\n            grid-auto-rows: min-content;\n          }\n\n          &__form {\n            &.show {\n              grid-row: 2;\n\n              + .takeactionsteptwo__event-list {\n                + .takeactionsteptwo__loader {\n                  + .takeactionsteptwo__see-all {\n                    grid-row: 4;\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  &__loader {\n    height: 0;\n    margin-top: 0;\n    opacity: 0;\n    padding-bottom: 0;\n    padding-top: 0;\n    position: relative;\n    width: 100%;\n    z-index: -1;\n    visibility: hidden;\n\n    &.show {\n      grid-column: 1 / span 1;\n      grid-row: 2;\n      height: 150px;\n      margin-top: 30px;\n      opacity: 1;\n      transition: opacity 0.5s ease-in-out 0.1s;\n      z-index: 2;\n      visibility: visible;\n    }\n  }\n\n  &__applied-filters {\n    color: $white;\n    margin-bottom: 0;\n    margin-left: auto;\n    margin-right: auto;\n    max-width: $max-title-width;\n    text-align: center;\n\n    &.show {\n      margin-bottom: 18px;\n    }\n  }\n\n  &__applied-filter-election,\n  &__applied-filter-event {\n    @include h6;\n\n    display: none;\n\n    &.show {\n      display: inline;\n\n      ul,\n      li,\n      a {\n        @include h6;\n\n        display: inline;\n      }\n\n      a {\n        text-decoration: underline;\n\n        &:hover,\n        &:focus {\n          color: $patriotic-red;\n          font-style: italic;\n        }\n      }\n    }\n  }\n\n  &__checkbox {\n    @include body;\n  }\n\n  &__checkbox-text {\n    margin-top: -1px;\n  }\n\n  &__legend {\n    @include h5;\n\n    color: $white;\n    margin-bottom: 25px;\n\n    &--details {\n      @include h6;\n\n      display: block;\n      margin-top: 20px;\n    }\n  }\n\n  &__submit {\n    @include secondary-button($color: $golden-rod, $hover: $golden-rod-dark);\n  }\n\n  &__icon {\n    color: $golden-rod;\n    height: 68px;\n    margin-bottom: 25px;\n    width: 79px;\n  }\n\n  &__event-list {\n    align-items: flex-start;\n    display: grid;\n    grid-template-columns: 1fr;\n    grid-row-gap: 34px;\n    grid-template-rows: min-content;\n    opacity: 0;\n    position: relative;\n    z-index: -1;\n    visibility: hidden;\n\n    &.show {\n      opacity: 1;\n      position: relative;\n      transition: opacity 0.5s ease-in-out 0.1s;\n      z-index: 2;\n      visibility: visible;\n    }\n  }\n\n  &__filters {\n    border-bottom: 1px solid $white;\n    border-top: 1px solid $white;\n    margin-bottom: 30px;\n    width: 100%;\n  }\n\n  &__filtersform {\n    input[type='submit']:not([id^='acf']) {\n      @include secondary-button($color: $golden-rod, $hover: $golden-rod-dark);\n\n      width: 100%;\n    }\n  }\n\n  &__fieldset {\n    border: none;\n    margin-bottom: 30px;\n    padding: 0;\n  }\n\n  &__clear-links {\n    margin-bottom: 0;\n    text-align: center;\n  }\n\n  &__clear {\n    display: none;\n    margin-left: 26px;\n    margin-right: 26px;\n\n    &.show {\n      display: inline-block;\n    }\n  }\n\n  .emphasizedlink__svg {\n    top: 4px;\n  }\n\n  &__no-result-event-list {\n    display: grid;\n    grid-template-columns: 1fr;\n    grid-template-rows: 1fr;\n    grid-gap: 40px;\n    margin-top: 50px;\n  }\n\n  @include breakpoint(mobile, max) {\n    &__heading {\n      font-size: min(24vmin, 105px);\n    }\n  }\n\n  @include breakpoint(nav-tablet, min) {\n    .priorityaction {\n      margin-bottom: 50px;\n      margin-top: 10px;\n      order: 1;\n      max-width: 400px;\n    }\n\n    &__heading {\n      &--where {\n        order: 2;\n      }\n    }\n\n    .stateselector {\n      order: 3;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    padding: 120px $tablet-gutter;\n\n    &__clear-links {\n      margin-bottom: 0;\n    }\n\n    &__applied-filters {\n      margin-bottom: 0;\n\n      &.show {\n        margin-bottom: 20px;\n      }\n    }\n\n    &__form {\n      grid-column: 1 / span 2;\n\n      &.show {\n        grid-row: 1;\n        margin-bottom: 40px;\n\n        .takeactionsteptwo__actionnetwork {\n          display: grid;\n          grid-column-gap: 30px;\n          grid-template-columns: calc(100% - 240px) 210px;\n\n          .form__form-disclaimer {\n            grid-column: 1 / span 2;\n            grid-row: 3;\n          }\n\n          .input-group {\n            grid-column: 1 / span 2;\n\n            &--zip {\n              grid-column: 1 / span 1;\n            }\n\n            &--phone {\n              grid-row: 2;\n            }\n\n            &__submit {\n              height: fit-content;\n              margin-bottom: 10px;\n            }\n          }\n        }\n      }\n    }\n\n    &__filtersform {\n      input[type='submit']:not([id^='acf']) {\n        @include button(\n          $color: $patriotic-red,\n          $hover: $patriotic-red-dark,\n          $text-color: $white\n        );\n\n        width: auto;\n      }\n    }\n\n    &__fieldset {\n      margin-bottom: 40px;\n    }\n\n    &__accordiontitle {\n      display: none;\n    }\n\n    &__accordionpanel {\n      &[aria-hidden='false'],\n      &[aria-hidden='true'] {\n        height: auto !important;\n      }\n    }\n\n    &__see-all {\n      margin-top: -20px;\n      grid-column: 1 / span 2;\n      grid-row: 2;\n    }\n\n    .emphasizedlink__svg {\n      top: 5px;\n\n      &--x {\n        top: 6px;\n      }\n    }\n\n    &__event-list {\n      grid-row-gap: 50px;\n      grid-column: 1 / span 1;\n      grid-row: 1;\n    }\n\n    &__loader {\n      &.show {\n        grid-column: 1 / span 1;\n        grid-row: 1;\n        height: 750px;\n        padding: 300px 50px;\n      }\n    }\n\n    &__heading {\n      &--where {\n        margin-bottom: 50px;\n      }\n\n      &--state {\n        margin-bottom: 35px;\n      }\n    }\n\n    &__filters {\n      background-color: $electoral-blue;\n      border: none;\n      grid-column: 2 / span 1;\n      height: fit-content;\n      margin-left: auto;\n      margin-right: -$tablet-gutter;\n      padding: $tablet-gutter;\n      position: relative;\n      width: calc(100% + $tablet-gutter);\n    }\n\n    &__results {\n      border-bottom: none;\n      border-top: none;\n      display: grid;\n      grid-gap: 50px;\n      grid-template-columns: 50% calc(50% - 50px);\n      margin-top: 50px;\n      max-width: $max-wide-width;\n      padding-bottom: 0;\n      padding-top: 0;\n\n      &.show {\n        .takeactionsteptwo {\n          &__form {\n            + .takeactionsteptwo__event-list {\n              margin-top: 0;\n            }\n          }\n        }\n\n        &.final {\n          .takeactionsteptwo {\n            &__filters {\n              display: none;\n            }\n\n            &__event-list {\n              grid-column: 1 / span 2;\n              grid-column-gap: 50px;\n              grid-auto-rows: min-content;\n              grid-template-columns: 1fr 1fr;\n\n              &--form {\n                grid-row: 2;\n              }\n            }\n\n            &__see-all {\n              grid-row: 2;\n            }\n\n            &__loader {\n              grid-column: 1 / span 2;\n\n              &.show {\n                height: 300px;\n                padding: 50px;\n              }\n            }\n\n            &__form {\n              &.show {\n                grid-row: 1;\n              }\n\n              + .takeactionsteptwo__event-list {\n                margin-top: -50px;\n\n                + .takeactionsteptwo__loader {\n                  &.show {\n                    grid-row: 2;\n                  }\n\n                  + .takeactionsteptwo__see-all {\n                    grid-row: 3;\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    &__no-result-event-list {\n      display: grid;\n      grid-template-columns: 1fr 1fr 1fr;\n      grid-gap: 40px;\n      margin-top: 60px;\n\n      &--1 {\n        grid-template-columns: 1fr;\n      }\n\n      &--2 {\n        grid-template-columns: 1fr 1fr;\n      }\n\n      .mobilizeevent {\n        &__content {\n          text-align: left;\n        }\n      }\n    }\n  }\n\n  @include breakpoint(nav-desktop, min) {\n    &__form {\n      grid-column: 1 / span 2;\n\n      &.show {\n        margin-bottom: 60px;\n        padding: 30px;\n\n        .takeactionsteptwo {\n          &__actionnetwork {\n            display: grid;\n            grid-column-gap: 30px;\n            grid-template-columns:\n              calc((100% - 270px) / 2) calc((100% - 270px) / 2)\n              210px;\n\n            .form__form-disclaimer {\n              grid-column: span 2;\n              grid-row: 3;\n            }\n\n            .input-group {\n              grid-column: span 1;\n              grid-row: 1;\n\n              &--zip {\n                grid-column: span 1;\n              }\n\n              &--phone {\n                grid-column: span 2;\n                grid-row: 2;\n              }\n\n              &__submit {\n                height: fit-content;\n                margin-bottom: 10px;\n                grid-row: 1 / span 2;\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .priorityaction {\n      margin-bottom: 50px;\n      margin-top: 50px;\n      order: 1;\n      max-width: 500px;\n    }\n\n    &__bg-swirl {\n      position: absolute;\n    }\n\n    &__title {\n      padding-left: 110px;\n      padding-right: 110px;\n    }\n\n    &__clear {\n      margin-right: 40px;\n      margin-left: 40px;\n    }\n\n    &__results {\n      &.show {\n        &.final {\n          .takeactionsteptwo {\n            &__filters {\n              display: none;\n            }\n\n            &__event-list {\n              grid-column: 1 / span 2;\n              grid-column-gap: 50px;\n              grid-auto-rows: min-content;\n              grid-template-columns: 1fr 1fr 1fr;\n            }\n\n            &__form {\n              + .takeactionsteptwo__event-list {\n                margin-top: -70px;\n              }\n\n              + .takeactionsteptwo__loader {\n                &.show {\n                  grid-row: 2;\n                }\n\n                + .takeactionsteptwo__see-all {\n                  grid-row: 4;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    &__heading {\n      &--where {\n        margin-bottom: 60px;\n      }\n\n      &--state {\n        margin-bottom: 40px;\n      }\n    }\n\n    &__clear-links {\n      margin-bottom: 0;\n    }\n\n    &__applied-filters {\n      margin-bottom: 0;\n\n      &.show {\n        margin-bottom: 22px;\n      }\n    }\n\n    &__event-list {\n      grid-row-gap: 70px;\n    }\n\n    &__results {\n      grid-gap: 70px;\n      grid-template-columns: 50% calc(50% - 70px);\n      margin-top: 70px;\n    }\n\n    &__filters {\n      margin-right: -$desktop-gutter;\n      padding: $desktop-gutter;\n      width: calc(100% + 50px);\n    }\n\n    &__fieldset {\n      max-width: 425px;\n    }\n\n    .priorityaction {\n      margin-bottom: 60px;\n      margin-top: -40px;\n      max-width: 600px;\n    }\n\n    &__see-all {\n      margin-top: -40px;\n    }\n\n    &__form {\n      .takeactionsteptwo__actionnetwork {\n        .input-group {\n          &.error {\n            margin-top: 20px;\n\n            input {\n              padding-top: 10px;\n            }\n          }\n\n          &__error-msg {\n            margin-right: 55px;\n\n            &::after {\n              background-size: 48px 33px;\n              height: 33px;\n              right: 0;\n              top: 0;\n              width: 58px;\n            }\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    &__results {\n      grid-gap: 70px 100px;\n      grid-template-columns: calc(100% - 545px) 425px;\n\n      &.show {\n        &.final {\n          .takeactionsteptwo {\n            &__filters {\n              display: none;\n            }\n\n            &__event-list {\n              grid-column-gap: 70px;\n            }\n          }\n\n          .mobilizeevent {\n            align-items: center;\n            display: block;\n            grid-column-gap: 30px;\n            grid-template-columns: min(calc(50% - 30px), 325px) 1fr;\n\n            &__fig {\n              border-top: 6px solid $electoral-blue;\n              border-left: none;\n              margin-bottom: 30px;\n            }\n\n            &:hover,\n            &:focus {\n              .mobilizeevent__fig {\n                border-top-color: $patriotic-red;\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .priorityaction {\n      margin-bottom: 90px;\n      margin-top: -40px;\n    }\n\n    &__filters {\n      margin-left: 0;\n      margin-right: calc(((100vw - $max-wide-width) / 2) * -1);\n      max-width: 100%;\n      min-width: calc(100% + 70px);\n      right: 0;\n      width: 100%;\n    }\n  }\n\n  @include breakpoint(ta-desktop, min) {\n    &__results {\n      grid-row-gap: 110px;\n      grid-template-columns: calc(100% - 585px) 425px;\n    }\n\n    &__filters {\n      min-width: calc(475px + ((100vw - $max-wide-width) / 2) + 1px);\n      width: 100%;\n    }\n\n    &__see-all {\n      margin-top: -70px;\n    }\n  }\n}\n", ".targetstatlist {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  grid-gap: 24px;\n  max-width: 950px;\n\n  .targetstat {\n    align-items: center;\n    display: flex;\n    flex-direction: row;\n    margin: 0;\n    width: min-content;\n\n    &__stat {\n      @include h2;\n    }\n\n    &__title {\n      @include body;\n\n      margin-left: px-to-rem(15px);\n    }\n  }\n\n  @include breakpoint(m-mobile, min) {\n    grid-template-columns: 1fr 1fr 1fr;\n    grid-gap: 32px;\n  }\n\n  @include breakpoint(l-mobile, min) {\n    align-items: center;\n    display: flex;\n    flex-flow: row wrap;\n    grid-gap: 0;\n    justify-content: center;\n\n    .targetstat {\n      margin: 20px;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    grid-template-columns: 1fr 1fr 1fr;\n\n    .targetstat {\n      margin: 30px;\n    }\n  }\n}\n", ".teamleaderboard {\n  align-items: center;\n  background-color: var(--bg-color);\n  color: var(--heading-color);\n  display: flex;\n  flex-direction: column;\n  overflow-x: clip;\n  overflow-y: visible;\n  padding: 50px $mobile-gutter 55px;\n  position: relative;\n\n  $self: &;\n\n  &__content {\n    align-items: center;\n    display: flex;\n    flex-direction: column;\n    position: relative;\n    width: 100%;\n\n    & + #{$self}__content {\n      #{$self} {\n        &__hgroup {\n          margin-top: 90px;\n        }\n\n        &__icon {\n          &:nth-child(1) {\n            top: 180px;\n          }\n        }\n      }\n    }\n  }\n\n  &__footnote {\n    @include caption;\n\n    color: var(--footnote-color);\n    margin-top: 50px;\n    max-width: 870px;\n    text-align: center;\n  }\n\n  &__icon {\n    color: var(--icon-color);\n    height: 50px;\n    position: absolute;\n    transform: translate(var(--tx), var(--ty)) rotate(var(--r))\n      scale(var(--scale));\n    width: 50px;\n\n    &:nth-child(1) {\n      left: -10px;\n      top: 130px;\n    }\n\n    &:nth-child(2) {\n      right: -15px;\n      top: 30%;\n    }\n\n    &:nth-child(3) {\n      bottom: 110px;\n      left: 5px;\n    }\n\n    &:nth-child(4),\n    &:nth-child(5) {\n      display: none;\n    }\n  }\n\n  &__hgroup {\n    margin-bottom: 60px;\n    max-width: 1200px;\n    position: relative;\n    width: fit-content;\n    z-index: 1;\n  }\n\n  &__heading {\n    @include community-stats;\n\n    color: var(--heading-color);\n    line-height: 100%;\n    text-align: center;\n    width: fit-content;\n\n    .handdrawnunderline2,\n    .handdrawnunderline {\n      display: inline-block;\n      position: relative;\n\n      &::after {\n        content: ' ';\n        background-repeat: no-repeat;\n        background-size: contain;\n        bottom: -20px;\n        height: 27px;\n        left: 0;\n        position: absolute;\n        right: 0;\n        width: 100%;\n      }\n    }\n  }\n\n  &__winning {\n    @include commentary;\n\n    bottom: -25px;\n    color: var(--winning-color);\n    margin-bottom: 0;\n    position: absolute;\n    text-align: right;\n    transform: rotate(-3deg);\n    right: 0;\n\n    span {\n      display: inline;\n      color: #0000;\n      background: linear-gradient(-90deg, var(--winning-color) 5px, #0000 0)\n          10px 0,\n        linear-gradient(var(--winning-color) 0 0) 0 0;\n      background-size: calc(var(--n) * 1ch) 200%;\n      /* stylelint-disable */\n      background-clip: padding-box, text;\n      -webkit-background-clip: padding-box, text;\n      /* stylelint-enable */\n      background-repeat: no-repeat;\n      line-height: 1.1;\n      opacity: 0;\n    }\n\n    &[data-visible='true'],\n    &[data-has-animated='true'] {\n      span {\n        animation: b 0.35s infinite steps(1),\n          t calc(var(--n) * 0.1s) steps(var(--n)) forwards;\n        animation-delay: 1s;\n        opacity: 1;\n        transition: opacity 0.05s ease;\n        transition-delay: 1s;\n\n        @keyframes t {\n          from {\n            background-size: 0 200%;\n          }\n        }\n\n        @keyframes b {\n          50% {\n            background-position: 0 -100%, 0 0;\n          }\n        }\n      }\n    }\n  }\n\n  &__stat {\n    border-bottom: 1px solid var(--heading-color);\n    padding-bottom: 40px;\n    margin-bottom: 40px;\n\n    &:last-of-type {\n      border-bottom: none;\n      margin-bottom: 0;\n      padding-bottom: 0;\n    }\n  }\n\n  &__stat-heading {\n    margin-bottom: 30px;\n    text-align: center;\n  }\n\n  &__stat-eyebrow {\n    @include h6;\n\n    display: block;\n    margin-bottom: 12px;\n  }\n\n  &__team-name {\n    @include h6;\n\n    display: block;\n    margin-bottom: 0;\n  }\n\n  &__headers {\n    text-align: left;\n  }\n\n  &__stat-header {\n    @include h6;\n\n    &--team {\n      padding-left: 50px;\n    }\n  }\n\n  &__stat-cell {\n    @include h3;\n\n    padding: 50px 0;\n\n    &--team {\n      padding-left: 50px;\n    }\n  }\n\n  &__stat-row {\n    border-bottom: 1px solid var(--heading-color);\n\n    &:last-of-type {\n      border-bottom: none;\n    }\n  }\n\n  &__team-cell-stat {\n    display: block;\n    position: relative;\n    width: fit-content;\n  }\n\n  &__stat-title,\n  &__team-stat {\n    @include h3;\n\n    font-size: 30px;\n    display: block;\n  }\n\n  &__teams {\n    display: grid;\n    grid-gap: 35px;\n    grid-template-columns: repeat(2, 1fr);\n  }\n\n  &__team {\n    &:nth-child(odd) {\n      align-items: flex-end;\n      display: flex;\n      flex-direction: column;\n      text-align: right;\n\n      #{$self} {\n        &__team-win {\n          &--checkmark {\n            left: -27px;\n            bottom: 2px;\n          }\n\n          &--eyes {\n            bottom: 0;\n            left: -30px;\n            transform: rotate(8.37deg);\n          }\n\n          &--flame {\n            bottom: -10px;\n            left: -27px;\n            transform: rotate(12.76deg);\n          }\n        }\n      }\n    }\n\n    &:nth-child(even) {\n      align-items: flex-start;\n      display: flex;\n      flex-direction: column;\n      text-align: left;\n\n      #{$self} {\n        &__team-win {\n          &--checkmark {\n            right: -27px;\n            bottom: 2px;\n          }\n\n          &--eyes {\n            bottom: 0;\n            right: -30px;\n            transform: rotate(-8.37deg);\n          }\n\n          &--flame {\n            bottom: -10px;\n            right: -27px;\n            transform: rotate(-12.76deg);\n          }\n        }\n      }\n    }\n  }\n\n  &__team-stat {\n    position: relative;\n    width: fit-content;\n  }\n\n  &__team-win {\n    align-items: center;\n    background-color: $golden-rod;\n    border-radius: 50%;\n    display: flex;\n    height: 33px;\n    justify-content: center;\n    position: absolute;\n    width: 33px;\n  }\n\n  &__team-cell-win {\n    align-items: center;\n    background-color: $golden-rod;\n    border-radius: 50%;\n    display: flex;\n    height: 33px;\n    justify-content: center;\n    position: absolute;\n    right: -27px;\n    top: 50%;\n    width: 33px;\n\n    &--eyes {\n      top: calc(50% - 17px);\n    }\n  }\n\n  &__win-icon {\n    color: $electoral-blue;\n    height: 25px;\n    width: 25px;\n\n    &--checkmark {\n      height: 20px;\n      width: 20px;\n    }\n\n    &--eyes {\n      height: 23px;\n      width: 23px;\n    }\n  }\n\n  &__stats {\n    border-collapse: collapse;\n    color: var(--heading-color);\n    margin-bottom: 0;\n    max-width: 1120px;\n    position: relative;\n    z-index: 1;\n\n    &--table {\n      display: none;\n      width: 100%;\n    }\n  }\n\n  &__tbody {\n    width: 100%;\n  }\n\n  @include breakpoint(tablet, min) {\n    padding: 75px $tablet-gutter 70px;\n\n    &__content {\n      & + #{$self}__content {\n        #{$self} {\n          &__hgroup {\n            margin-top: 130px;\n          }\n\n          &__icon {\n            &:nth-child(1) {\n              top: 180px;\n            }\n          }\n        }\n      }\n    }\n\n    &__icon {\n      height: 65px;\n      width: 65px;\n\n      &:nth-child(1) {\n        left: -10px;\n        top: 130px;\n      }\n\n      &:nth-child(2) {\n        right: -15px;\n        top: 30%;\n      }\n\n      &:nth-child(3) {\n        bottom: 0;\n        left: 5px;\n      }\n\n      &:nth-child(4) {\n        display: block;\n        left: 5px;\n        top: 55%;\n      }\n\n      &:nth-child(5) {\n        display: block;\n        right: 5px;\n        top: 80%;\n      }\n    }\n\n    &__hgroup {\n      margin-bottom: 100px;\n    }\n\n    &__heading {\n      line-height: 95%;\n    }\n\n    &__teams {\n      grid-gap: 45px;\n    }\n\n    &__team {\n      &:nth-child(odd) {\n        #{$self} {\n          &__team-win {\n            &--checkmark {\n              bottom: 10px;\n              left: -42px;\n            }\n\n            &--eyes {\n              bottom: 5px;\n              left: -42px;\n            }\n\n            &--flame {\n              left: -42px;\n            }\n          }\n        }\n      }\n\n      &:nth-child(even) {\n        #{$self} {\n          &__team-win {\n            &--checkmark {\n              bottom: 10px;\n              right: -45px;\n            }\n\n            &--eyes {\n              bottom: 5px;\n              right: -45px;\n            }\n\n            &--flame {\n              right: -45px;\n            }\n          }\n        }\n      }\n    }\n\n    &__stats {\n      display: none;\n      margin-bottom: 0;\n\n      &--table {\n        display: table;\n      }\n    }\n\n    &__stat {\n      padding-bottom: 65px;\n      margin-bottom: 65px;\n\n      &:last-of-type {\n        margin-bottom: 0;\n        padding-bottom: 0;\n      }\n    }\n\n    &__stat-heading {\n      margin-bottom: 40px;\n    }\n\n    &__stat-eyebrow,\n    &__team-name {\n      font-size: 22px;\n      margin-bottom: 20px;\n    }\n\n    &__stat-header {\n      font-size: 22px;\n    }\n\n    &__team-cell-win,\n    &__team-win {\n      height: 55px;\n      width: 55px;\n    }\n\n    &__team-cell-win {\n      right: -42px;\n\n      &--eyes {\n        top: calc(50% - 36px);\n      }\n    }\n\n    &__win-icon {\n      height: 35px;\n      width: 35px;\n\n      &--checkmark {\n        height: 30px;\n        width: 30px;\n      }\n    }\n\n    &__footnote {\n      margin-top: 70px;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    padding: 100px $desktop-gutter 90px;\n\n    &__content {\n      & + #{$self}__content {\n        #{$self} {\n          &__hgroup {\n            margin-top: 170px;\n          }\n\n          &__icon {\n            &:nth-child(1) {\n              top: 280px;\n            }\n          }\n        }\n      }\n    }\n\n    &__winning {\n      font-size: 40px;\n    }\n\n    &__hgroup {\n      margin-bottom: 100px;\n    }\n\n    &__icon {\n      height: 80px;\n      width: 80px;\n    }\n\n    &__stat-heading {\n      margin-bottom: 50px;\n    }\n\n    &__stat-eyebrow,\n    &__team-name,\n    &__stat-header {\n      font-size: 30px;\n    }\n\n    &__teams {\n      grid-gap: 60px;\n    }\n\n    &__stats {\n      margin-bottom: 0;\n    }\n\n    &__stat {\n      margin-bottom: 90px;\n      padding-bottom: 90px;\n\n      &:last-of-type {\n        margin-bottom: 0;\n        padding-bottom: 0;\n      }\n    }\n\n    &__footnote {\n      margin-top: 90px;\n    }\n  }\n}\n", ".testimonial {\n  margin-bottom: 45px;\n  margin-top: 45px;\n  position: relative;\n\n  &::after {\n    border: 6px solid var(--border-color);\n    bottom: 0;\n    content: '';\n    height: 100%;\n    left: 0;\n    position: absolute;\n    width: 100%;\n    z-index: 1;\n  }\n\n  $self: &;\n\n  p {\n    @include featured-text;\n\n    color: var(--text-color);\n  }\n\n  &__blockquote {\n    color: var(--text-color);\n    padding: 34px 22px 28px;\n    position: relative;\n    z-index: 2;\n\n    p:last-of-type {\n      margin-bottom: 0;\n    }\n  }\n\n  &__lgquote,\n  &__cite {\n    @include cite;\n\n    color: var(--cite-color);\n    display: block;\n    line-height: 1;\n  }\n\n  &__cite {\n    margin-top: 26px;\n  }\n\n  &__lgquote {\n    font-size: px-to-rem(260px);\n    position: absolute;\n    top: -55px;\n    left: 20px;\n    z-index: 2;\n  }\n\n  &__image {\n    display: block;\n    position: relative;\n    object-fit: cover;\n    z-index: -1;\n  }\n\n  &--square {\n    margin-bottom: 60px;\n    padding: 0 20px 20px;\n\n    &::after {\n      bottom: 16px;\n      height: calc(100% - 16px);\n      left: 16px;\n      top: 0;\n      width: calc(100% - 32px);\n    }\n\n    #{$self} {\n      &__image {\n        bottom: -20px;\n        clip-path: $pixel-tips-mobile;\n        height: 100%;\n        margin-top: -20px;\n        max-height: 560px;\n        max-width: calc(100% + 40px);\n        position: relative;\n        left: -20px;\n        width: calc(100% + 40px);\n      }\n\n      &__lgquote {\n        left: 35px;\n        top: -50px;\n      }\n\n      &__blockquote {\n        padding-bottom: 32px;\n      }\n    }\n  }\n\n  &--crop {\n    width: 100%;\n\n    #{$self} {\n      &__image {\n        bottom: 6px;\n        height: auto;\n        left: 6px;\n        max-width: calc(100% - 12px);\n        object-fit: contain;\n        object-position: bottom right;\n        position: relative;\n        right: 6px;\n        width: 100%;\n        z-index: 2;\n      }\n    }\n  }\n\n  &--half {\n    max-width: 100%;\n  }\n\n  @include breakpoint(tablet, min) {\n    &--half {\n      max-width: 560px;\n    }\n\n    &__blockquote {\n      p:last-of-type {\n        margin-bottom: 0;\n      }\n    }\n  }\n\n  @include breakpoint(nav-desktop, min) {\n    margin-bottom: 65px;\n    margin-top: 65px;\n\n    &__lgquote {\n      font-size: px-to-rem(280px);\n      left: 35px;\n      top: -55px;\n    }\n\n    &__blockquote {\n      padding: 40px 32px 30px;\n    }\n\n    &--crop:not(.testimonial--half) {\n      border: none;\n      padding: 0;\n      width: 100%;\n\n      #{$self} {\n        &__blockquote {\n          display: grid;\n          grid-template-columns: calc(55% - 50px) 45%;\n          grid-column-gap: 32px;\n          padding: 32px;\n          margin-bottom: 0;\n\n          p,\n          cite {\n            grid-column: 1;\n          }\n        }\n\n        &__lgquote {\n          top: -55px;\n        }\n\n        &__image {\n          bottom: 6px;\n          height: auto;\n          margin-top: 0;\n          max-height: calc(100% + 22px);\n          max-width: 50%;\n          position: absolute;\n          right: 6px;\n          left: unset;\n          width: 50%;\n        }\n      }\n    }\n\n    &--square:not(.testimonial--half) {\n      padding: 16px 16px 16px 0;\n      width: 100%;\n\n      &::after {\n        inset: 16px 32px 16px 0;\n        height: calc(100% - 32px);\n        width: calc(100% - 16px);\n      }\n\n      #{$self} {\n        &__blockquote {\n          display: grid;\n          grid-template-columns: calc(55% - 50px) 45%;\n          grid-column-gap: 32px;\n          margin-bottom: 0;\n          padding: 32px;\n\n          p,\n          cite {\n            grid-column: 1;\n          }\n        }\n\n        &__lgquote {\n          top: -33px;\n        }\n\n        &__image {\n          bottom: unset;\n          height: 100%;\n          margin-top: 0;\n          max-height: 100%;\n          max-width: calc(45% + 26px);\n          position: absolute;\n          right: 0;\n          left: unset;\n          top: 0;\n          width: calc(45% + 26px);\n        }\n      }\n    }\n\n    &--half {\n      max-width: 560px;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-bottom: 90px;\n    margin-top: 90px;\n\n    &::after {\n      border: 12px solid var(--border-color);\n    }\n\n    &__lgquote {\n      font-size: px-to-rem(300px);\n      left: 50px;\n      top: -60px;\n    }\n\n    &__blockquote {\n      padding: 63px 53px 53px;\n\n      p:last-of-type {\n        margin-bottom: 0;\n      }\n    }\n\n    &--square {\n      &:not(.testimonial--half) {\n        padding: 22px 22px 22px 0;\n\n        &::after {\n          inset: 22px 44px 22px 0;\n          height: calc(100% - 44px);\n          width: calc(100% - 22px);\n        }\n\n        #{$self} {\n          &__blockquote {\n            display: grid;\n            grid-template-columns: calc(55% - 50px) 45%;\n            grid-column-gap: 50px;\n            padding: 63px 53px 53px;\n\n            p,\n            cite {\n              grid-column: 1;\n            }\n          }\n\n          &__lgquote {\n            left: 45px;\n            top: -35px;\n          }\n\n          &__image {\n            height: 100%;\n            max-height: 100%;\n            max-width: calc(45% + 24px);\n            position: absolute;\n            right: 0;\n            top: 0;\n            width: calc(45% + 24px);\n          }\n        }\n      }\n\n      &#{$self}--half {\n        padding: 0 22px 22px;\n\n        &::after {\n          bottom: 22px;\n          height: calc(100% - 22px);\n          left: 22px;\n          width: calc(100% - 44px);\n        }\n\n        #{$self} {\n          &__lgquote {\n            left: 70px;\n            top: -60px;\n          }\n        }\n      }\n    }\n\n    &--crop {\n      &:not(.testimonial--half) {\n        #{$self} {\n          &__blockquote {\n            display: grid;\n            grid-template-columns: calc(55% - 50px) 45%;\n            grid-column-gap: 50px;\n            padding: 63px 53px 53px;\n\n            p,\n            cite {\n              grid-column: 1;\n            }\n          }\n\n          &__image {\n            bottom: 12px;\n            height: auto;\n            margin-top: 0;\n            max-height: calc(100% + 36px);\n            max-width: 50%;\n            position: absolute;\n            right: 12px;\n            left: unset;\n            width: 50%;\n          }\n        }\n      }\n\n      &#{$self}--half {\n        #{$self} {\n          &__image {\n            bottom: 12px;\n            left: 12px;\n            right: 12px;\n            max-width: calc(100% - 24px);\n          }\n        }\n      }\n    }\n\n    &--half {\n      max-width: 560px;\n    }\n  }\n}\n", ".teamtarget {\n  color: var(--target-text-color);\n\n  &__state {\n    @include h5;\n\n    display: block;\n    margin-bottom: px-to-rem(10px);\n  }\n\n  &__race {\n    @include h6;\n\n    display: block;\n    margin-bottom: px-to-rem(20px);\n  }\n\n  .paragraph {\n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n}\n", ".teamtargetlist {\n  background-color: var(--bg-color);\n  display: grid;\n  grid-template-rows: 1fr;\n  padding: 0 $mobile-gutter px-to-rem(60px);\n  transition: visibility 0s ease 0s, opacity 0.15s ease-in-out 0.1s,\n    grid-template-rows 0.3s ease-in-out 0.1s;\n  visibility: visible;\n\n  &__content {\n    align-items: center;\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    overflow: hidden;\n  }\n\n  &.hide {\n    grid-template-rows: 0fr;\n    opacity: 0;\n    padding: 0 $mobile-gutter;\n    visibility: hidden;\n  }\n\n  &__list {\n    display: grid;\n    grid-template-columns: 100%;\n    grid-gap: 40px;\n    max-width: $max-wide-width;\n    margin: 0 auto;\n  }\n\n  &__title {\n    @include h4;\n\n    background-color: var(--heading-bg-color);\n    color: var(--heading-text-color);\n    line-height: 1;\n    margin-bottom: 40px;\n    padding: px-to-rem(15px);\n    text-align: center;\n    width: 100%;\n  }\n\n  @include breakpoint(m-mobile, min) {\n    &__title {\n      width: fit-content;\n    }\n  }\n\n  @include breakpoint(l-mobile, min) {\n    &__list {\n      grid-template-columns: 1fr 1fr;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    padding: 0 $tablet-gutter px-to-rem(90px);\n\n    &__title {\n      margin-bottom: 65px;\n      padding: px-to-rem(16px) px-to-rem(20px) px-to-rem(20px);\n    }\n\n    &__list {\n      grid-gap: 55px;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    padding: 0 $desktop-gutter px-to-rem(120px);\n\n    &__title {\n      margin-bottom: 70px;\n      padding: px-to-rem(14px) px-to-rem(30px) px-to-rem(20px);\n    }\n\n    &__list {\n      grid-template-columns: 1fr 1fr 1fr;\n      grid-gap: 60px;\n    }\n\n    @include breakpoint(l-desktop, min) {\n      &__list {\n        grid-gap: 70px;\n        padding: 0 $desktop-gutter;\n      }\n    }\n  }\n}\n", ".testimoniallist {\n  display: grid;\n  grid-template-columns: 1fr;\n  grid-gap: 40px 30px;\n  margin-bottom: 45px;\n  margin-top: 45px;\n\n  .testimonial {\n    margin-bottom: 0;\n    margin-top: 0;\n\n    &__blockquote {\n      align-items: flex-start;\n      display: flex;\n      flex-direction: column;\n      justify-content: space-evenly;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    grid-template-columns: 1fr 1fr;\n    grid-gap: 50px 40px;\n\n    .testimonial {\n      margin-bottom: 0;\n      margin-top: 0;\n      max-width: 600px;\n    }\n  }\n\n  @include breakpoint(nav-desktop, min) {\n    margin-bottom: 65px;\n    margin-top: 65px;\n\n    .testimonial {\n      margin-bottom: 0;\n      margin-top: 0;\n      max-width: 600px;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    grid-gap: 60px 50px;\n    margin-bottom: 90px;\n    margin-top: 90px;\n\n    .testimonial {\n      margin-bottom: 0;\n      margin-top: 0;\n      max-width: 600px;\n    }\n  }\n}\n", ".tickertape {\n  /* How long one slide is visible on screen (from entering screen to leaving it) */\n  --s-item-dur: 10s;\n  --m-item-dur: 15s;\n  --l-item-dur: 20s;\n  --xl-item-dur: 25s;\n\n  /* How many items we want to see on screen at once */\n  --s-items-visible: 3;\n  --m-items-visible: 5;\n  --l-items-visible: 8;\n  --xl-items-visible: 10;\n\n  background-color: var(--background-color);\n  color: var(--text-color);\n  overflow: hidden;\n  will-change: transform;\n\n  &:hover,\n  &:focus {\n    .tickertape__scroller {\n      animation-play-state: paused;\n    }\n  }\n\n  &__scroller {\n    animation-duration: calc(\n      var(--s-item-dur, 1s) / var(--s-items-visible) * var(--items-total)\n    );\n    animation-timing-function: linear;\n    animation-name: scrolling-gallery;\n    animation-iteration-count: infinite;\n    display: flex;\n    flex-flow: row nowrap;\n    overflow: visible;\n    padding: 0;\n    margin: 0;\n  }\n\n  &__container {\n    display: flex;\n    flex-direction: row;\n    width: 100vw;\n  }\n\n  .tickertapeitem {\n    box-sizing: border-box;\n    font-size: px-to-rem(20px);\n    font-family: $inter-font;\n    line-height: 100%;\n    padding: 10px 10px 12px;\n    text-transform: uppercase;\n    transform: scaleX(0.9);\n    white-space: nowrap;\n    width: max(fit-content, calc(100vw / var(--s-items-visible)));\n\n    &:nth-child(2n) {\n      font-weight: 900;\n      letter-spacing: 0.05em;\n      transform: scaleX(1.1);\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    &__scroller {\n      animation-duration: calc(\n        var(--m-item-dur, 1s) / var(--m-items-visible) * var(--items-total)\n      );\n    }\n\n    .tickertapeitem {\n      font-size: px-to-rem(22px);\n      width: max(fit-content, calc(100vw / var(--m-items-visible)));\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    &__scroller {\n      animation-duration: calc(\n        var(--l-item-dur, 1s) / var(--l-items-visible) * var(--items-total)\n      );\n    }\n\n    .tickertapeitem {\n      font-size: px-to-rem(24px);\n      padding: 13px 10px 17px;\n      width: max(fit-content, calc(100vw / var(--l-items-visible)));\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    &__scroller {\n      animation-duration: calc(\n        var(--xl-item-dur, 1s) / var(--xl-items-visible) * var(--items-total)\n      );\n    }\n\n    .tickertapeitem {\n      font-size: px-to-rem(30px);\n      width: max(fit-content, calc(100vw / var(--xl-items-visible)));\n    }\n  }\n}\n\n@keyframes scrolling-gallery {\n  0% {\n    transform: translateX(0);\n  }\n\n  100% {\n    transform: translateX(-100%);\n  }\n}\n", ".tooltip {\n  --after-left: auto;\n  --after-right: 0;\n  --after-tx: -15px;\n\n  display: inline;\n  position: relative;\n  z-index: 1;\n\n  &:hover,\n  &:focus,\n  &:focus-within {\n    .tooltip__content {\n      display: block;\n    }\n  }\n\n  &__trigger {\n    color: $electoral-blue;\n    font-family: $inter-font;\n    font-size: px-to-rem(10px);\n    line-height: 1.5;\n    padding: 0;\n    position: relative;\n    left: px-to-rem(-2px);\n    top: px-to-rem(-8px);\n    z-index: 1;\n\n    &:hover,\n    &:focus,\n    &:focus-within {\n      + .tooltip__content {\n        display: block;\n      }\n    }\n  }\n\n  &__triggerspan {\n    border: 1px solid $electoral-blue;\n    border-radius: 50%;\n    color: $electoral-blue;\n    display: block;\n    font-family: $inter-font;\n    font-size: px-to-rem(10px);\n    height: 15px;\n    line-height: 1.2;\n    padding-bottom: px-to-rem(2.5px);\n    width: 15px;\n  }\n\n  &__content {\n    @include caption;\n\n    background-color: rgba($crt-black, 0.9);\n    bottom: calc(100% + 20px);\n    color: $white;\n    display: none;\n    padding: px-to-rem(14px);\n    position: absolute;\n    right: -100%;\n    width: calc(100vw - 104px);\n\n    &::after {\n      /* Add a downward arrow to style the tooltip */\n      border-right: 10px solid transparent;\n      border-left: 10px solid transparent;\n      border-top: 15px solid rgba($crt-black, 0.9);\n      border-bottom: none;\n      bottom: -15px;\n      content: '';\n      height: 0;\n      left: var(--after-left);\n      position: absolute;\n      right: var(--after-right);\n      transform: translateX(var(--after-tx));\n      width: 0;\n    }\n\n    &::before {\n      background-color: rgba($crt-black, 0);\n      bottom: -30px;\n      content: '';\n      height: 30px;\n      position: absolute;\n      left: var(--after-left);\n      right: var(--after-right);\n      width: calc(100vw - 104px);\n    }\n  }\n\n  @include breakpoint(m-mobile, min) {\n    &__content {\n      width: 250px;\n\n      &::before {\n        width: 250px;\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    --after-right: auto;\n    --after-left: 115px;\n    --after-tx: 0;\n\n    &__trigger {\n      left: px-to-rem(-2px);\n      top: px-to-rem(-8px);\n    }\n\n    &__content {\n      @include caption;\n\n      background-color: rgba($crt-black, 0.9);\n      bottom: calc(100% + 20px);\n      color: $white;\n      display: none;\n      padding: px-to-rem(14px);\n      position: absolute;\n      right: calc(-125px + 50%);\n      width: 250px;\n\n      &::after {\n        left: var(--after-left);\n        right: var(--after-right);\n        transform: translateX(var(--after-tx));\n      }\n\n      &::before {\n        left: calc(-125px + 50%);\n        bottom: -30px;\n        width: 250px;\n        height: 30px;\n      }\n    }\n  }\n}\n", ".usvote {\n  &__election {\n    border-bottom: 1px solid $golden-rod;\n    margin-bottom: 30px;\n    padding-bottom: 30px;\n  }\n\n  &__name {\n    @include h4;\n\n    display: flex;\n    justify-content: space-between;\n    text-align: left;\n    width: 100%;\n\n    svg {\n      color: $electoral-blue;\n      height: 24px;\n      margin-left: 14px;\n      margin-top: 8px;\n      width: 24px;\n\n      .plus {\n        transition: transform 0.3s ease-in;\n        transform-origin: center center;\n        transform: rotate(0deg);\n      }\n    }\n  }\n\n  &__date {\n    @include body;\n\n    display: block;\n    margin-top: 15px;\n  }\n\n  &__deadline {\n    display: grid;\n    margin-bottom: 20px;\n\n    &:last-of-type {\n      margin-bottom: 0;\n    }\n  }\n\n  &__kind {\n    @include body;\n\n    font-weight: 600;\n    margin-bottom: 13px;\n  }\n\n  &__detes {\n    @include body;\n\n    list-style: disc;\n    margin-bottom: 0;\n    padding-left: 20px;\n  }\n\n  &__dete {\n    display: list-item;\n  }\n\n  &__accordionpanel {\n    height: auto;\n    overflow: hidden;\n    margin-bottom: 0;\n    padding: 0;\n    transition: height 0.3s ease-in-out;\n\n    &[aria-hidden='false'] {\n      overflow: visible;\n      padding-top: px-to-rem(20px);\n    }\n  }\n\n  &__accordionbutton {\n    padding: 0;\n    text-align: left;\n    width: 100%;\n\n    &[aria-expanded='true'] {\n      svg {\n        .plus {\n          transform: rotate(90deg);\n        }\n      }\n    }\n  }\n\n  &__additionalinfo {\n    @include body;\n\n    margin-bottom: 0;\n    margin-top: 20px;\n\n    a {\n      @include inline-link($color: $electoral-blue, $hover: $patriotic-red);\n    }\n  }\n\n  &__custom {\n    p {\n      @include body;\n    }\n\n    a {\n      @include inline-link($color: $electoral-blue, $hover: $patriotic-red);\n    }\n\n    ul {\n      @include body;\n\n      list-style: disc;\n      padding-left: 20px;\n\n      li {\n        display: list-item;\n\n        a {\n          @include inline-link($color: $electoral-blue, $hover: $patriotic-red);\n        }\n      }\n    }\n\n    ol {\n      @include body;\n\n      counter-reset: list-number;\n      list-style: decimal;\n      padding-left: 20px;\n\n      li {\n        display: list-item;\n\n        a {\n          @include inline-link($color: $electoral-blue, $hover: $patriotic-red);\n        }\n      }\n    }\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n\n  @include breakpoint(l-mobile, min) {\n    &__deadline {\n      grid-gap: 20px;\n      grid-template-columns: 200px calc(100% - 200px);\n      margin-bottom: 30px;\n\n      &:last-of-type {\n        margin-bottom: 0;\n      }\n    }\n\n    &__additionalinfo {\n      margin-bottom: 0;\n      margin-top: 30px;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    &__additionalinfo {\n      margin-bottom: 0;\n      margin-top: 40px;\n    }\n\n    &__election {\n      margin-bottom: 30px;\n      padding-bottom: 45px;\n    }\n\n    &__date {\n      margin-top: 17px;\n    }\n\n    &__deadline {\n      grid-gap: 25px;\n      grid-template-columns: 225px calc(100% - 225px);\n      margin-bottom: 40px;\n\n      &:last-of-type {\n        margin-bottom: 0;\n      }\n    }\n\n    &__accordionpanel {\n      padding: 0;\n\n      &[aria-hidden='false'] {\n        padding-top: px-to-rem(30px);\n      }\n    }\n\n    &__detes {\n      margin-bottom: 0;\n    }\n\n    &__name {\n      svg {\n        height: 20px;\n        margin-top: 12px;\n        width: 20px;\n      }\n    }\n\n    &__custom {\n      &:last-child {\n        margin-bottom: 0;\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    &__additionalinfo {\n      margin-bottom: 0;\n    }\n\n    &__date {\n      margin-top: 20px;\n    }\n\n    &__election {\n      margin-bottom: 45px;\n      padding-bottom: 60px;\n    }\n\n    &__detes {\n      margin-bottom: 0;\n    }\n\n    &__name {\n      svg {\n        height: 24px;\n        margin-left: 14px;\n        margin-top: 14px;\n        width: 24px;\n      }\n    }\n\n    &__accordionpanel {\n      padding: 0;\n\n      &[aria-hidden='false'] {\n        padding-top: px-to-rem(40px);\n      }\n    }\n\n    &__custom {\n      &:last-child {\n        margin-bottom: 0;\n      }\n    }\n  }\n}\n", ".usvotestatevotinginformation {\n  background-color: var(--background-color);\n  color: var(--text-color);\n  overflow: visible;\n  margin-bottom: 45px;\n  margin-top: 45px;\n  position: relative;\n  padding: 0 $mobile-gutter 65px;\n\n  .emphasizedlink {\n    padding: 0 px-to-rem(16px) 0 0;\n    width: calc(100% + 16px);\n\n    span {\n      display: inline;\n      padding-right: 0;\n    }\n\n    &__svg {\n      display: inline-block;\n      margin-right: -16px;\n      position: relative;\n      right: unset;\n      top: unset;\n      transition: transform 0.3s ease-in-out;\n\n      &--two {\n        margin-right: -14px;\n        transform: translateX(2px);\n      }\n    }\n\n    &:hover,\n    &:focus {\n      color: var(--text-hover-color);\n      font-style: italic;\n\n      .emphasizedlink__svg {\n        &--two {\n          transform: translateX(13px);\n        }\n      }\n    }\n  }\n\n  &__heading {\n    @include h1;\n\n    margin-left: auto;\n    margin-right: auto;\n    max-width: $max-wide-width;\n    position: relative;\n    text-align: center;\n    z-index: 1;\n\n    &--where {\n      margin-bottom: 50px;\n    }\n\n    &--state {\n      margin-bottom: 15px;\n    }\n  }\n\n  &__voting-details {\n    background-color: transparent;\n    margin: 30px auto -2px;\n    max-width: $max-title-width;\n    min-height: 140px;\n    padding: 30px;\n    position: relative;\n    z-index: 1;\n    width: 100%;\n\n    &::before {\n      background-color: $white;\n      content: '';\n      clip-path: $pixel-tips-top-mobile;\n      height: 100%;\n      inset: 0;\n      position: absolute;\n      width: 100%;\n    }\n  }\n\n  &__title {\n    color: var(--text-color);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-flow: column;\n    inset: 0;\n    opacity: 0;\n    padding-top: 65px;\n    position: absolute;\n    transition: opacity 0.3s ease-in-out 0s;\n    z-index: -1;\n    visibility: hidden;\n\n    &.show {\n      inset: unset;\n      opacity: 1;\n      position: relative;\n      top: 0;\n      transition: opacity 0.5s ease-in-out 0.1s;\n      z-index: 2;\n      visibility: visible;\n    }\n  }\n\n  &__icon {\n    color: $white;\n    position: absolute;\n    transform: translate(var(--tx), var(--ty)) rotate(var(--r));\n    z-index: 2;\n\n    &--email {\n      bottom: 200px;\n      height: 32px;\n      left: calc($mobile-gutter + 10px);\n      width: 49px;\n    }\n\n    &--eyes {\n      height: 100px;\n      right: calc($mobile-gutter + 10px);\n      top: -20px;\n      width: 100px;\n    }\n\n    &--finger {\n      height: 100px;\n      right: 0;\n      top: 60px;\n      width: 100px;\n    }\n\n    &--youdecide {\n      bottom: 150px;\n      height: 56px;\n      right: 20px;\n      width: 80px;\n    }\n\n    &--congress {\n      height: 64px;\n      left: $mobile-gutter;\n      top: $mobile-gutter;\n      width: 67px;\n    }\n  }\n\n  &__restart-link {\n    @include inline-link($color: var(--text-color), $hover: var(--text-color));\n  }\n\n  &__crosslink {\n    @include inline-link($color: var(--text-color), $hover: var(--text-color));\n\n    margin-top: 25px;\n  }\n\n  &__linklist {\n    margin-top: 0;\n  }\n\n  &__linkitem {\n    margin-bottom: 0;\n  }\n\n  &__restart {\n    margin-bottom: 0;\n    position: relative;\n    text-align: center;\n    z-index: 3;\n  }\n\n  &__wave {\n    display: none;\n    position: absolute;\n    inset: 30px calc($mobile-gutter * -1) 0;\n    width: 100vw;\n    z-index: 0;\n\n    &--mobile {\n      height: calc(100vw * (800 / 375));\n    }\n\n    &--desktop {\n      height: 100vw;\n    }\n  }\n\n  &__state-voting-details {\n    color: $crt-black;\n    display: none;\n\n    &.show {\n      display: block;\n      position: relative;\n\n      .emphasizedlinklist {\n        margin-bottom: 30px;\n        margin-top: 30px;\n      }\n    }\n  }\n\n  &__info-disclaimer {\n    font-style: italic;\n    margin-top: 16px;\n\n    a {\n      @include inline-link;\n    }\n  }\n\n  .state {\n    display: none;\n  }\n\n  &--state {\n    min-height: 100%;\n    overflow: hidden;\n    padding-bottom: 0;\n\n    .usvotestatevotinginformation {\n      &__wave {\n        &--mobile {\n          display: block;\n        }\n      }\n\n      &__icon {\n        &--youdecide {\n          display: none;\n        }\n\n        &--email {\n          left: -22px;\n        }\n\n        &--finger {\n          right: -22px;\n        }\n\n        &--congress {\n          left: 0;\n        }\n      }\n    }\n\n    .state {\n      &.show {\n        bottom: 20px;\n        display: block;\n        height: 80px;\n        position: absolute;\n        right: 0;\n        width: 80px;\n        z-index: 2;\n      }\n    }\n  }\n\n  @include breakpoint(mobile, max) {\n    &__heading {\n      font-size: min(24vmin, 105px);\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    margin-bottom: 65px;\n    margin-top: 65px;\n    padding: 0 $tablet-gutter 80px;\n\n    &__title {\n      padding-top: 80px;\n    }\n\n    &__linklist {\n      margin-top: 0;\n    }\n\n    &__linkitem {\n      margin-bottom: 0;\n    }\n\n    .emphasizedlink {\n      padding-right: px-to-rem(18px);\n      width: calc(100% + 18px);\n\n      &__svg {\n        margin-right: -18px;\n\n        &--two {\n          margin-right: -14px;\n          transform: translateX(4px);\n        }\n      }\n\n      &:hover,\n      &:focus {\n        .emphasizedlink__svg {\n          &--two {\n            transform: translateX(15px);\n          }\n        }\n      }\n    }\n\n    &__icon {\n      &--finger {\n        top: 100px;\n      }\n\n      &--youdecide {\n        bottom: 50px;\n        height: 56px;\n        right: 0;\n        width: 80px;\n      }\n    }\n\n    &__heading {\n      &--where {\n        margin-bottom: 60px;\n      }\n\n      &--state {\n        margin-bottom: 20px;\n      }\n    }\n\n    &__voting-details {\n      padding: 50px;\n      margin-top: 60px;\n      min-height: 270px;\n\n      &::after {\n        clip-path: $pixel-tips-top-desktop;\n      }\n    }\n\n    &__state-voting-details {\n      .show {\n        .emphasizedlinklist {\n          margin-bottom: 30px;\n          margin-top: 30px;\n        }\n      }\n    }\n\n    &__info-disclaimer {\n      margin-top: 16px;\n    }\n\n    &--state {\n      padding-bottom: 0;\n\n      .state {\n        &.show {\n          bottom: 50px;\n          height: 150px;\n          right: 0;\n          width: 150px;\n        }\n      }\n\n      .usvotestatevotinginformation {\n        &__wave {\n          inset: 30px calc($desktop-gutter * -1) 0;\n\n          &--mobile {\n            display: none;\n          }\n\n          &--desktop {\n            display: block;\n          }\n        }\n\n        &__icon {\n          &--email {\n            bottom: unset;\n            left: 32px;\n            top: 200px;\n          }\n\n          &--finger {\n            bottom: 250px;\n            right: -22px;\n            top: unset;\n          }\n\n          &--congress {\n            left: 10px;\n          }\n\n          &--youdecide {\n            bottom: unset;\n            display: block;\n            top: 100px;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-bottom: 90px;\n    margin-top: 90px;\n    padding: 0 $desktop-gutter 100px;\n\n    &__title {\n      padding-top: 100px;\n    }\n\n    &__linklist {\n      margin-top: 0;\n    }\n\n    &__linkitem {\n      margin-bottom: 0;\n    }\n\n    &__icon {\n      &--email {\n        top: 250px;\n        height: 50px;\n        left: unset;\n        right: 100px;\n        width: 75px;\n      }\n\n      &--eyes {\n        bottom: unset;\n        height: 170px;\n        left: 45%;\n        top: 0;\n        width: 170px;\n      }\n\n      &--finger {\n        bottom: 145px;\n        height: 160px;\n        left: calc(50vw - 500px);\n        right: unset;\n        top: unset;\n        width: 160px;\n      }\n\n      &--youdecide {\n        display: block;\n        height: 88px;\n        right: 100px;\n        top: 0;\n        width: 115px;\n      }\n\n      &--congress {\n        height: 110px;\n        left: calc(50vw - 500px);\n        top: 100px;\n        width: 115px;\n      }\n    }\n\n    &__heading {\n      &--where {\n        margin-bottom: 60px;\n      }\n    }\n\n    &__voting-details {\n      margin-top: 70px;\n      min-height: 390px;\n      padding: 70px 70px 60px;\n    }\n\n    &__state-voting-details {\n      .show {\n        .emphasizedlinklist {\n          margin-bottom: 30px;\n          margin-top: 30px;\n        }\n      }\n    }\n\n    &__resource-heading {\n      margin-top: 60px;\n    }\n\n    &__info-disclaimer {\n      margin-top: 14px;\n    }\n\n    &--state {\n      padding-bottom: 0;\n\n      .state {\n        &.show {\n          bottom: 60px;\n          height: 250px;\n          right: -22px;\n          width: 250px;\n        }\n      }\n\n      .usvotestatevotinginformation {\n        &__wave {\n          inset: 45px calc($desktop-gutter * -1) 0;\n        }\n\n        &__icon {\n          &--email {\n            top: 50%;\n            height: 50px;\n            left: unset;\n            right: calc(50vw - 500px);\n            width: 75px;\n          }\n\n          &--eyes {\n            bottom: unset;\n            height: 170px;\n            left: 45%;\n            top: 0;\n            width: 170px;\n          }\n\n          &--finger {\n            bottom: 145px;\n            height: 160px;\n            left: -45px;\n            right: unset;\n            top: unset;\n            width: 160px;\n          }\n\n          &--youdecide {\n            display: block;\n            height: 88px;\n            right: calc(50vw - 450px);\n            top: 0;\n            width: 115px;\n          }\n\n          &--congress {\n            height: 90px;\n            left: 10px;\n            top: 100px;\n            width: 95px;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    &__heading {\n      &--where {\n        margin-bottom: 70px;\n      }\n    }\n\n    &__voting-details {\n      min-height: 490px;\n      padding-left: 100px;\n      padding-right: 100px;\n    }\n\n    &__icon {\n      &--email {\n        right: calc(50vw - 550px);\n      }\n\n      &--finger {\n        left: calc(50vw - 600px);\n      }\n\n      &--youdecide {\n        top: 100px;\n        right: calc(50vw - 400px);\n      }\n    }\n\n    &--state {\n      padding-bottom: 0;\n\n      .usvotestatevotinginformation {\n        &__wave {\n          inset: 60px -50px 0;\n        }\n\n        &__icon {\n          &--youdecide {\n            top: 0;\n          }\n\n          &--congress {\n            height: 110px;\n            left: calc(50vw - 610px);\n            width: 115px;\n          }\n\n          &--finger {\n            left: calc(50vw - 660px);\n          }\n\n          &--email {\n            right: calc(50vw - 600px);\n          }\n        }\n      }\n\n      .state {\n        &.show {\n          height: 350px;\n          right: calc((100vw - $max-title-width - 275px) / 2);\n          width: 350px;\n        }\n      }\n    }\n  }\n\n  @include breakpoint(xl-desktop, min) {\n    &--state {\n      .usvotestatevotinginformation {\n        &__icon {\n          &--congress {\n            left: calc(50vw - 700px);\n          }\n        }\n      }\n    }\n  }\n}\n", ".verticalvideo {\n  background-color: var(--background-color);\n  margin-bottom: 65px;\n  margin-top: 65px;\n  max-width: 100vw;\n  overflow: hidden;\n  padding-left: $mobile-gutter;\n  position: relative;\n\n  &__carousel {\n    height: fit-content;\n    padding-top: 60px;\n    max-height: 200vh;\n    min-height: 0;\n    min-width: 0;\n    max-width: 100%;\n    overflow: hidden;\n    position: relative;\n    width: 100%;\n  }\n\n  &__caption {\n    margin-top: 12px;\n\n    p {\n      @include caption;\n\n      color: var(--text-color);\n      margin-bottom: 0;\n    }\n  }\n\n  &__slide {\n    flex-shrink: 0;\n    display: block;\n    height: fit-content;\n    min-width: 288px;\n    min-height: 510px;\n    width: 288px;\n  }\n\n  &__video {\n    height: 510px;\n    min-width: 288px;\n    width: 288px;\n  }\n\n  &__wrapper {\n    display: flex;\n    flex-flow: row nowrap;\n  }\n\n  &__title {\n    @include h4;\n\n    color: var(--text-color);\n    margin-bottom: 20px;\n    padding-right: $mobile-gutter;\n    text-align: center;\n  }\n\n  &__intro {\n    color: var(--text-color);\n    text-align: center;\n    padding-right: $mobile-gutter;\n  }\n\n  &__pause,\n  &__play {\n    margin: 0 auto 18px;\n    padding: 0;\n    text-decoration: underline;\n    text-align: center;\n    width: 100%;\n  }\n\n  &__pause,\n  &__play,\n  &__button-prev,\n  &__button-next {\n    color: var(--button-color);\n\n    &:hover,\n    &:focus {\n      color: var(--button-hover-color);\n    }\n  }\n\n  &__button-next {\n    padding: 20px;\n    position: absolute;\n    right: calc(50% - 61px);\n    top: -20px;\n    transition: transform 0.3s ease-in-out;\n\n    &:hover,\n    &:focus {\n      transform: translateX(5px);\n    }\n\n    &.swiper-button-lock {\n      display: none;\n    }\n  }\n\n  &__button-prev {\n    padding: 20px;\n    position: absolute;\n    top: -20px;\n    left: calc(50% - 61px);\n    transition: transform 0.3s ease-in-out;\n\n    &:hover,\n    &:focus {\n      transform: translateX(-5px);\n    }\n\n    &.swiper-button-lock {\n      display: none;\n    }\n\n    svg {\n      transform: rotate(180deg);\n    }\n  }\n\n  &--background {\n    padding-bottom: 50px;\n    padding-top: 50px;\n\n    .verticalvideo {\n      &__button-prev,\n      &__button-next {\n        top: -20px;\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    padding-left: calc($tablet-gutter / 2);\n\n    &__pause,\n    &__play {\n      margin-top: 0;\n      margin-bottom: 40px;\n      margin-left: 0;\n      width: fit-content;\n    }\n\n    &__title,\n    &__intro {\n      margin-left: 0;\n      margin-right: auto;\n      max-width: calc(100vw - $mobile-gutter - 160px);\n      text-align: left;\n    }\n\n    &__button-next {\n      right: $mobile-gutter;\n    }\n\n    &__button-prev {\n      left: unset;\n      right: calc($mobile-gutter + 60px);\n    }\n\n    &__carousel {\n      max-height: 100vw;\n      padding-top: 0;\n      position: static;\n    }\n\n    &--background {\n      padding-bottom: 60px;\n      padding-top: 60px;\n\n      .verticalvideo {\n        &__button-prev,\n        &__button-next {\n          top: 40px;\n        }\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    padding-left: $desktop-gutter;\n\n    &__caption {\n      margin-top: 30px;\n    }\n\n    &__pause,\n    &__play {\n      margin-bottom: 60px;\n    }\n\n    &__slide {\n      height: fit-content;\n      min-width: 310px;\n      width: 310px;\n    }\n\n    &__video {\n      height: 550px;\n      min-width: 310px;\n      width: 310px;\n    }\n\n    &__button-next {\n      right: $desktop-gutter;\n    }\n\n    &__button-prev {\n      right: calc($desktop-gutter + 60px);\n    }\n\n    &__title,\n    &__intro {\n      margin-left: 0;\n      margin-right: auto;\n      max-width: calc(100vw - $desktop-gutter - 160px);\n    }\n\n    &--background {\n      padding-bottom: 70px;\n      padding-top: 70px;\n\n      .verticalvideo {\n        &__button-prev,\n        &__button-next {\n          top: 50px;\n        }\n      }\n    }\n  }\n\n  @include breakpoint(wide-desktop, min) {\n    padding-left: calc((100vw - $max-wide-width) / 2);\n\n    &__title,\n    &__intro {\n      max-width: $max-text-width;\n    }\n  }\n\n  @include breakpoint(ta-desktop, min) {\n    &__button-next {\n      right: calc(((100vw - $max-wide-width) / 2) - 20px);\n    }\n\n    &__button-prev {\n      right: calc(((100vw - $max-wide-width) / 2) + 40px);\n    }\n  }\n}\n", ".video {\n  align-items: center;\n  display: grid;\n  grid-template-columns: 1fr;\n  margin-bottom: 45px;\n  margin-top: 45px;\n\n  &--caption {\n    border-bottom: 6px solid var(--border-color);\n    border-top: 6px solid var(--border-color);\n    padding-bottom: 30px;\n    padding-top: 30px;\n  }\n\n  &__video {\n    clip-path: $pixel-tips-mobile;\n    height: 0;\n    position: relative;\n    padding-bottom: 56.25%; /* 16:9 */\n    width: 100%;\n  }\n\n  &__poster {\n    color: var(--icon-color);\n    padding: 0;\n    position: relative;\n    width: 100%;\n\n    svg {\n      height: 48px;\n      left: calc(50% - 24px);\n      position: absolute;\n      top: calc(50% - 24px);\n      transition: all 0.3s ease-in-out;\n      width: 48px;\n      z-index: 1;\n    }\n\n    &.click {\n      display: none;\n    }\n\n    &::after {\n      background: $crt-black;\n      content: '';\n      height: 100%;\n      left: 0;\n      opacity: 0.25;\n      position: absolute;\n      top: 0;\n      transition: opacity 0.3s ease-in-out;\n      width: 100%;\n    }\n\n    &::before {\n      background: radial-gradient(\n        ellipse at center,\n        rgb(255 255 255 / 0%) 0%,\n        rgb(0 0 0 / 0%) 65%,\n        rgb(0 0 0 / 40%) 100%\n      );\n      content: '';\n      height: 100%;\n      left: 0;\n      opacity: 0;\n      position: absolute;\n      top: 0;\n      transition: opacity 0.3s ease-in-out;\n      width: 100%;\n    }\n\n    &:hover,\n    &:focus {\n      color: var(--icon-hover-color);\n\n      &::after {\n        opacity: 0;\n      }\n\n      &::before {\n        opacity: 1;\n      }\n    }\n  }\n\n  &__image {\n    height: 100%;\n    object-fit: cover;\n    width: 100%;\n  }\n\n  &__embed {\n    display: none;\n    height: 100%;\n    left: 0;\n    position: absolute;\n    top: 0;\n    width: 100%;\n\n    .playing & {\n      display: block;\n    }\n  }\n\n  &__caption {\n    grid-row: 1;\n    margin-bottom: 30px;\n\n    .caption {\n      &__title {\n        @include h4;\n\n        font-style: normal;\n        margin-bottom: 0;\n      }\n\n      &__text {\n        @include caption;\n\n        margin-bottom: 0;\n        margin-top: 20px;\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    border-bottom: none;\n    border-top: none;\n    margin-bottom: 60px;\n    margin-top: 60px;\n\n    &--caption {\n      grid-template-columns: calc(70% - 25px) calc(30% - 25px);\n      grid-column-gap: 50px;\n      padding-bottom: 0;\n      padding-top: 0;\n    }\n\n    &__caption {\n      border-bottom: 12px solid var(--border-color);\n      border-top: 12px solid var(--border-color);\n      grid-column: 2;\n      margin-bottom: 0;\n      padding-bottom: 45px;\n      padding-top: 45px;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    margin-bottom: 90px;\n    margin-top: 90px;\n\n    &--caption {\n      grid-template-columns: calc(70% - 35px) calc(30% - 35px);\n      grid-column-gap: 70px;\n    }\n\n    &__video {\n      clip-path: $pixel-tips-desktop;\n    }\n\n    &__poster {\n      svg {\n        height: 75px;\n        left: calc(50% - 37.5px);\n        position: absolute;\n        top: calc(50% - 37.5px);\n        width: 75px;\n      }\n    }\n  }\n}\n", ".videolisting {\n  border-bottom: 6px solid var(--border-color);\n  border-top: 6px solid var(--border-color);\n  display: grid;\n  grid-template-columns: 1fr;\n  grid-gap: 40px;\n  margin-bottom: 45px;\n  margin-top: 45px;\n  padding-bottom: 35px;\n  padding-top: 30px;\n\n  .video {\n    align-items: flex-start;\n    display: flex;\n    flex-direction: column;\n    justify-content: flex-start;\n    height: 100%;\n    margin-bottom: 0;\n    margin-top: 0;\n\n    &__video {\n      order: 2;\n    }\n\n    &__caption {\n      order: 1;\n    }\n  }\n\n  &__video {\n    padding: 0;\n    border-bottom: none;\n    border-top: none;\n\n    .video__caption {\n      border-bottom: none;\n      border-top: none;\n      padding-top: 0;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    border-bottom: 12px solid var(--border-color);\n    border-top: 12px solid var(--border-color);\n    grid-template-columns: calc(50% - 25px) calc(50% - 25px);\n    grid-gap: 50px;\n    margin-bottom: 65px;\n    margin-top: 65px;\n    padding-bottom: 45px;\n    padding-top: 40px;\n\n    .video {\n      margin-bottom: 0;\n      margin-top: 0;\n    }\n\n    &__video {\n      grid-template-columns: 1fr;\n      padding: 0;\n\n      .video__caption {\n        grid-column: 1;\n        padding-top: 0;\n        padding-bottom: 30px;\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    grid-template-columns: calc(50% - 30px) calc(50% - 30px);\n    grid-gap: 60px;\n    margin-bottom: 90px;\n    margin-top: 90px;\n    padding-bottom: 50px;\n    padding-top: 45px;\n\n    .video {\n      margin-bottom: 0;\n      margin-top: 0;\n    }\n  }\n}\n", ".vote {\n  margin-left: auto;\n  margin-right: auto;\n  max-width: $max-text-width;\n\n  h2 {\n    @include community-stats;\n\n    margin-bottom: 26px;\n    text-align: center;\n  }\n\n  p {\n    @include body;\n\n    text-align: center;\n\n    &:last-of-type {\n      margin-bottom: 0;\n    }\n  }\n\n  .civicengine-embed-loaded {\n    background-color: transparent !important;\n    margin-top: 30px;\n    padding: 0;\n  }\n\n  @include breakpoint(l-mobile, min) {\n    h2 {\n      text-align: center;\n    }\n\n    p {\n      text-align: center;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    .civicengine-embed-loaded {\n      margin-top: 40px;\n    }\n\n    h2 {\n      @include h2;\n    }\n\n    p {\n      &:last-of-type {\n        margin-bottom: 0;\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    padding: 0 50px;\n\n    .civicengine-embed-loaded {\n      margin-top: 50px;\n      padding: 0;\n    }\n\n    h2 {\n      margin-bottom: 40px;\n    }\n\n    p {\n      &:last-of-type {\n        margin-bottom: 0;\n      }\n    }\n  }\n}\n", ".votehero {\n  padding: 85px $mobile-gutter;\n  position: relative;\n  margin-bottom: 50px;\n\n  $self: &;\n\n  &--toc {\n    padding-bottom: 50px;\n  }\n\n  &__bg-image {\n    background-size: cover;\n    background-position: center center;\n    inset: 0;\n    position: absolute;\n  }\n\n  &__bg-swirl-wrap {\n    align-items: center;\n    display: flex;\n    flex-direction: column;\n    inset: 0;\n    justify-content: center;\n    position: absolute;\n    overflow: hidden;\n    z-index: 0;\n  }\n\n  &__bg-swirl {\n    left: 0;\n    min-width: max(calc(100vh - 93px), 100vw, 100%);\n    min-height: max(calc(100vh - 93px), 100vw, 100%);\n    right: 0;\n  }\n\n  &__content {\n    display: flex;\n    flex-flow: row wrap;\n    max-width: $max-title-width;\n    margin: 0 auto;\n    position: relative;\n    z-index: 1;\n\n    h1 {\n      margin-bottom: 0;\n      width: 100%;\n    }\n\n    p {\n      margin-bottom: 0;\n      margin-top: 26px;\n      max-width: $max-text-width;\n      width: 100%;\n    }\n\n    .tableofcontents {\n      margin-top: 50px;\n      width: 100%;\n    }\n  }\n\n  .emphasizedlinklist {\n    grid-row-gap: 10px;\n  }\n\n  .ballotreadyinput {\n    margin-top: 30px;\n    width: 100%;\n    max-width: 760px;\n  }\n\n  + .tickertape {\n    margin-top: -50px;\n  }\n\n  @include breakpoint(tablet, min) {\n    margin-bottom: 60px;\n    padding: 100px $tablet-gutter;\n\n    &--toc {\n      padding-bottom: 65px;\n    }\n\n    + .tickertape {\n      margin-top: -60px;\n    }\n  }\n\n  @include breakpoint(nav-desktop, min) {\n    &--toc {\n      padding-bottom: 95px;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    padding-right: $desktop-gutter;\n    margin-bottom: 70px;\n\n    &--toc {\n      #{$self} {\n        &__content {\n          display: grid;\n          grid-template-columns: calc(100% - 260px) 220px;\n          grid-column-gap: 40px;\n          max-width: 1115px;\n          transform: translateX(0);\n        }\n      }\n\n      h1 {\n        grid-column: 1 / span 2;\n      }\n\n      p {\n        grid-column: 1;\n        grid-row: 2;\n      }\n\n      nav + h1 + p {\n        grid-row: 3;\n      }\n\n      .ballotreadyinput {\n        grid-column: 1;\n        grid-row: 3;\n        margin-top: 30px;\n      }\n\n      .tableofcontents {\n        grid-column: 2;\n        grid-row: 2 / span 2;\n        margin-left: auto;\n        margin-right: 0;\n        margin-top: 30px;\n        max-width: 220px;\n        width: auto;\n      }\n\n      div.ballotreadyinput + div.tableofcontents {\n        grid-row: 3 / span 1;\n        margin-top: 57px;\n      }\n    }\n\n    &__content {\n      margin: 0 auto;\n    }\n\n    .emphasizedlinklist {\n      grid-row-gap: 20px;\n    }\n\n    + .tickertape {\n      margin-top: -70px;\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    &--toc {\n      #{$self} {\n        &__content {\n          display: grid;\n          grid-column-gap: 60px;\n          grid-template-columns: 600px 300px;\n        }\n      }\n\n      .tableofcontents {\n        max-width: 300px;\n        width: 100%;\n      }\n\n      div.ballotreadyinput + div.tableofcontents {\n        margin-top: 63px;\n      }\n    }\n  }\n\n  @include breakpoint(xl-desktop, min) {\n    padding-right: $desktop-gutter;\n    margin-bottom: 80px;\n\n    &__content {\n      margin: 0 auto;\n      transform: translateX(-90px);\n    }\n\n    &--toc {\n      #{$self} {\n        &__content {\n          grid-column-gap: 80px;\n          grid-template-columns: 650px 320px;\n        }\n      }\n\n      .tableofcontents {\n        max-width: 320px;\n      }\n    }\n\n    + .tickertape {\n      margin-top: -80px;\n    }\n  }\n}\n", ".winstat {\n  @include h2;\n\n  color: var(--text-color);\n  position: relative;\n  text-align: center;\n  width: fit-content;\n\n  &__checkmark,\n  &__circle {\n    color: var(--handdrawn-color);\n  }\n\n  &__circlewrap {\n    position: relative;\n  }\n\n  &__circle {\n    animation: dashcirclereverse 0.3s ease-in;\n    animation-fill-mode: forwards;\n    inset: -22% 0 0 -25%;\n    height: 130%;\n    position: absolute;\n    stroke-dasharray: 550;\n    stroke-dashoffset: 550;\n    width: 130%;\n    z-index: 1;\n  }\n\n  &__checkmark {\n    height: 43px;\n    position: absolute;\n    right: -55px;\n    stroke-dasharray: 250;\n    stroke-dashoffset: 250;\n    top: 7px;\n    width: 50px;\n    z-index: 1;\n  }\n\n  .handdrawnunderline {\n    display: inline-block;\n    position: relative;\n\n    &::after {\n      content: ' ';\n      background-repeat: no-repeat;\n      background-size: contain;\n      bottom: -28px;\n      height: 27px;\n      left: 0;\n      position: absolute;\n      right: 0;\n      width: 100%;\n    }\n  }\n\n  &[data-visible='true'][data-halfway='above-halfway']:not(.hide) {\n    .winstat {\n      &__circle {\n        animation: dashcircle 0.7s ease-in 1.5s;\n        animation-fill-mode: forwards;\n      }\n\n      &__checkmark {\n        animation: dashcircle 0.5s ease-in 1s;\n        animation-fill-mode: forwards;\n      }\n    }\n\n    .handdrawnunderline {\n      display: inline-block;\n      position: relative;\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    &__checkmark {\n      height: 47px;\n      margin-bottom: 13px;\n      width: 55px;\n    }\n\n    .handdrawnunderline {\n      &::after {\n        bottom: -35px;\n        height: 35px;\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    &__checkmark {\n      height: 52px;\n      margin-bottom: 0;\n      margin-left: 15px;\n      right: -67px;\n      top: 35px;\n      width: 60px;\n    }\n\n    .handdrawnunderline {\n      &::after {\n        bottom: -40px;\n        height: 40px;\n      }\n    }\n  }\n\n  @keyframes dashcircle {\n    to {\n      stroke-dashoffset: 0;\n    }\n  }\n\n  @keyframes dashcirclereverse {\n    from {\n      stroke-dashoffset: 0;\n    }\n\n    to {\n      stroke-dashoffset: 550;\n    }\n  }\n}\n", ".winstatlistsection {\n  background-color: var(--bg-color);\n  color: var(--heading-color);\n  display: grid;\n  grid-template-rows: 1fr;\n  opacity: 1;\n  overflow-x: clip;\n  overflow-y: visible;\n  padding: px-to-rem(60px) 0 px-to-rem(33px);\n  position: relative;\n  transition: visibility 0s ease 0s, opacity 0.15s ease-in-out 0.1s,\n    grid-template-rows 0.3s ease-in-out 0.1s;\n  visibility: visible;\n\n  &__content {\n    overflow: hidden;\n    padding: 0 $mobile-gutter px-to-rem(27px);\n  }\n\n  &.hide {\n    grid-template-rows: 0fr;\n    opacity: 0;\n    padding: 0;\n    visibility: hidden;\n\n    .winstatlistsection {\n      &__content {\n        overflow: hidden;\n        padding: 0 $mobile-gutter;\n      }\n    }\n  }\n\n  &__icon {\n    color: var(--icon-color);\n    height: px-to-rem(35px);\n    position: absolute;\n    transform: translate(var(--tx), var(--ty)) rotate(var(--r))\n      scale(var(--scale));\n    width: px-to-rem(35px);\n    z-index: 1;\n\n    &:nth-child(1) {\n      left: -5px;\n      top: 75px;\n    }\n\n    &:nth-child(2) {\n      right: 7px;\n      bottom: 30%;\n    }\n\n    &:nth-child(3) {\n      left: 22px;\n      bottom: -15px;\n    }\n  }\n\n  &__state {\n    color: var(--icon-color);\n    height: px-to-rem(68px);\n    position: absolute;\n    width: px-to-rem(68px);\n    z-index: 1;\n\n    &:nth-child(1) {\n      left: 14px;\n      top: -25px;\n    }\n\n    &:nth-child(2) {\n      right: 24px;\n      top: -10px;\n    }\n\n    &:nth-child(3) {\n      right: -10px;\n      top: 120px;\n    }\n\n    &:nth-child(4) {\n      left: -10px;\n      bottom: calc(27% + 65px);\n    }\n\n    &:nth-child(5) {\n      left: -15px;\n      bottom: calc(27%);\n    }\n\n    &:nth-child(6) {\n      right: -20px;\n      bottom: 10px;\n    }\n\n    &:nth-child(7) {\n      right: 40px;\n      bottom: -5px;\n    }\n\n    &:nth-child(8) {\n      display: none;\n    }\n  }\n\n  .winstatlist {\n    align-items: center;\n    display: flex;\n    flex-direction: column;\n    margin-top: 40px;\n    position: relative;\n    z-index: 2;\n  }\n\n  .heading2 {\n    font-family: $inter-font;\n    font-size: px-to-rem(22px);\n    font-weight: 400;\n    line-height: 140%;\n    padding-left: $mobile-gutter;\n    padding-right: $mobile-gutter;\n    text-align: center;\n  }\n\n  @include breakpoint(tablet, min) {\n    padding: px-to-rem(90px) 0 px-to-rem(53px);\n\n    &__content {\n      padding: 0 calc($tablet-gutter + 10px) 27px;\n    }\n\n    &.hide {\n      padding: 0;\n\n      .winstatlistsection {\n        &__content {\n          overflow: hidden;\n          padding: 0 calc($tablet-gutter + 10px);\n        }\n      }\n    }\n\n    &__icon {\n      height: px-to-rem(50px);\n      width: px-to-rem(50px);\n    }\n\n    &__state {\n      height: px-to-rem(100px);\n      width: px-to-rem(100px);\n\n      &:nth-child(4) {\n        left: -10px;\n        bottom: calc(30% + 100px);\n      }\n\n      &:nth-child(5) {\n        left: -20px;\n        bottom: calc(20%);\n      }\n\n      &:nth-child(6) {\n        bottom: -10px;\n        right: 75px;\n      }\n\n      &:nth-child(7) {\n        bottom: 20px;\n        right: -20px;\n      }\n    }\n\n    .winstatlist {\n      margin-top: 45px;\n    }\n\n    .heading2 {\n      font-size: px-to-rem(26px);\n      padding-left: 0;\n      padding-right: 0;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    padding: px-to-rem(120px) 0 px-to-rem(63px);\n\n    &__content {\n      padding: 0 $desktop-gutter 27px;\n    }\n\n    &.hide {\n      padding: 0;\n\n      .winstatlistsection {\n        &__content {\n          padding: 0 $desktop-gutter;\n        }\n      }\n    }\n\n    &__state {\n      height: px-to-rem(130px);\n      width: px-to-rem(130px);\n\n      &:nth-child(1) {\n        left: 48px;\n        top: -55px;\n      }\n\n      &:nth-child(2) {\n        right: -24px;\n        top: 35%;\n      }\n\n      &:nth-child(3) {\n        right: 50px;\n        top: 80px;\n      }\n\n      &:nth-child(4) {\n        bottom: 25%;\n      }\n\n      &:nth-child(5) {\n        bottom: 0;\n        left: 100px;\n      }\n\n      &:nth-child(6) {\n        bottom: -75px;\n        right: 90px;\n      }\n\n      &:nth-child(7) {\n        bottom: 20px;\n        right: 0;\n      }\n\n      &:nth-child(8) {\n        display: block;\n        left: 70px;\n        top: 60px;\n      }\n    }\n\n    &__icon {\n      height: px-to-rem(83px);\n      width: px-to-rem(83px);\n\n      &:nth-child(1) {\n        left: -5px;\n        top: 175px;\n      }\n\n      &:nth-child(2) {\n        right: 7px;\n        bottom: 30%;\n      }\n\n      &:nth-child(3) {\n        left: 40px;\n        bottom: -40px;\n      }\n    }\n\n    .winstatlist {\n      margin-top: 50px;\n    }\n\n    .heading2 {\n      font-size: px-to-rem(30px);\n    }\n  }\n}\n", "/* stylelint-disable */\n\n#cmplz-cookiebanner-container {\n  --cmplz_banner_width: 800px;\n  background: rgba(0, 0, 0, 0.14);\n  bottom: 0vh;\n  box-shadow: 0 -8px 24px rgba(0, 0, 0, 0.24);\n  height: auto;\n  max-height: calc(100vh - 20px);\n  position: fixed;\n  max-width: var(--cmplz_banner_width);\n  width: 100%;\n  right: 0;\n  z-index: 999999;\n\n  .cmplz-cookiebanner {\n    clip-path: $pixel-tips-top-mobile;\n    grid-row-gap: 0;\n    padding: $mobile-gutter;\n    position: relative;\n    right: 0;\n\n    .cmplz-close {\n      @include click-cursor;\n\n      align-self: flex-start;\n      height: 24px;\n      margin-top: 0;\n      width: 24px;\n\n      svg {\n        color: $electoral-blue;\n        height: 24px;\n        width: 24px;\n      }\n    }\n\n    .cmplz-cookie {\n      height: 70px;\n      position: relative;\n      margin-bottom: -10px;\n      margin-left: -10px;\n      margin-right: 10px;\n      margin-top: -10px;\n      width: 70px;\n    }\n\n    .cmplz-title {\n      @include h4;\n\n      display: flex;\n      flex-direction: row;\n      align-items: center;\n      grid-column: 1 / span 2;\n      justify-self: flex-start;\n      margin-bottom: 20px;\n      margin-left: 0;\n    }\n\n    .cmplz-body {\n      @include body;\n\n      margin-bottom: 6px;\n    }\n\n    .cmplz-categories {\n      .cmplz-category {\n        .cmplz-banner-checkbox {\n          input.cmplz-consent-checkbox {\n            @include click-cursor;\n          }\n        }\n      }\n    }\n\n    .cmplz-buttons {\n      display: grid;\n      grid-column-gap: 10px;\n      grid-row-gap: 18px;\n      grid-template-columns: 150px 150px;\n      margin-bottom: -21px;\n\n      .cmplz-btn {\n        @include click-cursor;\n\n        &.cmplz-accept {\n          @include button(\n            $color: $patriotic-red,\n            $hover: $patriotic-red-dark,\n            $text-color: $white\n          );\n\n          height: fit-content;\n          width: 150px;\n        }\n\n        &.cmplz-deny {\n          @include body;\n          @include secondary-button(\n            $color: $patriotic-red,\n            $hover: $patriotic-red-dark\n          );\n\n          display: inline-block;\n          height: fit-content;\n          width: 150px;\n        }\n\n        &.cmplz-save-preferences,\n        &.cmplz-view-preferences {\n          @include body;\n          @include inline-link($color: $crt-black, $hover: $patriotic-red);\n\n          border: none;\n          height: fit-content;\n          padding: 0;\n          width: fit-content;\n        }\n\n        &.cmplz-save-preferences {\n          display: none;\n        }\n      }\n    }\n\n    .cmplz-links.cmplz-documents {\n      justify-content: flex-start;\n      margin-left: 152px;\n      margin-top: -1px;\n\n      .cmplz-link {\n        &.privacy-statement {\n          @include body;\n          @include click-cursor;\n          @include inline-link($color: $crt-black, $hover: $patriotic-red);\n\n          text-decoration: none;\n        }\n\n        &.cookie-statement {\n          display: none;\n        }\n      }\n    }\n  }\n\n  @include breakpoint(mobile, min) {\n    .cmplz-cookiebanner {\n      .cmplz-buttons {\n        grid-template-columns: 175px 175px;\n\n        .cmplz-btn {\n          &.cmplz-accept,\n          &.cmplz-deny {\n            width: 175px;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    .cmplz-cookiebanner {\n      bottom: 0;\n      padding: 30px;\n\n      .cmplz-close {\n        margin-top: 0;\n      }\n\n      .cmplz-buttons {\n        margin-bottom: -24px;\n\n        .cmplz-btn {\n          &.cmplz-deny {\n            font-size: 1.25rem;\n          }\n        }\n      }\n\n      .cmplz-body {\n        margin-bottom: 10px;\n      }\n\n      .cmplz-links.cmplz-documents {\n        margin-left: 168px;\n      }\n    }\n\n    .cmplz-message,\n    .cmplz-categories {\n      width: 100%;\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    .cmplz-cookiebanner {\n      clip-path: $pixel-tips-top-desktop;\n      padding: 40px;\n\n      .cmplz-close {\n        height: 50px;\n        position: relative;\n        right: -10px;\n        top: -10px;\n        width: 50px;\n\n        svg {\n          height: 50px;\n          padding: 10px;\n          width: 50px;\n        }\n      }\n\n      .cmplz-title {\n        margin-left: 0;\n      }\n\n      .cmplz-cookie {\n        height: 90px;\n        position: relative;\n        margin-bottom: -20px;\n        margin-left: -15px;\n        margin-right: 10px;\n        margin-top: -20px;\n        width: 90px;\n      }\n\n      .cmplz-buttons {\n        grid-row-gap: 24px;\n      }\n    }\n  }\n}\n", "@use 'sass:map';\n\n/**\n  FOOTER COMPONENT\n**/\n\n.footer {\n  background-color: $crt-black;\n  color: $white;\n  padding: px-to-rem(46px) px-to-rem($mobile-gutter);\n  position: relative;\n  overflow: hidden;\n\n  $self: &;\n\n  &__background {\n    background-color: $crt-black;\n    position: absolute;\n    inset: 0;\n    width: 100%;\n    height: 100%;\n    z-index: 0;\n\n    .spiral,\n    svg {\n      color: $crt-black-light;\n      height: 150%;\n      transform-origin: center;\n      position: absolute;\n      left: 50%;\n      top: 50%;\n      transform: translate(-50%, calc(-50% + 125px));\n      min-height: 150%;\n      width: 600%;\n    }\n  }\n\n  &__inner {\n    margin: 0 auto;\n    max-width: $max-wide-width;\n    position: relative;\n    z-index: 1;\n  }\n\n  &__slogan {\n    @include h1;\n\n    color: $patriotic-red;\n    margin-bottom: 50px;\n  }\n\n  &__copyright {\n    @include small;\n\n    align-self: center;\n    color: $white;\n    justify-self: center;\n    margin-bottom: 10px;\n    text-align: center;\n  }\n\n  &__disclaimer {\n    @include small;\n\n    border: 1px solid $white;\n    color: $white;\n    justify-self: center;\n    padding: px-to-rem(3px) px-to-rem(6px);\n    margin-top: 10px;\n    text-align: center;\n    width: fit-content;\n  }\n\n  &__primary-items {\n    align-items: flex-start;\n    display: flex;\n    flex-flow: row wrap;\n    justify-content: space-between;\n    margin-bottom: 10px;\n    margin-top: 50px;\n\n    &--desktop {\n      display: none;\n    }\n  }\n\n  &__primary-column {\n    width: calc(50% - 25px);\n  }\n\n  &__primary-item {\n    @include body;\n\n    margin-bottom: 20px;\n\n    button {\n      padding: 0;\n      text-align: left;\n    }\n  }\n\n  &__primary-link {\n    background: linear-gradient(to left, $white 50%, $patriotic-red 50%) right;\n    background-position: right 100%;\n    background-repeat: no-repeat;\n    background-size: 200% 1px;\n    color: $white;\n    display: inline-block;\n    transition: background 0.3s ease, color 0.3s ease-in-out,\n      transform 0.3s ease;\n    word-break: break-word;\n\n    &:hover,\n    &:focus {\n      background-position: left 100%;\n      color: $patriotic-red;\n      transform: skewX(-15deg);\n    }\n  }\n\n  &__social-items {\n    display: flex;\n    flex-flow: row wrap;\n    column-gap: 20px;\n  }\n\n  &__social-link {\n    align-items: center;\n    display: flex;\n    justify-content: center;\n    height: 30px;\n    width: 30px;\n\n    svg {\n      color: $white;\n      height: 30px;\n      transition: color 0.3s ease-in;\n      width: 30px;\n    }\n\n    &:hover {\n      svg {\n        color: $patriotic-red;\n      }\n    }\n  }\n\n  /* stylelint-disable */\n  &__form-button {\n    -webkit-appearance: none;\n    -moz-appearance: none;\n    appearance: none;\n    margin-top: 36px;\n    min-height: 60px;\n    width: 100%;\n  }\n  /* stylelint-enable */\n\n  &__form-group {\n    &:not(:first-of-type) {\n      margin-top: 26px;\n    }\n  }\n\n  input[type='email'],\n  input[type='month'],\n  input[type='number'],\n  input[type='password'],\n  input[type='search'],\n  input[type='tel'],\n  input[type='text'],\n  input[type='time'],\n  input[type='url'],\n  input[type='week'] {\n    @include input-small($color: $golden-rod, $active-color: $patriotic-red);\n  }\n\n  &__post-submit:not(.hidden) {\n    p {\n      @include h6;\n    }\n\n    align-items: flex-start;\n    color: $golden-rod;\n    display: flex;\n    flex-direction: column;\n  }\n\n  &__post-submit-icon {\n    height: 45px;\n    margin-bottom: 20px;\n    width: 45px;\n  }\n\n  &--simple {\n    #{$self} {\n      &__inner {\n        max-width: $max-xwide-width;\n      }\n\n      &__primary-items {\n        align-items: flex-start;\n        display: none;\n        margin-bottom: 0;\n        margin-top: 0;\n\n        &--desktop {\n          display: flex;\n        }\n      }\n\n      &__copyright {\n        margin-bottom: 0;\n      }\n\n      &__disclaimer {\n        margin-bottom: 0;\n      }\n\n      &__logo {\n        height: 86px;\n        margin-bottom: 20px;\n        width: 104px;\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    padding: px-to-rem(70px) px-to-rem($tablet-gutter);\n\n    &__background {\n      .spiral,\n      svg {\n        transform: translate(-50%, calc(-50% + 175px));\n      }\n    }\n\n    &__primary-items {\n      flex-wrap: wrap;\n      grid-column-gap: 20px;\n      grid-column: 1 / span 2;\n      margin-bottom: 30px;\n\n      &--desktop {\n        display: flex;\n      }\n\n      &--mobile {\n        display: none;\n      }\n    }\n\n    &__copyright {\n      grid-column: 1 / span 2;\n    }\n\n    &__disclaimer {\n      grid-column: 1 / span 2;\n    }\n\n    &--simple {\n      padding: px-to-rem(45px) px-to-rem($tablet-gutter);\n\n      #{$self} {\n        &__inner {\n          display: grid;\n          grid-column-gap: 50px;\n          grid-template-columns: 104px 1fr;\n          grid-template-rows: repeat(3, auto);\n        }\n\n        &__primary-items {\n          grid-column: 2;\n          grid-row: 1;\n        }\n\n        &__copyright {\n          grid-column: 2;\n          grid-row: 2;\n        }\n\n        &__disclaimer {\n          grid-column: 2;\n          grid-row: 3;\n        }\n\n        &__logo {\n          grid-column: 1;\n          grid-row: 1 / span 3;\n        }\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    padding: px-to-rem(100px) px-to-rem(80px);\n\n    &__background {\n      .spiral,\n      svg {\n        height: unset;\n        transform: translate(calc(-50% + 20vw), calc(-50% + 40px));\n        width: 150vw;\n      }\n    }\n\n    &__inner {\n      display: grid;\n      grid-template-columns: 1fr 320px;\n    }\n\n    &__form {\n      margin-top: 10px;\n    }\n\n    &__form-button {\n      width: fit-content;\n    }\n\n    &__post-submit:not(.hidden) {\n      align-items: center;\n      height: 100%;\n      justify-content: center;\n    }\n\n    &__post-submit-icon {\n      height: 60px;\n      margin-bottom: 30px;\n      width: 60px;\n    }\n\n    &--simple {\n      padding: px-to-rem(100px) px-to-rem($desktop-gutter);\n\n      #{$self} {\n        &__inner {\n          grid-column-gap: 65px;\n        }\n      }\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    padding: px-to-rem(100px);\n\n    &__inner {\n      display: grid;\n      grid-template-columns: 1fr 380px;\n    }\n\n    &__background {\n      .spiral,\n      svg {\n        transform: translate(calc(-52% + 20vw), -48%);\n      }\n    }\n\n    &--simple {\n      padding: px-to-rem(100px) px-to-rem($desktop-gutter);\n    }\n  }\n\n  @include breakpoint(xl-desktop, min) {\n    &__background {\n      .spiral,\n      svg {\n        transform: translate(calc(-52% + 20vw), -49%);\n      }\n    }\n  }\n}\n", "@use 'sass:map';\n\n/**\n  HEADER COMPONENT\n**/\n\n.header {\n  background: $white;\n  border-bottom: 3px solid $electoral-blue;\n  height: 93px;\n  padding: px-to-rem(15px) px-to-rem($mobile-gutter);\n  position: sticky;\n  transition: padding 0.3s ease-in-out;\n  top: 0;\n  z-index: 5;\n\n  $self: &;\n\n  &__content {\n    display: flex;\n    flex-direction: row;\n    justify-content: space-between;\n    align-items: center;\n  }\n\n  &__logo {\n    height: px-to-rem(60px);\n    position: relative;\n    width: px-to-rem(72px);\n    z-index: 3;\n\n    &-link {\n      display: block;\n      height: 100%;\n    }\n\n    svg {\n      height: px-to-rem(60px);\n      transition: all 0.5s ease-in-out;\n      transform: scale(1);\n      width: px-to-rem(72px);\n    }\n\n    .check {\n      fill: $patriotic-red;\n      transition: fill 0.5s ease-in-out;\n    }\n\n    .backdrop {\n      fill: $electoral-blue;\n      transition: fill 0.5s ease-in-out;\n    }\n\n    .letter {\n      fill: $white;\n      transition: fill 0.5s ease-in-out;\n    }\n  }\n\n  &__cta {\n    @include button;\n\n    padding-bottom: 14px;\n    padding-top: 14px;\n    position: relative;\n    min-height: 60px;\n\n    &::after {\n      content: attr(data-label);\n      font-style: italic;\n      visibility: hidden;\n      height: 0;\n      overflow: hidden;\n      display: block;\n    }\n  }\n\n  &__nav-cta-item {\n    margin: px-to-rem(28px) px-to-rem($mobile-gutter) px-to-rem(56px);\n\n    #{$self}__cta {\n      width: 100%;\n    }\n  }\n\n  &__cta-item {\n    margin-left: auto;\n    margin-right: 21px;\n    order: 1;\n  }\n\n  .hamburger {\n    path {\n      transition: all 0.3s ease;\n      transform-origin: center;\n    }\n  }\n\n  .nav {\n    margin-right: 0;\n    margin-left: 0;\n\n    &--hamburger {\n      margin-left: 0;\n      order: 2;\n    }\n\n    &__hamburger {\n      color: $crt-black;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-family: $obviously-font;\n      font-weight: 700;\n      line-height: 1;\n      margin-right: -9px;\n      padding: px-to-rem(18px) px-to-rem(9px);\n      transition: all 0.5s ease-in-out;\n      z-index: 2;\n    }\n\n    &__topwrap {\n      display: flex;\n      flex-direction: row;\n      align-items: flex-start;\n    }\n\n    &__hamburger-label {\n      @include is-visually-hidden;\n    }\n\n    &__item {\n      border-bottom: 1px solid $grey;\n      color: $crt-black;\n      display: block;\n      font-family: $inter-font;\n      font-size: px-to-rem(18px);\n      font-weight: 800;\n      line-height: 140%;\n      position: relative;\n\n      &:last-of-type {\n        margin-bottom: 0;\n      }\n\n      &--sub {\n        border-bottom: 0;\n        overflow: hidden;\n        padding: 0;\n\n        &:first-of-type {\n          .nav__link--sub {\n            padding-top: 0;\n          }\n        }\n\n        &:last-of-type {\n          .nav__link--sub {\n            padding-bottom: px-to-rem(28px);\n          }\n        }\n      }\n    }\n\n    &__expand {\n      cursor: default;\n      padding: 0;\n      z-index: 1;\n\n      svg {\n        display: none;\n        color: $crt-black;\n        transition: all 0.3s ease-in-out;\n      }\n    }\n\n    &__subwrap {\n      display: grid;\n      grid-template-rows: 1fr;\n      transition: grid-template-rows 0.2s ease-in-out;\n\n      &--open {\n        grid-template-rows: 1fr;\n        transition: grid-template-rows 0.2s ease-in-out;\n      }\n    }\n\n    &__subset {\n      width: 100%;\n    }\n\n    &__link {\n      display: block;\n      color: $crt-black;\n      font-style: normal;\n      padding: px-to-rem(28px) px-to-rem($mobile-gutter);\n      position: relative;\n      transition: all 0.5s cubic-bezier(0.3333, 0.6667, 0.6667, 1);\n      width: 100%;\n\n      .nav__text {\n        display: flex;\n        flex-direction: row;\n        align-items: center;\n        justify-content: space-between;\n\n        svg {\n          display: block;\n          transition: transform 0.3s ease-in;\n        }\n      }\n\n      &--sub {\n        color: $crt-black;\n        font-weight: 400;\n        padding: px-to-rem(14px) px-to-rem($mobile-gutter);\n      }\n\n      &:hover {\n        color: $electoral-blue;\n\n        .nav__text {\n          svg {\n            transform: translateX(5px);\n          }\n        }\n      }\n\n      &--button {\n        &:hover {\n          color: $crt-black;\n        }\n      }\n    }\n\n    &__set-wrap {\n      display: grid;\n      grid-template-rows: 0fr;\n      transition: visibility 0s linear 0.5s, grid-template-rows 0.5s ease-in;\n      filter: drop-shadow(6px 6px 3px rgb(50 50 0 / 50%));\n      left: 0;\n      position: absolute;\n      right: 0;\n      top: 93px;\n      width: 100%;\n      visibility: hidden;\n    }\n\n    &__set {\n      background-color: $white;\n      max-height: calc(100vh - 93px);\n      padding: 0;\n      overflow: auto;\n      transition: transform 0.5s ease-in, width 0.1s linear,\n        overflow 0s linear 0.5s;\n      transform-origin: 100% 0%;\n      width: 100%;\n    }\n  }\n\n  &--open {\n    #{$self} {\n      &__cta-item {\n        display: none;\n      }\n\n      &__logo {\n        svg {\n          transform: scale(1.1) translate(0, 0);\n        }\n      }\n    }\n\n    .hamburger {\n      path {\n        &.top {\n          transform: translate(-4px, 7px) rotate(45deg);\n        }\n\n        &.middle {\n          opacity: 0;\n        }\n\n        &.bottom {\n          transform: translate(-4px, -6px) rotate(-45deg);\n        }\n      }\n    }\n\n    .nav {\n      &--hamburger {\n        margin-right: 0;\n      }\n\n      &__set-wrap {\n        transition: visibility 0s linear 0s, grid-template-rows 0.5s ease-in;\n        visibility: visible;\n        grid-template-rows: 1fr;\n      }\n    }\n  }\n\n  @include breakpoint(nav-tablet, min) {\n    .nav {\n      &__set-wrap {\n        filter: drop-shadow(-6px 6px 3px rgb(50 50 0 / 50%));\n        width: 100%;\n      }\n\n      &__set {\n        padding: 0;\n        max-width: 100vw;\n        max-height: calc(100vh - 93px);\n      }\n\n      &__hamburger {\n        font-size: 18px;\n        margin-right: px-to-rem(-4px);\n        margin-left: 0;\n        position: relative;\n        transition: all 0.3s ease-out;\n\n        svg {\n          height: 22px;\n          width: 24px;\n        }\n      }\n    }\n\n    &__nav-cta-item {\n      display: none;\n    }\n\n    &--open {\n      #{$self}__cta-item {\n        display: block;\n      }\n\n      #{$self}__nav-cta-item {\n        display: none;\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    padding: 15px $tablet-gutter;\n\n    &__subset {\n      margin-bottom: 0;\n    }\n\n    .nav {\n      &__subset {\n        margin-bottom: 0;\n      }\n\n      &__set {\n        margin-bottom: 0;\n      }\n\n      &__link {\n        padding-left: px-to-rem($tablet-gutter);\n        padding-right: px-to-rem($tablet-gutter);\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    &__subset {\n      margin-bottom: 0;\n    }\n\n    &__content {\n      max-width: $max-wide-width;\n      margin-left: auto;\n      margin-right: auto;\n    }\n\n    .nav {\n      &__subset {\n        margin-bottom: 0;\n      }\n\n      &__set {\n        margin-bottom: 0;\n      }\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    height: fit-content;\n\n    &__content {\n      flex-direction: row;\n      justify-content: center;\n      align-items: center;\n    }\n\n    &__logo {\n      align-self: flex-end;\n      transition: all 0.5s ease-in-out;\n\n      &-link {\n        transform: scale(1.7) translate(15px, 15px);\n        transition: all 0.5s ease-in-out;\n      }\n\n      svg,\n      svg.open {\n        transform: scale(1) translate(0);\n      }\n    }\n\n    &__cta-item {\n      display: none;\n    }\n\n    &__nav-cta-item {\n      display: block;\n      align-self: center;\n      margin: 0 0 0 px-to-rem(25px);\n    }\n\n    .nav {\n      &--hamburger {\n        margin-left: auto;\n        margin-right: 0;\n        max-width: 100%;\n        padding-left: px-to-rem(60px);\n      }\n\n      &__hamburger {\n        display: none;\n      }\n\n      &__item {\n        align-items: center;\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        border-bottom: none;\n\n        &--sub {\n          .nav__link--sub {\n            padding-bottom: px-to-rem(14px);\n            padding-top: px-to-rem(14px);\n          }\n\n          &:first-of-type {\n            .nav__link--sub {\n              padding-top: px-to-rem(26px);\n            }\n          }\n\n          &:last-of-type {\n            .nav__link--sub {\n              padding-bottom: px-to-rem(26px);\n            }\n          }\n        }\n      }\n\n      &__link {\n        padding: px-to-rem(23px) px-to-rem(25px);\n        transition: color 0.3s ease-in-out;\n\n        &--button {\n          padding-right: px-to-rem(6px);\n        }\n\n        &--sub {\n          font-weight: 800;\n        }\n\n        .nav__text {\n          svg {\n            display: none;\n          }\n        }\n\n        &:hover {\n          color: $electoral-blue;\n        }\n      }\n\n      &__expand {\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        padding-right: px-to-rem(25px);\n\n        svg {\n          transform-origin: center center;\n          transform: rotate(0deg);\n          display: block;\n        }\n\n        &:hover {\n          svg {\n            color: $electoral-blue;\n          }\n        }\n\n        &--open {\n          svg {\n            color: $electoral-blue;\n            transform: rotate(-180deg);\n          }\n\n          .nav__link {\n            color: $electoral-blue;\n          }\n        }\n      }\n\n      &__set-wrap {\n        filter: none;\n        inset: unset;\n        transform: none;\n        transition: none;\n        position: relative;\n        visibility: visible;\n      }\n\n      &__set {\n        display: flex;\n        flex-flow: row nowrap;\n        align-items: stretch;\n        justify-content: center;\n        max-height: 100%;\n        max-width: 100%;\n        overflow: visible;\n        transform: none;\n        transition: none;\n      }\n\n      &__subwrap {\n        display: grid;\n        grid-template-rows: 0fr;\n        transition: grid-template-rows 0.3s ease-in;\n\n        &--open {\n          grid-template-rows: 1fr;\n          transition: grid-template-rows 0.3s ease-in;\n        }\n      }\n\n      &__subset {\n        background-color: $white;\n        border-top: 6px solid $electoral-blue;\n        transform: scale(1, 0) translate(-50%, 0%);\n        transform-origin: center top;\n        visibility: hidden;\n        margin-bottom: 0;\n        opacity: 0;\n        overflow: hidden;\n        position: absolute;\n        transition: visibility 0s linear 0.3s, opacity 0s linear 0.3s,\n          transform 0.3s ease-in 0s, width 0.1s linear 0s;\n        top: calc(100% + 12px);\n        min-width: 180px;\n        max-width: 220px;\n        width: max-content;\n\n        &--open {\n          filter: drop-shadow(-6px 6px 6px rgb(50 50 0 / 25%));\n          grid-template-rows: 1fr;\n          transition: visibility 0s linear, opacity 0s linear,\n            transform 0.3s ease-in 0.1s, width 0.1s linear 0s;\n          opacity: 1;\n          transform: scale(1, 1) translate(-50%, 0%);\n          visibility: visible;\n        }\n      }\n    }\n\n    &--open {\n      .header {\n        &__cta-item {\n          display: none;\n        }\n\n        &__nav-cta-item {\n          display: block;\n        }\n      }\n\n      .nav {\n        &__set-wrap {\n          transform: none;\n          transition: none;\n        }\n\n        &__set {\n          transform: none;\n          transition: none;\n        }\n      }\n    }\n\n    &--pinned {\n      padding-top: 5px;\n      padding-bottom: 5px;\n\n      #{$self}__logo {\n        align-self: center;\n\n        &-link,\n        svg,\n        svg.open {\n          transform: scale(1) translate(0, 0);\n        }\n      }\n\n      .nav {\n        &__hamburger {\n          transform: none;\n        }\n\n        &__subset {\n          transition: visibility 0s linear 0.3s, opacity 0s linear 0.3s,\n            transform 0.3s ease-in 0s, width 0.1s linear 0s, top 0.3s ease-in 0s;\n          top: calc(100% + 2px);\n\n          &--open {\n            transition: visibility 0s linear, opacity 0s linear,\n              transform 0.3s ease-in 0.1s, width 0.1s linear 0s,\n              top 0.3s ease-in 0s;\n          }\n        }\n      }\n    }\n  }\n}\n\n.disable-animation {\n  .header {\n    .nav__set-wrap {\n      transition: none;\n    }\n  }\n}\n\n.admin-bar {\n  .header {\n    .nav {\n      &__set {\n        top: 60px;\n      }\n    }\n\n    @include breakpoint(tablet, min) {\n      .nav {\n        &__set {\n          top: 110px;\n        }\n      }\n    }\n  }\n}\n", ".geoalert {\n  background-color: var(--bg-color);\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  justify-content: center;\n  padding: 26px $mobile-gutter;\n  position: relative;\n  z-index: 1;\n\n  &--hidden {\n    display: none;\n  }\n\n  &__para {\n    @include caption;\n\n    color: var(--text-color);\n    margin-bottom: 0;\n  }\n\n  &__link {\n    margin-top: 18px;\n  }\n\n  @include breakpoint(tablet, min) {\n    flex-direction: row;\n    align-items: center;\n    justify-content: center;\n    padding: 16px;\n\n    &__para {\n      margin-bottom: 0;\n    }\n\n    &__link {\n      margin-left: 18px;\n      margin-top: 0;\n    }\n  }\n}\n\n.nav-open {\n  .geoalert {\n    display: none;\n  }\n}\n", ".meerkat {\n  bottom: 70px;\n  box-shadow: 0 -6px 32px rgb(0 0 0 / 24%);\n  height: auto;\n  position: fixed;\n  right: 0;\n  transform: translateX(300px);\n  transition: all 0.3s ease-in-out;\n  width: 143px;\n  z-index: 4;\n\n  $self: &;\n\n  &__wrapper {\n    background-color: $golden-rod;\n    bottom: 0;\n    clip-path: $pixel-tips-left-desktop;\n    left: -12px;\n    overflow: visible;\n    position: relative;\n    right: 0;\n    transition: all 0.3s ease-in-out 0.01s, visibility 0s;\n    transform-origin: bottom right;\n    width: 155px;\n    z-index: 4;\n  }\n\n  &__icon {\n    display: block;\n    height: 125px;\n    top: -20px;\n    right: 15px;\n    position: absolute;\n    transition: all 0.3s ease-in-out;\n    width: 125px;\n  }\n\n  &__open {\n    @include h6;\n\n    color: $crt-black;\n    height: auto;\n    padding: 80px 10px 23px;\n    position: relative;\n    top: 0;\n    width: 155px;\n    z-index: 5;\n  }\n\n  &__close {\n    position: absolute;\n    right: $mobile-gutter;\n    top: 26px;\n  }\n\n  &__close-icon {\n    color: $electoral-blue;\n    height: 24px;\n    width: 24px;\n  }\n\n  &__dialog {\n    background-color: $golden-rod;\n    clip-path: $pixel-tips-left-desktop;\n    display: none;\n    padding: $mobile-gutter;\n    transition: all 0.3s ease-in-out 0.01s, visibility 0s;\n    transform: scale(0.2);\n    transform-origin: bottom right;\n    width: 330px;\n\n    p {\n      margin-bottom: 0;\n    }\n  }\n\n  &__actionnetwork {\n    align-items: flex-end;\n    display: grid;\n    grid-template-columns: 100%;\n    grid-column-gap: 40px;\n\n    &.hidden {\n      display: none;\n    }\n\n    .input-group__error-msg {\n      font-size: px-to-rem(12px);\n      margin-bottom: 0;\n      margin-top: 16px;\n\n      &::after {\n        background-size: 48px 33px;\n        height: 33px;\n        right: 0;\n        top: 18px;\n        width: 48px;\n      }\n    }\n  }\n\n  &__form-group {\n    grid-column: 1;\n\n    &.input-group--zip {\n      grid-column: 1;\n    }\n  }\n\n  &__form-button {\n    grid-column: 1;\n    height: fit-content;\n    position: relative;\n    margin-top: 23px;\n  }\n\n  &__post-submit {\n    @include h6;\n\n    color: $electoral-blue;\n    margin-top: 12px;\n\n    p {\n      @include h6;\n    }\n  }\n\n  &--show {\n    transform: translateX(0) skewY(0);\n    animation: eyes 3s linear 1;\n    animation-fill-mode: forwards;\n\n    #{$self} {\n      &__icon {\n        animation: eye 3s linear 1;\n        animation-fill-mode: forwards;\n      }\n    }\n\n    &:hover,\n    &:focus {\n      box-shadow: 0 -6px 32px rgb(0 0 0 / 50%);\n    }\n  }\n\n  &--open {\n    height: auto;\n    animation: open 0.3s linear 1;\n    animation-fill-mode: forwards;\n    z-index: 6;\n    width: 318px;\n\n    #{$self} {\n      &__wrapper {\n        bottom: 0;\n        width: 330px;\n      }\n\n      &__icon {\n        animation: none;\n        height: 175px;\n        left: -24px;\n        width: 175px;\n        z-index: 6;\n      }\n\n      &__open {\n        display: none;\n      }\n\n      &__dialog {\n        display: block;\n        margin-top: -93px;\n        max-height: calc(100vh - 155px);\n        overflow: auto;\n        padding-top: 105px;\n        transition: all 0.3s ease-in-out 0s, visibility 0s ease-in-out 0.3s;\n        transform: scale(1);\n      }\n    }\n  }\n\n  &--close {\n    animation: none;\n\n    #{$self} {\n      &__icon {\n        animation: none;\n      }\n    }\n  }\n\n  @keyframes open {\n    0% {\n      transform: scaleY(0.33);\n      transform-origin: bottom right;\n      display: block;\n    }\n\n    100% {\n      transform: scale(1);\n      transform-origin: bottom right;\n    }\n  }\n\n  @keyframes eyes {\n    0% {\n      transform: translateX(300px);\n    }\n\n    40% {\n      transform: translateX(300px);\n    }\n\n    45% {\n      transform: translateX(100px);\n    }\n\n    55% {\n      transform: translateX(100px);\n    }\n\n    75% {\n      transform: translateX(100px);\n    }\n\n    80% {\n      transform: translateX(0);\n    }\n\n    100% {\n      transform: translateY(0);\n    }\n  }\n\n  @keyframes eye {\n    0% {\n      transform: translateX(0);\n    }\n\n    40% {\n      transform: translateX(0);\n    }\n\n    45% {\n      transform: translateX(0) skew(0deg);\n    }\n\n    50% {\n      transform: translateX(-30px) skew(5deg);\n    }\n\n    55% {\n      transform: translateX(-40px) skew(8deg);\n    }\n\n    65% {\n      transform: translateX(-40px) skew(9deg);\n    }\n\n    75% {\n      transform: translateX(-40px) skew(10deg);\n    }\n\n    80% {\n      transform: translateX(0) skew(0deg);\n    }\n\n    100% {\n      transform: translateY(0);\n    }\n  }\n\n  @include breakpoint(l-mobile, min) {\n    &__dialog {\n      padding: 22px 30px 33px;\n      width: 475px;\n    }\n\n    &__actionnetwork {\n      grid-template-columns: 213px 172px;\n    }\n\n    &__form-group {\n      grid-column: 1 / span 2;\n\n      &.input-group--zip {\n        grid-column: 1 / span 1;\n      }\n    }\n\n    &__form-button {\n      bottom: 12px;\n      grid-column: 2 / span 1;\n      margin-top: unset;\n    }\n\n    &__post-submit {\n      margin-top: 22px;\n    }\n\n    &--open {\n      width: 463px;\n\n      #{$self} {\n        &__wrapper {\n          bottom: 0;\n          width: 475px;\n        }\n\n        &__dialog {\n          margin-top: -88px;\n          padding-top: 110px;\n        }\n\n        &__icon {\n          left: -15px;\n        }\n      }\n    }\n  }\n\n  @include breakpoint(nav-desktop, min) {\n    &__actionnetwork {\n      .input-group__error-msg {\n        font-size: px-to-rem(14px);\n      }\n    }\n\n    &--open {\n      #{$self} {\n        &__dialog {\n          max-height: calc(100vh - 70px);\n        }\n      }\n    }\n  }\n}\n", ".popup {\n  display: none;\n  inset: 0;\n  min-height: 100vh;\n  overflow: auto;\n  padding: $mobile-gutter;\n  position: fixed;\n  z-index: $popup-z-index;\n  width: 100%;\n\n  &.show {\n    display: block;\n  }\n\n  &__icon {\n    height: 80px;\n    margin-bottom: 30px;\n    width: 120px;\n\n    &--vsalogo {\n      height: 140px;\n      margin-bottom: 0;\n      margin-top: -20px;\n      width: 140px;\n    }\n\n    &--vsalogomap {\n      height: 112px;\n      width: 180px;\n    }\n\n    &--handwritten {\n      color: $golden-rod;\n      height: 57px;\n      width: 65px;\n    }\n  }\n\n  &__wrapper {\n    align-items: center;\n    background-color: var(--background-color);\n    clip-path: $pixel-tips-desktop;\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    margin: 0;\n    min-height: calc(100vh - $mobile-gutter - $mobile-gutter);\n    padding: 55px $mobile-gutter 75px;\n    position: relative;\n    width: 100%;\n  }\n\n  &__content {\n    padding: 26px $mobile-gutter 76px;\n    width: 100%;\n  }\n\n  &__title {\n    color: var(--title-color);\n    margin-bottom: 30px;\n    max-width: $max-title-width;\n    text-align: center;\n\n    &--h1 {\n      @include h1;\n    }\n\n    &--hcom {\n      @include community-stats;\n    }\n\n    &--h2 {\n      @include h2;\n    }\n\n    &--h3 {\n      @include h3;\n    }\n\n    &--h4 {\n      @include h4;\n    }\n  }\n\n  &__intro {\n    color: var(--intro-color);\n    margin-bottom: 30px;\n    max-width: $max-text-width;\n    text-align: center;\n\n    br {\n      display: block;\n      content: '';\n      margin-bottom: 18px;\n    }\n  }\n\n  &__close {\n    align-items: center;\n    display: flex;\n    height: 36px;\n    justify-content: center;\n    padding: 10px;\n    position: absolute;\n    right: 12px;\n    top: 12px;\n    width: 44px;\n    z-index: 1;\n\n    &:hover,\n    &:focus {\n      .popup__close-icon {\n        color: $patriotic-red-dark;\n      }\n    }\n  }\n\n  &__close-icon {\n    color: var(--intro-color);\n    height: 26px;\n    pointer-events: none;\n    transition: color 0.3s ease-in-out;\n    width: 26px;\n\n    path {\n      stroke-width: 6px;\n    }\n  }\n\n  &__asks {\n    display: grid;\n    grid-template-columns: calc(50% - 6px) calc(50% - 6px);\n    grid-gap: 12px;\n    max-width: $max-text-width;\n  }\n\n  &__button {\n    @include button(\n      $color: var(--button-background-color),\n      $hover: var(--button-hover-background-color),\n      $text-color: var(--button-text-color)\n    );\n\n    width: 100%;\n  }\n\n  &__disclaimer {\n    @include caption;\n\n    color: var(--intro-color);\n    font-style: italic;\n    grid-column: 1 / span 2;\n    text-align: center;\n    margin-top: 5px;\n  }\n\n  &__form {\n    max-width: $max-wide-width;\n    width: 100%;\n\n    .input-group__submit {\n      margin-bottom: 10px;\n      margin-top: 40px;\n      width: 100%;\n    }\n\n    .input-group {\n      &.error {\n        .input-group__error-msg {\n          margin-bottom: 0;\n          margin-top: 22px;\n\n          &::after {\n            top: 16px;\n          }\n        }\n      }\n    }\n  }\n\n  &__actionnetwork {\n    &.hidden {\n      display: none;\n    }\n  }\n\n  &__post-submit {\n    color: var(--intro-color);\n    text-align: center;\n  }\n\n  &__ballotreadyinput {\n    margin: 0 auto;\n    max-width: calc($max-text-width - 20px);\n    width: 100%;\n  }\n\n  &--image {\n    .popup {\n      &__wrapper {\n        padding: 0;\n      }\n\n      &__picture {\n        position: relative;\n        width: 100%;\n\n        &--desktop {\n          display: none;\n        }\n      }\n\n      &__image {\n        &--desktop {\n          display: none;\n        }\n      }\n\n      &__title {\n        text-align: left;\n      }\n\n      &__intro {\n        text-align: left;\n      }\n\n      &__form {\n        margin-top: -30px;\n      }\n\n      &__post-submit {\n        text-align: left;\n      }\n    }\n  }\n\n  @include breakpoint(m-mobile, min) {\n    &__cta {\n      width: fit-content;\n    }\n  }\n\n  @include breakpoint(wp-admin, min) {\n    padding: $tablet-gutter;\n\n    &__wrapper {\n      min-height: calc(100vh - $tablet-gutter - $tablet-gutter);\n      padding: 55px $tablet-gutter 50px;\n    }\n\n    &__content {\n      padding: 55px $tablet-gutter 50px;\n    }\n\n    &__icon {\n      margin-bottom: 40px;\n\n      &--vsalogo {\n        height: 180px;\n        margin-bottom: 0;\n        margin-top: -50px;\n        width: 180px;\n      }\n\n      &--vsalogomap {\n        height: 170px;\n        width: 270px;\n      }\n\n      &--handwritten {\n        height: 83px;\n        width: 95px;\n      }\n    }\n\n    &__title {\n      margin-bottom: 40px;\n    }\n\n    &__intro {\n      margin-bottom: 40px;\n\n      br {\n        margin-bottom: 20px;\n      }\n    }\n\n    &__disclaimer {\n      margin-left: calc($tablet-gutter / -2);\n      width: calc(100% + $tablet-gutter);\n    }\n\n    &__form {\n      .input-group__submit {\n        margin-top: 40px;\n        width: auto;\n      }\n    }\n\n    &--image {\n      .popup {\n        &__wrapper {\n          padding: 0;\n        }\n\n        &__picture {\n          width: 100%;\n        }\n\n        &__image {\n          height: 375px;\n          object-fit: cover;\n          width: 100%;\n        }\n\n        &__icon {\n          margin-bottom: 30px;\n\n          &--vsalogo {\n            margin-bottom: 0;\n          }\n        }\n\n        &__title {\n          margin-bottom: 30px;\n        }\n\n        &__intro {\n          margin-bottom: 30px;\n        }\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    padding: $desktop-gutter;\n\n    &__wrapper {\n      min-height: calc(100vh - $desktop-gutter - $desktop-gutter);\n      padding: 55px 70px;\n    }\n\n    &__icon {\n      margin-bottom: 50px;\n\n      &--vsalogo {\n        margin-bottom: 0;\n      }\n    }\n\n    &__title {\n      margin-bottom: 50px;\n    }\n\n    &__intro {\n      margin-bottom: 50px;\n\n      br {\n        margin-bottom: 22px;\n      }\n    }\n\n    &__asks {\n      grid-template-columns: calc((100% - 24px) / 3) calc((100% - 24px) / 3) calc(\n          (100% - 24px) / 3\n        );\n    }\n\n    &__disclaimer {\n      grid-column: 1 / span 3;\n      margin-top: 5px;\n      margin-left: -25px;\n      width: calc(100% + 50px);\n    }\n\n    &__actionnetwork {\n      align-items: flex-end;\n      display: grid;\n      grid-gap: 40px;\n      grid-template-columns: calc((100% - 270px) / 2) calc((100% - 270px) / 2) 190px;\n\n      &.hidden {\n        display: none;\n      }\n    }\n\n    &__form-button {\n      grid-column: 3;\n    }\n\n    &__form {\n      .input-group {\n        &.error {\n          .input-group__error-msg {\n            margin-bottom: 0;\n            margin-top: 22px;\n\n            &::after {\n              background-size: 48px 33px;\n              height: 33px;\n              right: 0;\n              top: 16px;\n              width: 48px;\n            }\n          }\n        }\n      }\n\n      .form__form-disclaimer {\n        margin-top: -20px;\n      }\n\n      &--3 {\n        .popup__actionnetwork {\n          grid-template-columns: calc((100% - 40px) / 2) calc((100% - 40px) / 2);\n        }\n\n        .input-group--phone {\n          grid-column: 1;\n          grid-row: 2;\n        }\n\n        .popup__form-disclaimer {\n          grid-row: 3;\n        }\n\n        .input-group__submit {\n          grid-column: 2;\n          grid-row: 2;\n        }\n      }\n\n      &--4 {\n        .input-group--zip {\n          grid-column: 2;\n        }\n\n        .input-group--phone {\n          grid-column: 1;\n          grid-row: 2;\n        }\n\n        .input-group__submit {\n          grid-row: 1 / span 2;\n        }\n\n        .popup__form-disclaimer {\n          width: 200%;\n        }\n      }\n\n      &--5 {\n        .input-group__submit {\n          grid-row: 3;\n        }\n\n        .input-group--email {\n          grid-column: 1;\n        }\n\n        .input-group--zip {\n          grid-column: 2;\n        }\n\n        .input-group--phone {\n          grid-column: 1 / span 2;\n          grid-row: 3;\n        }\n\n        .form__form-disclaimer {\n          grid-column: 1 / span 2;\n          grid-row: 4;\n        }\n      }\n    }\n\n    &__content {\n      padding: 70px 60px;\n    }\n\n    &--image {\n      .popup {\n        &__wrapper {\n          display: grid;\n          grid-template-columns: 40% 60%;\n          grid-gap: 0;\n          padding: 0;\n        }\n\n        &__icon {\n          margin-bottom: 30px;\n        }\n\n        &__title {\n          margin-bottom: 30px;\n        }\n\n        &__intro {\n          margin-bottom: 30px;\n        }\n\n        &__actionnetwork {\n          align-items: flex-start;\n          display: flex;\n          flex-direction: column;\n          grid-gap: 0;\n          max-width: 625px;\n\n          &.hidden {\n            display: none;\n          }\n        }\n\n        &__form-group {\n          width: 100%;\n        }\n\n        &__picture {\n          &--mobile {\n            display: none;\n          }\n\n          &--desktop {\n            display: block;\n            height: 100%;\n          }\n        }\n\n        &__image {\n          &--mobile {\n            display: none;\n          }\n\n          &--desktop {\n            display: block;\n            height: 100%;\n          }\n        }\n      }\n\n      .form__form-disclaimer {\n        margin-top: 22px;\n      }\n    }\n  }\n\n  @include breakpoint(l-desktop, min) {\n    &__wrapper {\n      padding: 55px 80px;\n    }\n\n    &--image {\n      .popup {\n        &__wrapper {\n          padding: 0;\n        }\n\n        &__content {\n          padding: 65px;\n          margin-left: 0;\n          margin-right: auto;\n          max-width: $max-title-width;\n        }\n      }\n    }\n  }\n\n  @include breakpoint(xl-desktop, min) {\n    &__wrapper {\n      margin-left: auto;\n      margin-right: auto;\n      max-width: $max-xwide-width;\n    }\n\n    &--image {\n      .popup {\n        &__wrapper {\n          grid-template-columns: 550px calc(100% - 550px);\n          padding: 0;\n        }\n\n        &__content {\n          padding: 70px;\n        }\n      }\n    }\n  }\n}\n", ".page-template-default,\n.form-template-default,\n.resource-template-default {\n  &:has(.programhero) {\n    overflow-x: hidden;\n  }\n\n  .content-wrapper {\n    padding-left: $mobile-gutter;\n    padding-right: $mobile-gutter;\n\n    .article-body {\n      .votehero,\n      .generichero {\n        + script + .statevotinginformation,\n        + script + .usvotestatevotinginformation {\n          margin-top: -50px;\n        }\n      }\n\n      & > *:not(.wide, .alignwide, .full, .alignfull, .title-wide) {\n        max-width: $max-text-width;\n        margin-left: auto;\n        margin-right: auto;\n      }\n\n      .alignleft,\n      .left,\n      .alignright,\n      .right {\n        float: none;\n      }\n\n      & > .title-wide {\n        max-width: $max-title-width;\n        margin-left: auto;\n        margin-right: auto;\n      }\n\n      & > .wide,\n      & > .alignwide {\n        max-width: $max-wide-width;\n        margin-left: auto;\n        margin-right: auto;\n      }\n\n      & > .full,\n      & > .alignfull {\n        max-width: calc(100% + $mobile-gutter + $mobile-gutter);\n        margin-left: -$mobile-gutter;\n        width: calc(100% + $mobile-gutter + $mobile-gutter);\n      }\n\n      &\n        > :last-child:not(\n          .homepagemidsection,\n          .donationcta,\n          .donationsection,\n          .actionnetworkform,\n          .tickertape,\n          .homepageform,\n          .homepagecd\n        ) {\n        margin-bottom: 60px;\n      }\n    }\n  }\n\n  @include breakpoint(tablet, min) {\n    .content-wrapper {\n      padding-left: $tablet-gutter;\n      padding-right: $tablet-gutter;\n\n      .article-body {\n        & > .wide,\n        & > .alignwide {\n          margin-left: calc($tablet-gutter / -2);\n          width: calc(100% + $tablet-gutter);\n        }\n\n        & > .full,\n        & > .alignfull {\n          max-width: calc(100% + $tablet-gutter + $tablet-gutter);\n          margin-left: -$tablet-gutter;\n          width: calc(100% + $tablet-gutter + $tablet-gutter);\n        }\n\n        &\n          > :last-child:not(\n            .homepagemidsection,\n            .donationcta,\n            .donationsection,\n            .signup,\n            .actionnetworkform,\n            .tickertape,\n            .homepageform,\n            .homepagecd\n          ) {\n          margin-bottom: 80px;\n        }\n\n        .votehero,\n        .generichero {\n          + script + .statevotinginformation,\n          + script + .usvotestatevotinginformation {\n            margin-top: -60px;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(desktop, min) {\n    .content-wrapper {\n      padding-left: $desktop-gutter;\n      padding-right: $desktop-gutter;\n\n      .article-body {\n        & > .full,\n        & > .alignfull {\n          max-width: calc(100% + $desktop-gutter + $desktop-gutter);\n          margin-left: -$desktop-gutter;\n          width: calc(100% + $desktop-gutter + $desktop-gutter);\n        }\n\n        & > .wide,\n        & > .alignwide {\n          margin-left: calc($desktop-gutter / -2);\n          width: calc(100% + $desktop-gutter);\n        }\n\n        &\n          > :last-child:not(\n            .homepagemidsection,\n            .donationcta,\n            .donationsection,\n            .signup,\n            .actionnetworkform,\n            .tickertape,\n            .homepageform,\n            .homepagecd\n          ) {\n          margin-bottom: 100px;\n        }\n\n        .votehero,\n        .generichero {\n          + script + .statevotinginformation,\n          + script + .usvotestatevotinginformation {\n            margin-top: -70px;\n          }\n        }\n      }\n    }\n  }\n\n  @include breakpoint(wide-desktop, min) {\n    .content-wrapper {\n      .article-body {\n        & > .wide,\n        & > .alignwide {\n          margin-left: auto;\n          margin-right: auto;\n          width: 100%;\n        }\n      }\n    }\n  }\n\n  @include breakpoint(xl-desktop, min) {\n    .content-wrapper {\n      .article-body {\n        .votehero,\n        .generichero {\n          + script + .statevotinginformation,\n          + script + .usvotestatevotinginformation {\n            margin-top: -80px;\n          }\n        }\n      }\n    }\n  }\n}\n"], "names": [], "sourceRoot": ""}